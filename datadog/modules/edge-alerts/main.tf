resource "datadog_monitor" "disk_usage" {
  name               = "Primary volume is almost full"
  type               = "metric alert"
  message            = <<EOT
{{#is_alert}} System disk usage is too high! {{/is_alert}}
{{^is_alert}} System disk usage is approaching full. Please investigate! {{/is_alert}}

Host: {{host.name}}
Customer: {{host.customer}}
Role: {{host.role}}
Notify: @slack-team-field-eng-alert-${var.environment}
EOT
  escalation_message = "Escalation message @pagerduty"

  query = "avg(last_5m):avg:system.disk.in_use{device_name:ubuntu--vg-ubuntu--lv,env:${var.environment}} by {host} > 0.95"

  monitor_thresholds {
    warning  = 0.9
    critical = 0.95
  }

  include_tags = true

  tags = concat([
    "environment:${var.environment}",
    "team:field-eng"
  ], var.extra_tags)

  priority = var.priority
}

resource "datadog_monitor" "host_down" {
  name    = "Host is down or unreachable (${var.environment})"
  type    = "service check"
  message = <<EOT
${var.environment} host {{host.name}} has been missing for more than 2 minutes

* Host: {{host.name}}
* Customer: {{host.customer}}
* Role: {{host.role}}
Notify: @slack-team-field-eng-alert-${var.environment}
EOT

  query = "\"datadog.agent.up\".over(\"env:${var.environment}\").by(\"host\").last(2).count_by_status()"

  include_tags = true

  new_group_delay = 240
  notify_no_data  = true

  tags = concat([
    "environment:${var.environment}",
    "team:field-eng"
  ], var.extra_tags)

  priority = var.priority
}
