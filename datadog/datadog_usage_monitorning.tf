resource "datadog_monitor" "ingested_events" {
  name    = "Alert for number of events ingested to Datadog"
  type    = "metric alert"
  message = "A high volume of events have been ingested to Datadog in the past hour. @slack-team-platform-services-alert-prod"

  query = "sum(${var.logs_ingestion_1h_evaluation_period}):sum:datadog.estimated_usage.logs.ingested_events{*}.as_count() > 30000000.0"

  monitor_thresholds {
    warning  = 15000000.0
    critical = 30000000.0
  }

  include_tags   = true
  notify_no_data = true

  tags = concat([
    "team:platform-services"
  ], var.extra_tags)

  priority = var.priority
}
