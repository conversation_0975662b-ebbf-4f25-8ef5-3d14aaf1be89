
resource "tailscale_acl" "acl" {
  acl = jsonencode(
    // Example/default ACLs for unrestricted connections.
    {
      // Declare static groups of users beyond those in the identity service.
      "groups" : {
        "group:platform-services" : [
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
          "<EMAIL>",
        ],
        "group:ops" : [
          "<EMAIL>",
          "<EMAIL>",
        ],
        "group:cobalt" : [
          "<EMAIL>",
          "<EMAIL>",
        ],
      },
      // Declare convenient hostname aliases to use in place of IP addresses.
      "hosts" : {
        "example-host-1" : "***************",

        // we need to explicitly declare dst access to these resources for members
        // https://tailscale.com/kb/1192/acl-samples/#vpc-access-vpc-peering
        "dev-gcp-network-1" : "***********/22",
        "dev-gcp-network-2" : "***********/28",
        "staging-gcp-network-1" : "***********/22",
        "staging-gcp-network-2" : "************/28",
        "prod-gcp-network-1" : "***********/22",
        "prod-gcp-network-2" : "***********/28",

        "apella-camera-network" : "************/24",
        // third party networks
        "dev-confluent-network" : "***********/16",
        // customer networks
        // We don't own every device in the range, but for brevity, we've chosen /24 summary ranges
        "hf-vh-network" : "************/24",

        "hmh-hmcl-ld-network" : "************/24",

        "hmh-hmcl-main-network" : "************/24",

        "hmh-hmcl-asc-network" : "************/24",

        "hmh-dunn03-network" : "10.20.31.0/24",

        "hmh-main03-network-1" : "10.50.24.0/24",

        "hmh-main03-network-2" : "10.50.25.0/24",

        "hmh-dunn06-network" : "10.20.109.0/24",

        "hmh-opc18-network-1" : "10.112.144.0/24",

        "hmh-opc18-network-2" : "10.112.222.0/24",

        "hmh-opc19-network" : "10.112.151.0/24",

        "hmh-wt03-network" : "10.65.31.0/24",

        "hmh-hmtw-network-1" : "10.174.26.0/24",

        "hmh-hmtw-network-2" : "10.170.27.0/24",

        "hmh-hmtw-network-3" : "10.174.25.0/24",

        "hmh-hmtw-network-4" : "10.170.70.0/24",

        "hmh-hmtw-network-5" : "10.170.25.0/24",

        "hmh-hmbt-network-1" : "10.150.175.0/24",

        "hmh-hmbt-network-2" : "10.152.133.0/24",

        "hmh-hmbt-network-3" : "10.150.82.0/24",

        "hmh-hmbt-network-4" : "10.152.134.0/24",

        "hmh-hmw-network-1" : "10.176.19.0/24",

        "hmh-hmw-network-2" : "10.176.20.0/24",

        "hmh-hmw-network-3" : "10.176.57.0/24",

        "hmh-hmw-network-4" : "10.176.58.0/24",

        "hmh-hmw-network-5" : "10.176.207.0/24",

        "hmh-hmsl-network-1" : "10.161.19.0/24",

        "hmh-hmsl-network-2" : "10.161.23.0/24",

        "hmh-hmsl-network-3" : "10.161.52.0/24",

        "hmh-hmsl-network-4" : "10.161.53.0/24",

        "hmh-hmsl-ld-network" : "10.160.130.0/24",

        "hmh-hmba-network-1" : "10.240.21.0/24",

        "hmh-hmba-network-2" : "10.240.23.0/24",

        "hmh-hmcy-network-1" : "10.212.240.0/24",

        "hmh-hmcy-network-2" : "10.212.34.0/24",

        "hmh-hmcy-network-3" : "10.212.31.0/24",

        "hmh-hmcy-network-4" : "10.212.36.0/24",

        "hmh-hmcy-network-5" : "10.212.32.0/24",

        "hmh-hmcy-network-6" : "10.212.46.0/24",

        "hmh-hmcy-network-7" : "10.212.44.0/24",

        "hmh-hmcy-network-8" : "10.212.45.0/24",

        "hmh-hmcy-network-9" : "10.212.24.0/24",

        "hmh-hmcy-network-10" : "10.212.22.0/24",

        "hmh-hmcy-network-11" : "10.212.27.0/24",

        "hmh-hmcy-network-12" : "10.212.21.0/24",

        "hmh-hmcy-network-13" : "10.213.11.0/24",

        "hmh-hmcy-network-14" : "10.213.12.0/24",

        "hmh-hmcy-network-15" : "10.212.33.0/24",

        "nbh-nbmc-network" : "10.1.88.0/24",

        "tgh-main-network" : "172.31.32.0/24",

        "lbhs-gor-network-1" : "10.21.4.0/24",

        "lbhs-gor-network-2" : "10.21.6.0/24",

        "hmh-hmwb-network-1" : "10.132.35.0/24",

        "hmh-hmwb-network-2" : "10.132.36.0/24",

        "hmh-hmwb-network-3" : "10.130.20.0/24",

        "hmh-hmwb-network-4" : "10.131.13.0/24",

        "hmhn-htp-network-1" : "10.132.178.0/24",
      },
      // Access control lists.
      "acls" : [
        // Exit nodes can access everything
        { "action" : "accept", "src" : ["tag:exit-node"], "dst" : ["autogroup:internet:*"] },
        // Edge nodes can access exit nodes and gcs buckets
        {
          "action" : "accept",
          "src" : ["tag:edge"],
          "dst" : ["autogroup:internet:*"]
        },
        // we want only platform services and ops team accessing edge nodes
        {
          "action" : "accept",
          "src" : ["group:platform-services", "group:ops"],
          "dst" : ["tag:edge:*", "tag:k8s:*", "tag:k8s-operator:*", "tag:dmz:*", ]
        },
        {
          // platform services and ops team can access internal non-tailscale networks
          "action" : "accept",
          "src" : ["group:platform-services", "group:ops"],
          "dst" : [
            "apella-camera-network:80,443,554",
            "hf-vh-network:80,443,554",
            "hmh-hmcl-main-network:80,443,554",
            "hmh-hmcl-ld-network:80,443,554",
            "hmh-hmcl-asc-network:80,443,554",
            "hmh-dunn03-network:80,443,554",
            "hmh-main03-network-1:80,443,554",
            "hmh-main03-network-2:80,443,554",
            "hmh-dunn06-network:80,443,554",
            "hmh-opc18-network-1:80,443,554",
            "hmh-opc18-network-2:80,443,554",
            "hmh-hmtw-network-1:80,443,554",
            "hmh-hmtw-network-2:80,443,554",
            "hmh-hmtw-network-3:80,443,554",
            "hmh-hmtw-network-4:80,443,554",
            "hmh-hmtw-network-5:80,443,554",
            "hmh-hmbt-network-1:80,443,554",
            "hmh-hmbt-network-2:80,443,554",
            "hmh-hmbt-network-3:80,443,554",
            "hmh-hmbt-network-4:80,443,554",
            "hmh-hmw-network-1:80,443,554",
            "hmh-hmw-network-2:80,443,554",
            "hmh-hmw-network-3:80,443,554",
            "hmh-hmw-network-4:80,443,554",
            "hmh-hmw-network-5:80,443,554",
            "hmh-hmsl-network-1:80,443,554",
            "hmh-hmsl-network-2:80,443,554",
            "hmh-hmsl-network-3:80,443,554",
            "hmh-hmsl-network-4:80,443,554",
            "hmh-hmsl-ld-network:80,443,554",
            "hmh-hmba-network-1:80,443,554",
            "hmh-hmba-network-2:80,443,554",
            "hmh-hmcy-network-1:80,443,554",
            "hmh-hmcy-network-2:80,443,554",
            "hmh-hmcy-network-3:80,443,554",
            "hmh-hmcy-network-4:80,443,554",
            "hmh-hmcy-network-5:80,443,554",
            "hmh-hmcy-network-6:80,443,554",
            "hmh-hmcy-network-7:80,443,554",
            "hmh-hmcy-network-8:80,443,554",
            "hmh-hmcy-network-9:80,443,554",
            "hmh-hmcy-network-10:80,443,554",
            "hmh-hmcy-network-11:80,443,554",
            "hmh-hmcy-network-12:80,443,554",
            "hmh-hmcy-network-13:80,443,554",
            "hmh-hmcy-network-14:80,443,554",
            "hmh-hmcy-network-15:80,443,554",
            "hmh-opc19-network:80,443,554",
            "hmh-wt03-network:80,443,554",
            "nbh-nbmc-network:80,443,554",
            "tgh-main-network:80,443,554",
            "lbhs-gor-network-1:80,443,554",
            "lbhs-gor-network-2:80,443,554",
            "hmh-hmwb-network-1:80,443,554",
            "hmh-hmwb-network-2:80,443,554",
            "hmh-hmwb-network-3:80,443,554",
            "hmh-hmwb-network-4:80,443,554",
            "hmhn-htp-network-1:80,443,554",
          ]
        },
        {
          // all users can access gcp internal network
          "action" : "accept",
          "src" : ["autogroup:members"],
          "dst" : [
            "dev-gcp-network-1:*",
            "dev-gcp-network-2:80,443",
            "staging-gcp-network-1:*",
            "staging-gcp-network-2:80,443",
            "prod-gcp-network-1:*",
            "prod-gcp-network-2:80,443",
          ]
        },
        {
          // all users can access third party peer network
          "action" : "accept",
          "src" : ["autogroup:members"],
          "dst" : [
            "dev-confluent-network:*",
          ]
        },

        // all users can use exit node and connect to own signed-in devices
        {
          "action" : "accept",
          "src" : ["autogroup:members"],
          "dst" : ["autogroup:internet:*", "autogroup:self:*"],
        },
        // All grafana egresses can connect to grafana ingresses
        {
          "action" : "accept",
          "src" : ["tag:k8s-grafana"],
          "dst" : ["tag:k8s-grafana:*"]
        },
        // Setup app connector for access to anything
        {
          "action" : "accept",
          "src" : ["autogroup:members"],
          "dst" : ["autogroup:internet:*"],
        },
        // Setup Cobalt User to access 1 Dev NUC
        {
          "action" : "accept",
          "src" : ["group:cobalt"],
          "dst" : ["tag:cobalt:22"],
        },
      ],
      "tests" : [
        {
          "src" : "tag:exit-node",
          "accept" : ["*******:80"],
          "deny" : ["tag:edge:80", "tag:gcp-subnet-router:80", "<EMAIL>:80"]
        },
        {
          "src" : "tag:edge",
          "accept" : ["*******:80", "*******:443"],
          // deny access to dev laptops and edge nodes in other sites. Access through exit nodes instead does not require access to it.
          "deny" : ["tag:exit-node:80", "<EMAIL>:80", "tag:gcp-subnet-router:80", "tag:gcp-subnet-router:443"]
        },
        {
          "src" : "tag:gcp-subnet-router",
          "deny" : ["tag:edge:80", "tag:exit-node:80", "<EMAIL>:80"]
        },
        {
          "src" : "tag:k8s-grafana",
          "accept" : ["tag:k8s-grafana:4317"],
          "deny" : ["tag:exit-node:80", "<EMAIL>:80", "tag:gcp-subnet-router:80", "tag:gcp-subnet-router:443"]
        },
        {
          "src" : "<EMAIL>",
          // pending a way to access camera web servers not on the tailnet, we have the very liberal access below
          "accept" : ["tag:edge:80", "<EMAIL>:22", "************:443", "*********:443"],
          "deny" : ["<EMAIL>:80", "<EMAIL>:22", "tag:exit-node:80"]
        },
        {
          // can connect to an ip on the prod subnet
          "src" : "<EMAIL>",
          "accept" : ["*******:443", "<EMAIL>:22", "************:443", "************:8008"],
          "deny" : ["tag:edge:22", "tag:k8s:443", "tag:k8s-operator:443", "<EMAIL>:80", "<EMAIL>:22", "tag:exit-node:22", "*********:5000"]
        }
      ],
      "autoApprovers" : {
        "routes" : {
          // prod-gke-bastion
          "***********/22" : ["tag:gcp-subnet-router"],
          "***********/28" : ["tag:gcp-subnet-router"],
          // staging-gke-bastion
          "***********/22" : ["tag:gcp-subnet-router"],
          "************/28" : ["tag:gcp-subnet-router"],
          // dev-gke-bastion
          "***********/22" : ["tag:gcp-subnet-router"],
          "***********/28" : ["tag:gcp-subnet-router"],
          // Setup app connector routes to anything
          "0.0.0.0/0" : ["tag:connector"],
          "::/0" : ["tag:connector"],
        },
        "exitNode" : ["tag:exit-node"],
      },
      "ssh" : [
        {
          "action" : "check",
          "src" : ["group:platform-services", "group:ops"],
          "dst" : ["tag:edge", "tag:dmz"],
          "users" : ["apella", "root"],
          "checkPeriod" : "24h0m0s",
        },
        {
          "action" : "check",
          "src" : ["group:cobalt"],
          "dst" : ["tag:cobalt"],
          "users" : ["apella"],
          "checkPeriod" : "24h0m0s",
        },
      ],
      "tagOwners" : {
        "tag:gcp-subnet-router" : [
          "group:platform-services",
          "group:ops",
        ],
        "tag:edge" : [
          "group:platform-services",
          "group:ops",
        ],
        "tag:exit-node" : [
          "group:platform-services",
          "group:ops",
        ],
        "tag:connector" : [
          "group:platform-services",
          "group:ops",
        ],
        "tag:k8s-operator" : [],
        "tag:k8s" : [
          "tag:k8s-operator",
          "group:platform-services",
          "group:ops",
        ],
        "tag:k8s-grafana" : [
          "tag:k8s-operator",
          "group:platform-services",
        ],
        "tag:dmz" : [
          "group:platform-services",
        ],
        "tag:cobalt" : [
          "group:platform-services",
          "group:ops",
        ]
      },
      "derpMap" : {
        "regions" : {
          "1" : null,
          "3" : null,
          "4" : null,
          "5" : null,
          "6" : null,
          "7" : null,
          "8" : null,
          "9" : null,
          "10" : null,
          "11" : null,
          "12" : null,
          "13" : null,
          "14" : null,
          "15" : null,
          "16" : null,
          "17" : null,
          "18" : null,
          "19" : null,
          "20" : null,
          "21" : null,
          "22" : null,
          "23" : null,
          "24" : null,
          "26" : null,
          "27" : null
        },
      },
      "nodeAttrs" : [
        {
          "target" : ["*"],
          "app" : {
            "tailscale.com/app-connectors" : [
              // configure app connector for Hex.
              // Tailscale Doc: https://tailscale.com/kb/1339/preset-apps#tailnet-policy-file-details
              {
                "name" : "hex",
                "connectors" : [
                  "tag:connector"
                ],
                "domains" : [
                  "hex.tech",
                  "*.hex.tech",
                ]
              },
            ]
          }
        }
      ],
    }
  )
}
