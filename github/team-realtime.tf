locals {
  realtime_linear_prefixes = ["RT"]
}

/************************************************
  Team: Realtime
************************************************/

module "realtime_team" {
  source         = "./modules/github-team"
  name           = "Realtime"
  description    = "Helping make better real-time decisions"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "jrmils89",            # <PERSON>
    "juandiegocastrillon", # <PERSON>
    "Na-Apella",           # <PERSON>
    "samraper",            # <PERSON>
  ]
}

/************************************************
  Settings
************************************************/

resource "github_team_settings" "realtime_team_settings" {
  team_id = module.realtime_team.github_team.id
  review_request_delegation {
    algorithm    = "ROUND_ROBIN"
    member_count = 1
    notify       = false
  }
}
