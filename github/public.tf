/************************************************
  Repositories
************************************************/

module "dot_github" {
  source         = "./modules/apella-secure-repo"
  name           = ".github"
  description    = "Public github configuration for Apella"
  topics         = ["github", "configuration"]
  github_team_id = module.engineering_team.github_team.id

  # This repository must be public in order to act as a default source of various
  # Github-related files (e.g. pull request templates).
  visibility = "public"
}
