/************************************************
  Shared: Infrastructure
************************************************/


module "infrastructure" {
  source         = "./modules/apella-secure-repo"
  name           = "infrastructure"
  description    = "Infrastructure as code for Apella"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_datadog_gcp_monitor" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-datadog-gcp-monitor"
  description    = "Module for defining a Datadog monitor for GCP"
  topics         = ["datadog", "configuration", "infra"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_datadog_gcp_pubsub_monitors" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-datadog-gcp-pubsub-monitors"
  description    = "Module for defining Datadog monitors for GCP Pub/Sub"
  topics         = ["datadog", "configuration", "infra"]
  github_team_id = module.engineering_team.github_team.id
}

/************************************************
  Terraform Modules
************************************************/

module "terraform_google_bigquery" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-bigquery"
  description    = "Terraform module: BigQuery"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_cloud_nat" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-cloud-nat"
  description    = "Terraform module: Cloud NAT"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_cloud_router" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-cloud-router"
  description    = "Terraform module: Cloud Router"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_cloud_sql" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-cloud-sql"
  description    = "Terraform module: Cloud SQL"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_cloud_storage" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-cloud-storage"
  description    = "Terraform module: Cloud Storage"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_folders" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-folders"
  description    = "Terraform module: Folders"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_groups" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-groups"
  description    = "Terraform module: Google Groups"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_module_reference" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-module-reference"
  description    = "Terraform module: Module Reference"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_project_factory" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-project-factory"
  description    = "Terraform module: Project Factory"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_sharedvpc_access" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-sharedvpc-access"
  description    = "Terraform module: Shared VPC Access"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_vpc" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-vpc"
  description    = "Terraform module: VPC"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}

module "terraform_google_pubsub_subscription_with_deadletter" {
  source         = "./modules/apella-secure-repo"
  name           = "terraform-google-pubsub-subscription-with-deadletter"
  description    = "Terraform module: Pub/Sub Subscription with Deadletter"
  topics         = ["infrastructure", "terraform", "hcl"]
  github_team_id = module.engineering_team.github_team.id
}
