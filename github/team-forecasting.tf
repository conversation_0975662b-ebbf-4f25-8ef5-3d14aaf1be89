locals {
  fc_linear_prefixes = ["FORC"]
}

module "forecasting_team" {
  source         = "./modules/github-team"
  name           = "forecasting"
  description    = "ML Forecasting Team"
  parent_team_id = module.ds_ml_team.github_team.id
  maintainers = [
    "apel<PERSON>",      # <PERSON>
    "mingzhang9",   # <PERSON>
    "Smoss",        # <PERSON>,
    "mingzhang9",   # <PERSON>
    "will-appella", # <PERSON>
    "theorenloo",   # <PERSON>
  ]
}

module "realtime-dags" {
  source          = "./modules/apella-secure-repo"
  name            = "realtime-dags"
  description     = "Dagster repo for forecasting"
  topics          = ["dagster", "forecasting", "dag"]
  github_team_id  = module.forecasting_team.github_team.id
  linear_prefixes = local.fc_linear_prefixes
}