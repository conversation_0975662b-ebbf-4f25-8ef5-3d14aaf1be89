resource "github_team" "apella_team" {
  name           = var.name
  description    = var.description
  privacy        = var.privacy
  parent_team_id = var.parent_team_id
}

resource "github_team_membership" "maintainer" {
  for_each = toset(var.maintainers)
  team_id  = github_team.apella_team.id
  username = each.key
  role     = "maintainer"
}

resource "github_team_membership" "member" {
  for_each = toset(var.members)
  team_id  = github_team.apella_team.id
  username = each.key
  role     = "member"
}
