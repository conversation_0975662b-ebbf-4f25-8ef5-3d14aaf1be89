variable "name" {
  type        = string
  description = "Team name"
}

variable "description" {
  type        = string
  description = "Team description"
}

variable "parent_team_id" {
  type        = number
  description = "Parent team id"
  default     = null
}

variable "privacy" {
  type        = string
  description = "Team privacy"
  default     = "closed"
}

variable "maintainers" {
  type        = list(string)
  description = "Team members"
  default     = []
}

variable "members" {
  type        = list(string)
  description = "Team members"
  default     = []
}
