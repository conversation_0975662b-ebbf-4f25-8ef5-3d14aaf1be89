variable "name" {
  type        = string
  description = "Repository name"
}

variable "description" {
  type        = string
  description = "Repository description"
}

variable "visibility" {
  type        = string
  description = "The type of repository you're creating. Can be private, public, or internal."
  default     = "private"
  validation {
    condition     = contains(["private", "public", "internal"], var.visibility)
    error_message = "Valid values for var:visibility are private, public, or internal."
  }

}

variable "topics" {
  type        = list(string)
  description = "Github topics assigned to repository"
  default     = []
}

variable "linear_prefixes" {
  type        = list(string)
  description = "List of linear project prefixes"
  default     = []
}

variable "require_pull_request_reviews" {
  type        = bool
  description = "Require pull request reviews - override only in exceptional cases"
  default     = true
}

variable "github_team_id" {
  type        = string
  description = "Github team acting as maintainer"
  default     = ""
}

variable "template" {
  type = object({
    owner      = string
    repository = string
  })
  description = "Repository template"
  default     = null
}

variable "protected_environments" {
  type        = list(string)
  description = "Environments to require approvals before deploying"
  default     = []
}

variable "strict_checks_required" {
  type        = bool
  description = "Require that the branch being merged is up to date with main before merging"
  default     = false
}

variable "checks_required_to_merge_to_main" {
  type        = list(string)
  description = "Names of checks that must pass before a PR can be merged into the main branch."
  default     = []
}

variable "pull_request_bypassers" {
  type        = list(string)
  description = "Actors/IDs that are allowed to bypass pull request requirements."
  default     = []
}


variable "allow_merge_commit" {
  type        = bool
  description = "Set to false to disable merge commits on the repository."
  default     = false
}

variable "archived" {
  type        = bool
  description = "True for archiving a repository"
  default     = false
}

variable "squash_merge_commit_title" {
  type        = string
  description = "Title of the commit message when squashing merge commits"
  default     = "COMMIT_OR_PR_TITLE"
  validation {
    condition     = contains(["COMMIT_OR_PR_TITLE", "PR_TITLE"], var.squash_merge_commit_title)
    error_message = "Valid values for var:squash_merge_commit_title are COMMIT_OR_PR_TITLE, or PR_TITLE."
  }
}

variable "squash_merge_commit_message" {
  type        = string
  description = "Message of the commit message when squashing merge commits"
  default     = "COMMIT_MESSAGES"
  validation {
    condition     = contains(["COMMIT_MESSAGES", "PR_BODY", "BLANK"], var.squash_merge_commit_message)
    error_message = "Valid values for var:squash_merge_commit_message are PR_BODY, BLANK, or COMMIT_MESSAGES."
  }
}
