resource "github_repository" "repo" {
  name        = var.name
  description = var.description

  visibility                  = var.visibility
  has_issues                  = false
  has_wiki                    = false
  has_projects                = false
  allow_merge_commit          = var.allow_merge_commit
  allow_squash_merge          = true
  squash_merge_commit_title   = var.squash_merge_commit_title
  squash_merge_commit_message = var.squash_merge_commit_message
  allow_rebase_merge          = false
  delete_branch_on_merge      = true
  auto_init                   = false
  topics                      = var.topics
  vulnerability_alerts        = true
  archived                    = var.archived

  dynamic "template" {
    for_each = var.template[*]
    content {
      owner      = template.value.owner
      repository = template.value.repository
    }
  }
}

resource "github_team_repository" "repository_permission" {
  team_id    = var.github_team_id
  repository = github_repository.repo.name
  permission = "admin"
}

resource "github_branch_protection" "require_checks_and_review" {
  repository_id = github_repository.repo.id

  pattern                 = "main"
  enforce_admins          = true
  allows_deletions        = true
  required_linear_history = true

  required_status_checks {
    strict   = var.strict_checks_required
    contexts = var.checks_required_to_merge_to_main
  }

  required_pull_request_reviews {
    restrict_dismissals             = false
    required_approving_review_count = var.require_pull_request_reviews ? 1 : 0
    pull_request_bypassers          = var.pull_request_bypassers
  }
}

resource "github_repository_autolink_reference" "linear_key_prefix" {
  for_each = toset(var.linear_prefixes)

  repository = github_repository.repo.name

  key_prefix = "${each.value}-"

  target_url_template = "https://linear.app/apella/issue/${each.value}-<num>"
}

resource "github_repository_environment" "protected_environment" {
  for_each    = toset(var.protected_environments)
  environment = each.value
  repository  = github_repository.repo.name
  reviewers {
    teams = [var.github_team_id]
  }
}
