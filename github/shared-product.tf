/************************************************
  Shared: Product & Frontend
************************************************/


module "lib-web" {
  source                           = "./modules/apella-secure-repo"
  name                             = "lib-web"
  description                      = "Frontend monorepo"
  topics                           = ["library", "web"]
  github_team_id                   = module.engineering_team.github_team.id
  linear_prefixes                  = concat(local.frontend_linear_prefixes, local.orion_linear_prefixes, local.realtime_linear_prefixes, local.platform_services_linear_prefixes, local.ehr_linear_prefixes, local.orca_linear_prefixes)
  checks_required_to_merge_to_main = ["Typescript", "Build", "Lint", "Unit-Tests", "Circular-Dependency-Check"]
  squash_merge_commit_title        = "PR_TITLE"
}

module "cloud-api-server" {
  source                           = "./modules/apella-secure-repo"
  name                             = "cloud-api-server"
  description                      = "Apella's product API server"
  topics                           = ["frontend", "product-eng", "web"]
  github_team_id                   = module.engineering_team.github_team.id
  checks_required_to_merge_to_main = ["Python-Linter", "Python-Unit-Tests", "Component-Tests", "No-Pending-SQL-Migrations"]
  linear_prefixes                  = concat(local.orion_linear_prefixes, local.realtime_linear_prefixes, local.ds_ml_linear_prefixes, local.platform_services_linear_prefixes, local.ehr_linear_prefixes, local.orca_linear_prefixes)
}
