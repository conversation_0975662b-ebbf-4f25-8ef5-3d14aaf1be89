/************************************************
  Team: EHR
************************************************/

locals {
  ehr_linear_prefixes = ["EHR"]
}

module "ehr_team" {
  source         = "./modules/github-team"
  name           = "EHR"
  description    = "EHR client integration engine"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "d<PERSON><PERSON><PERSON>",    # <PERSON>
    "mauricecarey",   # <PERSON>
    "mikedmcfarland", # <PERSON>
    "ncgaskin",       # <PERSON>
    "hatah<PERSON>",        # Zia<PERSON> Hatahet
    "CelestineW",     # <PERSON>
  ]
}

/************************************************
  Repositories
************************************************/

module "redox-processor" {
  source          = "./modules/apella-secure-repo"
  name            = "redox-processor"
  description     = "Inbound EHR data processor"
  topics          = ["ehr", "redox", "hl7"]
  github_team_id  = module.ehr_team.github_team.id
  linear_prefixes = local.ehr_linear_prefixes
}

module "ehr" {
  source          = "./modules/apella-secure-repo"
  name            = "ehr"
  description     = "EHR v2 Processor"
  topics          = ["ehr", "hl7"]
  github_team_id  = module.ehr_team.github_team.id
  linear_prefixes = local.ehr_linear_prefixes
}

/************************************************
  ArgoCD Kubernetes Repositories
************************************************/
module "k8s-ehr" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-ehr"
  description     = "Repository to store the k8s applications for EHR integrations"
  topics          = ["devops", "ehr", "k8s", "argocd"]
  github_team_id  = module.ehr_team.github_team.id
  linear_prefixes = local.ehr_linear_prefixes
}

module "hl7apy" {
  source          = "./modules/apella-secure-repo"
  name            = "hl7apy"
  description     = "Fork of github.com/crs4/hl7apy"
  topics          = ["ehr", "hl7"]
  github_team_id  = module.ehr_team.github_team.id
  linear_prefixes = local.ehr_linear_prefixes
}
