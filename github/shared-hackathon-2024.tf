/************************************************
  Shared: For Hacakthon 2024
************************************************/


module "hack-surgeon-case-rating" {
  source         = "./modules/apella-secure-repo"
  name           = "hack-surgeon-case-rating"
  description    = "Hackathon project for surgeons to rate cases"
  topics         = ["hackathon", "notifications"]
  github_team_id = module.engineering_team.github_team.id
}

module "hack-configuration-service" {
  source         = "./modules/apella-secure-repo"
  name           = "hack-configomatic-service"
  description    = "Hackathon project for Apella Configuration Service"
  topics         = ["hackathon", "configuration", "service", "caas"]
  github_team_id = module.engineering_team.github_team.id
  archived       = true
}

module "hack-configuration-configs" {
  source         = "./modules/apella-secure-repo"
  name           = "hack-configomatic"
  description    = "Hackathon project for Apella's centralized configurations repository"
  topics         = ["hackathon", "configuration"]
  github_team_id = module.engineering_team.github_team.id
}

module "hack-gemini" {
  source         = "./modules/apella-secure-repo"
  name           = "hack-gemini"
  description    = "Hackathon project for Gemini"
  topics         = ["hackathon", "gemini"]
  github_team_id = module.ds_ml_team.github_team.id
}
