/************************************************
  Team: Data Science & Machine Learning
************************************************/

locals {
  ds_ml_linear_prefixes = ["CV", "FORC", "DP"]
}

module "ds_ml_team" {
  source         = "./modules/github-team"
  name           = "ds-ml"
  description    = "Data Science & Machine Learning"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "apellex",           # <PERSON>
    "kamillatekiela",    # <PERSON><PERSON><PERSON>
    "mingzhang9",        # <PERSON>
    "nathan<PERSON>bie", # <PERSON>
    "OrenLederman",      # <PERSON><PERSON>
    "rob-apella",        # <PERSON>
    "Smoss",             # <PERSON>,
    "ZacCarrico"         # <PERSON>ac <PERSON>
  ]
}

/************************************************
  Active Repositories
************************************************/

module "mlops_dags" {
  source                 = "./modules/apella-secure-repo"
  name                   = "mlops-dags"
  description            = "Repository MLOps code and pipelines"
  topics                 = ["python", "ml", "mlops", "dagster"]
  github_team_id         = module.ds_ml_team.github_team.id
  protected_environments = ["prod"]
  linear_prefixes        = local.ds_ml_linear_prefixes
}

module "ml_deployments" {
  source          = "./modules/apella-secure-repo"
  name            = "ml-deployments"
  description     = "Repository to deploy ML infra (e.g. wandb, seldon) on our own infrastructure"
  topics          = ["wandb", "mlops", "helm", "k8s"]
  github_team_id  = module.ds_ml_team.github_team.id
  linear_prefixes = local.ds_ml_linear_prefixes
}

module "data_deployments" {
  source          = "./modules/apella-secure-repo"
  name            = "data-deployments"
  description     = "Repository to deploy Data infra (e.g. Metabase, Dagster) on our own infrastructure"
  topics          = ["data", "helm", "k8s"]
  github_team_id  = module.ds_ml_team.github_team.id
  linear_prefixes = local.ds_ml_linear_prefixes
}

module "ml-services" {
  source                           = "./modules/apella-secure-repo"
  name                             = "ml-services"
  description                      = "Training and inference services for Apella ML models"
  topics                           = ["ml", "python"]
  github_team_id                   = module.ds_ml_team.github_team.id
  strict_checks_required           = true
  checks_required_to_merge_to_main = ["Validated"]
  linear_prefixes                  = local.ds_ml_linear_prefixes
}
