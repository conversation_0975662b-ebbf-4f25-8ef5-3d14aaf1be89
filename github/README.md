# Github Managment

This repository helps us manage our Github repositories and keep them compliant with our security policies.

## Create a new repository

To create a new repo, add a Terraform module to an existing /terraform/modules/team-<my-team>.tf file or, for a new team,
create a new /terraform/modules/team-<NEW-TEAM>.tf file. Here is an example GitHub repo module to add:
```hcl
module "github_management" {
  source        = "./modules/apella-secure-repo"
  name          = "github-management"
  description   = "Terraform based repository to manage all our GitHub repositories"
  linear_prefix = ["AP"]
  topics        = ["github", "configuration"]
}
```
`linear_prefix` refers to the team prefix used in the Linear app (e.g. "AP", "DATA", etc.). This is the same prefix used
in issues (e.g. AP-123, DATA-234). By default, all team prefixes at Apella will be used.

## Import existing repository

Follow these steps to import an existing repository.

### 1. Add the new repository

First, add a Terraform module to an existing /terraform/modules/team-<my-team>.tf file or, for a new team,
create a new /terraform/modules/team-<NEW-TEAM>.tf file.

```hcl
module "super_duper_repo" {
  source        = "./modules/apella-secure-repo"
  name          = "super-duper-repo"
  description   = "A really super repo"
  topics        = ["super", "repository"]
}
```

Commit the change, submit a PR and have it reviewed. Before merging it, check whether there are unapplied Plans in
[Terraform Cloud workspace](https://app.terraform.io/app/apella/workspaces/github-management). If there are, work with
the owners of these plans to apply them. The reason for this is that once these changes are merged, it will create a new
Plan for applying, but because this repo already exists, this plan should be canceled. Otherwise, it would overwrite the
existing repo with a blank one.

### 2. Get a Github token

If you haven't already, get a
[Github personal access token](https://github.com/settings/tokens), grant it SSO
to Apella and export it as an environment variable.

```bash
export $GITHUB_TOKEN=<your-token-here>
```

If you want, you can put this in an `.envrc` file in the root of this repository.

### 3. Import the repository into state

Once there are no pending runs in Terraform Cloud Workspace, the state will be unlocked, and you can run the following
script to import the state

```bash
cd terraform
../scripts/import-repo super_duper_repo super-duper-repo
```

This will import the repository into state. Once this import is done, you can
plan another run in the workspace and run it. The state should be up-to-date!

If you have questions, please reach out to #guild-platform

## Archive a repository

To archive a repository, follow these steps:

1. Install the Github CLI by following the instructions [here](https://github.com/cli/cli#installation").
2. Authenticate with the Github CLI by running `gh auth login`.
3. Run the following command to archive the repository:

```
./scripts/archive-repo.sh <repo-name>
```
4. Create a PR to remove the repo definitions from this repo
