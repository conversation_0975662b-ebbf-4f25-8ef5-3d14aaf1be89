locals {
  dp_linear_prefixes = ["DP"]
}

module "data_platform_team" {
  source         = "./modules/github-team"
  name           = "data-platform"
  description    = "Data Platform Team"
  parent_team_id = module.ds_ml_team.github_team.id
  maintainers = [
    "<PERSON><PERSON><PERSON><PERSON><PERSON>", # <PERSON><PERSON><PERSON>
    "rob-apella",     # <PERSON>
  ]
}

module "realtime_data_platform" {
  source         = "./modules/apella-secure-repo"
  name           = "realtime-data-platform"
  description    = "Repo for realtime data platform"
  topics         = ["decodable"]
  github_team_id = module.data_platform_team.github_team.id
}

module "apella_metabase" {
  source          = "./modules/apella-secure-repo"
  name            = "apella-metabase"
  description     = "Helm chart for deploying metabase"
  topics          = ["helm", "metabase", "k8s"]
  github_team_id  = module.data_platform_team.github_team.id
  linear_prefixes = local.dp_linear_prefixes
}