/************************************************
  Team: Python Pals
************************************************/

locals {
  python_pals_linear_prefixes = ["GPP"]
}

module "python_team" {
  source         = "./modules/github-team"
  name           = "python"
  description    = "Folks interested in snakes: the Python guild"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "abhay-apella",   # <PERSON><PERSON><PERSON>
    "apellex",        # <PERSON>
    "amipatel2",      # <PERSON><PERSON>
    "andreab-apella", # <PERSON>
    "Chase<PERSON>ro",       # <PERSON>
    "twonds",         # <PERSON>
    "darrenen<PERSON>",      # <PERSON>
    "dbullerwell",    # <PERSON>
    "mikedmcfarland", # <PERSON>
    "OrenLederman",   # <PERSON><PERSON>
    "rob-apella",     # <PERSON>
    "samraper",       # <PERSON>
    "Smoss",          # <PERSON>
    "hatahet",        # <PERSON><PERSON><PERSON>
  ]
}

resource "github_team_settings" "python_team_settings" {
  team_id = module.python_team.github_team.id
  review_request_delegation {
    algorithm    = "ROUND_ROBIN"
    member_count = 1
    notify       = false
  }
}
