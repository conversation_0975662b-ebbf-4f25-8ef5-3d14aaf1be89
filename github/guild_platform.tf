module "guild_platform" {
  source         = "./modules/github-team"
  name           = "Guild Platform"
  description    = "Managing common utility platform libraries"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "abhay-apella",   # <PERSON><PERSON><PERSON>
    "d<PERSON><PERSON><PERSON>",    # <PERSON>
    "twonds",         # <PERSON>
    "jam<PERSON><PERSON><PERSON>",    # <PERSON>
    "miked<PERSON><PERSON><PERSON><PERSON>", # <PERSON>
    "OrenLederman",   # <PERSON><PERSON>
  ]
}


module "lib_identity" {
  source         = "./modules/apella-secure-repo"
  name           = "lib-identity"
  description    = "Python library providing identity and jwt token"
  topics         = ["devops", "auth", "library"]
  github_team_id = module.guild_platform.github_team.id
}


module "lib_python_auth" {
  source         = "./modules/apella-secure-repo"
  name           = "lib-python-auth"
  description    = "Python library for managing auth for services/apps"
  topics         = ["devops", "auth", "library"]
  github_team_id = module.guild_platform.github_team.id
}
