/************************************************
  Team: ORca
************************************************/

locals {
  orca_linear_prefixes = ["ORCA"]
}

module "orca_team" {
  source         = "./modules/github-team"
  name           = "ORca"
  description    = "Offering historical data to make better decisions for the future"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "darreneng", # <PERSON>g
    "smoges25",  # <PERSON><PERSON>
    "anna-poz",  # <PERSON>
    # "rsteier-apella", # <PERSON> 
  ]
}

/************************************************
  Settings
************************************************/

resource "github_team_settings" "orca_team_settings" {
  team_id = module.orca_team.github_team.id
  review_request_delegation {
    algorithm    = "ROUND_ROBIN"
    member_count = 1
    notify       = false
  }
}

/************************************************
  Repositories
************************************************/

module "cubejs" {
  source                           = "./modules/apella-secure-repo"
  name                             = "cubejs"
  description                      = "CubeJS headless-BI"
  topics                           = ["headless-bi", "cubejs"]
  github_team_id                   = module.engineering_team.github_team.id # Giving all-eng permission so that CODEOWNERS can be flexible during transition
  linear_prefixes                  = local.orca_linear_prefixes
  checks_required_to_merge_to_main = ["Typescript-Compile", "Typescript-Lint", "Unit-Tests"]
}

module "analytics_data_pipelines" {
  source                           = "./modules/apella-secure-repo"
  name                             = "analytics-data-pipelines"
  description                      = "Analytics Data Pipelines"
  topics                           = ["dagster"]
  github_team_id                   = module.engineering_team.github_team.id # Giving all-eng permission so that CODEOWNERS can be flexible during transition
  linear_prefixes                  = local.orca_linear_prefixes
  checks_required_to_merge_to_main = ["Python-Linter", "Python-Unit-Tests"]
}
