/************************************************
  Team: ORion
************************************************/

locals {
  orion_linear_prefixes = ["ORION", "OBS"]
}
module "orion_team" {
  source         = "./modules/github-team"
  name           = "ORion"
  description    = "Product Eng Team focused on scheduling related products"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "andreab-apella",      # <PERSON>
    "amipatel2",           # <PERSON><PERSON>
    "ChaseBro",            # <PERSON>
    "infiniteluke",        # <PERSON>
    "juandiegocastrillon", # <PERSON>
  ]
}


/************************************************
  Settings
************************************************/

resource "github_team_settings" "orion_team_settings" {
  team_id = module.orion_team.github_team.id
  review_request_delegation {
    algorithm    = "ROUND_ROBIN"
    member_count = 1
    notify       = false
  }
}
