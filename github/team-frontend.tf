/************************************************
  Team: Frontend
************************************************/

locals {
  frontend_linear_prefixes = ["FEF"]
}

module "frontend_team" {
  source         = "./modules/github-team"
  name           = "Frontend"
  description    = "Frontend friends: co-collaborators across our UX libraries/web applications"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "andreab-apella", # <PERSON>
    "amipatel2",      # <PERSON><PERSON>
    "anna-poz",       # <PERSON>
    "<PERSON><PERSON>",       # <PERSON>
    "darrenen<PERSON>",      # <PERSON>
    "infiniteluke",   # <PERSON>
    "jrmils89",       # <PERSON>
  ]
}

resource "github_team_settings" "frontend_team_settings" {
  team_id = module.frontend_team.github_team.id
  review_request_delegation {
    algorithm    = "ROUND_ROBIN"
    member_count = 1
    notify       = false
  }
}
