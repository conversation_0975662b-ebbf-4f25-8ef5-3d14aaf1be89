locals {
  cv_linear_prefixes = ["CV"]
}

module "computer_vision_github_team" {
  source         = "./modules/github-team"
  name           = "computer-vision"
  description    = "Computer Vision Team"
  parent_team_id = module.ds_ml_team.github_team.id
  maintainers = [
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", # <PERSON>
    "OrenLederman",      # <PERSON><PERSON>
    "ZacCarrico"         # Zac Carrico
  ]
}

module "annotation-task-optimizer" {
  source          = "./modules/apella-secure-repo"
  name            = "annotation-task-optimizer"
  description     = "Tool to optimize which annotation tasks we create"
  topics          = ["python", "annotation"]
  github_team_id  = module.computer_vision_github_team.github_team.id
  linear_prefixes = local.cv_linear_prefixes
}

module "realtime_processing_services" {
  source         = "./modules/apella-secure-repo"
  name           = "realtime-processing-services"
  description    = "Repository for the event-changes-processor"
  topics         = ["python", "gcp"]
  github_team_id = module.computer_vision_github_team.github_team.id
}

module "event_transformer_model" {
  source          = "./modules/apella-secure-repo"
  name            = "event-transformer-model"
  description     = "Repository for training, and serving the event transformer model"
  topics          = ["python", "ml", "transformer"]
  github_team_id  = module.computer_vision_github_team.github_team.id
  linear_prefixes = local.cv_linear_prefixes
}