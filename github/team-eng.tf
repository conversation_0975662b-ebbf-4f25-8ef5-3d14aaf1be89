/************************************************
  Team: Engineering (container for other teams)
************************************************/

module "engineering_team" {
  source      = "./modules/github-team"
  name        = "eng"
  description = "Apella engineering"
  maintainers = local.apella_org_members
}

/************************************************
  Repositories
************************************************/

module "external_secrets" {
  source         = "./modules/apella-secure-repo"
  name           = "external-secrets"
  description    = "Repository to deploy external-secrets operator to k8s"
  topics         = ["devops", "helm", "k8s"]
  github_team_id = module.engineering_team.github_team.id
  archived       = true
}

module "interview_questions" {
  source      = "./modules/apella-secure-repo"
  name        = "interview-questions"
  description = "A repository to store our coding interview questions"
  topics      = ["internal", "interviews"]

  # This is a special case, we do not need PR reviews
  require_pull_request_reviews = false
  github_team_id               = module.engineering_team.github_team.id
}

module "pycharm_settings" {
  source      = "./modules/apella-secure-repo"
  name        = ".pycharm-settings"
  description = "A repository to store our pycharm settings"
  topics      = ["internal", "configuration"]

  # This is a special case, we do not need PR reviews
  require_pull_request_reviews = false
  github_team_id               = module.engineering_team.github_team.id
}

module "notebooks" {
  source         = "./modules/apella-secure-repo"
  name           = "notebooks"
  description    = "Jupyter notebook repository for easy sharing"
  topics         = ["jupyter", "python"]
  github_team_id = module.engineering_team.github_team.id
}

module "vibecode_sandbox" {
  source      = "./modules/apella-secure-repo"
  name        = "vibecode-sandbox"
  description = "Sandbox repository for vibecoding development and experimentation"
  topics      = ["sandbox", "development"]

  # This is a special case, we do not need PR reviews
  require_pull_request_reviews = false
  github_team_id               = module.engineering_team.github_team.id
}

module "load-generator" {
  source                           = "./modules/apella-secure-repo"
  name                             = "load-generator"
  description                      = "Generation of fake data in dev and staging and for prod Sales Demo"
  topics                           = ["python", "fake", "generator"]
  github_team_id                   = module.engineering_team.github_team.id
  checks_required_to_merge_to_main = ["Validated"]
}