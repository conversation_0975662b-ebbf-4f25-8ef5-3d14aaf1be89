/************************************************
  Organization membership
************************************************/

locals {
  apella_org_members = [
    "a-atinling",          # <PERSON>
    "abhay-apella",        # <PERSON><PERSON><PERSON>
    "andrew<PERSON>pay",         # <PERSON>
    "anna-poz",            # <PERSON>
    "apella-service",      # Apella Service Account
    "apellex",             # <PERSON>
    "cameron<PERSON>low",       # <PERSON>
    "Ce<PERSON>tineW",          # <PERSON><PERSON><PERSON>
    "ChaseBro",            # <PERSON>
    "darreneng",           # <PERSON>
    "dbull<PERSON><PERSON>",         # <PERSON>
    "hatahet",             # Ziad <PERSON>ahet
    "jamesapella",         # <PERSON>
    "jrmils89",            # <PERSON>
    "juandiegocastrillon", # <PERSON>
    "Kelsey-design",       # <PERSON>
    "leothedarling",       # <PERSON>
    "mauricecarey",        # <PERSON>
    "mikedmc<PERSON>land",      # <PERSON>
    "mingzhang9",          # <PERSON>
    "nathan<PERSON><PERSON><PERSON>",   # <PERSON>
    "Na-Apella",           # <PERSON>
    "ncgaskin",            # <PERSON> <PERSON><PERSON>
    "nhilger",             # <PERSON> <PERSON>lger
    "OrenLede<PERSON>",        # Oren Lederman
    "rob-a<PERSON>",          # <PERSON> <PERSON><PERSON><PERSON>
    "rockap<PERSON>",          # <PERSON> <PERSON>luto
    "samraper",            # <PERSON> <PERSON>er
    "smoges25",            # <PERSON>lam <PERSON>ges
    "<PERSON><PERSON><PERSON>",               # <PERSON> <PERSON>
    "teichman",            # <PERSON> <PERSON>ichman
    "theorenloo",          # <PERSON> <PERSON>o
    "twonds",              # <PERSON> <PERSON>orn
    "will-appella",        # Will Geary
  ]

  apella_admins = [
    "abhay-apella",        # Abhay Mahajan
    "cameronmarlow",       # Cameron Marlow
    "dbullerwell",         # Dorian Bullerwell
    "juandiegocastrillon", # Juan Diego Castrillon
    "rockapella",          # Michael Fluto
    "twonds",              # Christopher Zorn
    "leothedarling",       # Leo Darling
  ]
}

resource "github_membership" "apella_member" {
  for_each = toset(local.apella_org_members)
  username = each.key
  role     = contains(local.apella_admins, each.key) ? "admin" : "member"
}
