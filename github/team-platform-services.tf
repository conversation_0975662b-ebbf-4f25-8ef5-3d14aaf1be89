/************************************************
  Team: Platform Services
************************************************/

locals {
  platform_services_linear_prefixes = ["PS"]
}

module "platform_services_team" {
  source         = "./modules/github-team"
  name           = "Platform Services"
  description    = "Managing the edge and increasing developer velocity"
  parent_team_id = module.engineering_team.github_team.id
  maintainers = [
    "abhay-apella", # <PERSON><PERSON><PERSON>
    "d<PERSON><PERSON><PERSON>",  # <PERSON>
    "nc<PERSON>",     # <PERSON>
    "twonds",       # <PERSON>
    "james<PERSON><PERSON>"   # <PERSON>
  ]
}

/************************************************
  Repositories
************************************************/


module "developer_portal" {
  source          = "./modules/apella-secure-repo"
  name            = "developer-portal"
  description     = "Services and tools for providing a single integration of the developer platform"
  topics          = ["developer", "platform", "typescript", "devops", "backstage"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}


module "camera_controller" {
  source          = "./modules/apella-secure-repo"
  name            = "camera-controller"
  description     = "Services and tools for providing tooling for camera configuration and operation"
  topics          = ["platform", "edge", "camera", "devices"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}


module "platform_examples" {
  source          = "./modules/apella-secure-repo"
  name            = "platform-example"
  description     = "Well document, tested, working Platform Service examples."
  topics          = ["k8s", "python", "terraform", "templates", "examples"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "k8s_platform_examples" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-platform-examples"
  description     = "Well document, tested, working Platform Service examples."
  topics          = ["k8s", "python", "terraform", "templates", "examples"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "platform_templates" {
  source          = "./modules/apella-secure-repo"
  name            = "platform-templates"
  description     = "Platform skeltons and templates for applying features to projects easily."
  topics          = ["platform", "templates", "examples"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}


module "system_setup" {
  source          = "./modules/apella-secure-repo"
  name            = "system-setup"
  description     = "Ansible setup scripts for edge devices"
  topics          = ["ansible", "python", "edge"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "media_asset_service" {
  source          = "./modules/apella-secure-repo"
  name            = "media-asset-service"
  description     = "Apella media asset management service."
  topics          = ["k8s", "python", "service"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "media_sdk" {
  source          = "./modules/apella-secure-repo"
  name            = "media-sdk"
  description     = "Media SDK"
  topics          = ["cpp", "sdk", "media"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "technology-radar" {
  source          = "./modules/apella-secure-repo"
  name            = "technology-radar"
  description     = "Repository to store the Apella technology radar"
  topics          = ["technology"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "gstreamer-builder" {
  source          = "./modules/apella-secure-repo"
  name            = "gstreamer-builder"
  description     = "Builds images with gstreamer built in"
  topics          = ["python", "edge", "rtsp", "streaming", "nvr"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "rtsp-transcoder" {
  source          = "./modules/apella-secure-repo"
  name            = "rtsp-transcoder"
  description     = "Program that reads from camera RTSP streams and stores image snapshots and video segments locally"
  topics          = ["python", "edge", "rtsp", "streaming", "nvr"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "camera_setup" {
  source          = "./modules/apella-secure-repo"
  name            = "camera-setup"
  description     = "Service to scan and upgrade cameras"
  topics          = ["typescript", "edge"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

/************************************************
  Libraries
************************************************/

module "lib-helm" {
  source          = "./modules/apella-secure-repo"
  name            = "lib-helm"
  description     = "Repository to manage Helm Library Charts to simplify following best practices"
  topics          = ["devops", "helm", "library"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "lib-helm-argo" {
  source          = "./modules/apella-secure-repo"
  name            = "lib-helm-argo"
  description     = "Helm chart for easily managing argo crd resources"
  topics          = ["devops", "helm", "library"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "lib-helm-dagster" {
  source          = "./modules/apella-secure-repo"
  name            = "lib-helm-dagster"
  description     = "Repository to manage Helm Library Charts for self-hosted Dagster."
  topics          = ["devops", "helm", "library", "dagster"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "lib-python-logging" {
  source          = "./modules/apella-secure-repo"
  name            = "lib-python-logging"
  description     = "Repository to store a logging library for use in Apella"
  topics          = ["python", "configuration"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "lib-python-config" {
  source          = "./modules/apella-secure-repo"
  name            = "lib-python-config"
  description     = "Repository to store a configuration library for use in Apella"
  topics          = ["python", "configuration"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

/************************************************
  Terraform
************************************************/

module "terraform-google-github-action-runners" {
  source          = "./modules/apella-secure-repo"
  name            = "terraform-google-github-action-runners"
  description     = "Terraform module: Github Action Runners"
  topics          = ["terraform", "github"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
  template = {
    owner      = "Apella-Technology"
    repository = "terraform-google-template"
  }
}

module "datadog-management" {
  source          = "./modules/apella-secure-repo"
  name            = "datadog-management"
  description     = "Repository to manage best practices for Datadog"
  topics          = ["devops", "datadog", "monitoring"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

/************************************************
  ArgoCD Kubernetes Repositories
************************************************/
module "k8s-internal" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-internal"
  description     = "Repository to manage bootstrapping and deployment of the Internal k8s clusters"
  topics          = ["devops", "argocd", "k8s"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

data "github_app" "auto_edge" {
  slug = var.autoedge_app_slug
}

module "k8s-edge" {
  source                 = "./modules/apella-secure-repo"
  name                   = "k8s-edge"
  description            = "Repository to store the edge k8s applications"
  topics                 = ["devops", "k8s", "argocd", "edge"]
  pull_request_bypassers = [data.github_app.auto_edge.node_id]
  # k8s-edge auto approve and merge workflow requires this
  allow_merge_commit = true
  github_team_id     = module.platform_services_team.github_team.id
  linear_prefixes    = local.platform_services_linear_prefixes
}

module "k8s-media-asset" {
  source                 = "./modules/apella-secure-repo"
  name                   = "k8s-media-asset"
  description            = "Repository to store the media asset k8s applications"
  topics                 = ["devops", "k8s", "argocd"]
  github_team_id         = module.platform_services_team.github_team.id
  linear_prefixes        = local.platform_services_linear_prefixes
  pull_request_bypassers = [var.argocd_image_updater_app_id]
}

module "k8s-product" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-product"
  description     = "Customer-facing Kubernetes applications"
  topics          = ["devops", "argocd", "k8s"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "k8s-dagster" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-dagster"
  description     = "Repository to store dagster applications"
  topics          = ["devops", "k8s", "argocd", "dagster"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "k8s-knative" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-knative"
  description     = "Repository to store knative operator deployment configuration"
  topics          = ["devops", "k8s", "argocd", "knative"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

module "k8s-sandbox" {
  source          = "./modules/apella-secure-repo"
  name            = "k8s-sandbox"
  description     = "Repository to store sandbox and testing of components."
  topics          = ["devops", "k8s", "argocd", "sandbox"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
}

/************************************************
  Shared Github Actions
************************************************/
module "setup-node" {
  source         = "./modules/apella-secure-repo"
  name           = "setup-node"
  description    = "Repository to store a shared github action for setting up node"
  topics         = ["node", "javascript", "typescript", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "setup-python-env" {
  source         = "./modules/apella-secure-repo"
  name           = "setup-python-env"
  description    = "Repository to store a shared github action for setting up a python environment"
  topics         = ["python", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "publish-python-package" {
  source          = "./modules/apella-secure-repo"
  name            = "publish-python-package"
  description     = "A GitHub action to publish a Python package to Google Artifact Registry"
  topics          = ["python", "github-actions"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
  visibility      = "internal"
}

module "generate-python-client" {
  source          = "./modules/apella-secure-repo"
  name            = "generate-python-client"
  description     = "A GitHub action to generate a python client package from a FastAPI service"
  topics          = ["python", "github-actions"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
  visibility      = "internal"
}

module "setup-helm" {
  source         = "./modules/apella-secure-repo"
  name           = "setup-helm"
  description    = "Repository to store a shared github action for setting up Helm"
  topics         = ["helm", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "validate-helm" {
  source         = "./modules/apella-secure-repo"
  name           = "validate-helm"
  description    = "Repository to store a shared github action for validating a typescript project"
  topics         = ["helm", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "publish-helm-chart" {
  source         = "./modules/apella-secure-repo"
  name           = "publish-helm-chart"
  description    = "Repository to store a shared github action for publishing a helm chart"
  topics         = ["helm", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "setup-argocd-cli" {
  source         = "./modules/apella-secure-repo"
  name           = "setup-argocd-cli"
  description    = "Repository to store a shared github action for setting up argocd cli"
  topics         = ["helm", "github-actions", "argocd-cli"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

module "setup-pluto" {
  source          = "./modules/apella-secure-repo"
  name            = "setup-pluto"
  description     = "Repository to store a shared github action for setting up pluto"
  topics          = ["github-actions", "k8s"]
  github_team_id  = module.platform_services_team.github_team.id
  visibility      = "internal"
  linear_prefixes = local.platform_services_linear_prefixes
}

module "build-docker-container" {
  source          = "./modules/apella-secure-repo"
  name            = "build-docker-container"
  description     = "Repository to store a shared github action for creating and publishing a docker image"
  topics          = ["github-actions", "docker"]
  github_team_id  = module.platform_services_team.github_team.id
  visibility      = "internal"
  linear_prefixes = local.platform_services_linear_prefixes
}

module "reset-repo-branch" {
  source          = "./modules/apella-secure-repo"
  name            = "reset-repo-branch"
  description     = "Repository to store a shared github action for reseting a git branch"
  topics          = ["github-actions", "git"]
  github_team_id  = module.platform_services_team.github_team.id
  visibility      = "internal"
  linear_prefixes = local.platform_services_linear_prefixes
}

module "validate-terraform" {
  source         = "./modules/apella-secure-repo"
  name           = "validate-terraform"
  description    = "A shared Github workflow for validating Terraform modules "
  topics         = ["terraform", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
}

# The validate-terraform repo has shared Github workflows. To allow other repos to use them, we must
# grant organization-level permission to do so.
resource "github_actions_repository_access_level" "validate_terraform_actions_access_level" {
  access_level = "organization"
  repository   = module.validate-terraform.repo.name
}

/************************************************
  Archived Repositories
************************************************/

module "proc-media-asset-deletion" {
  source          = "./modules/apella-secure-repo"
  name            = "proc-media-asset-deletion"
  description     = "Repository to batch and handle the deletion of media assets from cloud storage"
  topics          = ["python", "helm", "k8s", "processor"]
  github_team_id  = module.platform_services_team.github_team.id
  linear_prefixes = local.platform_services_linear_prefixes
  archived        = true
}

module "validate-typescript" {
  source         = "./modules/apella-secure-repo"
  name           = "validate-typescript"
  description    = "Repository to store a shared github action for validating a typescript project"
  topics         = ["node", "javascript", "github-actions"]
  github_team_id = module.platform_services_team.github_team.id
  visibility     = "internal"
  archived       = true
}
