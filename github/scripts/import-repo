#! /usr/bin/env bash

if [[ $# -lt 2 ]]; then
    echo "Usage: $0 <module-name> <repo-name>"
    exit 1
fi

if [ -z "$GITHUB_TOKEN" ]; then
    echo "\$GITHUB_TOKEN is not defined. Please export and re-run."
    exit 1
fi

echo "Initializing terraform"
terraform init

echo "Importing module '$1' as repo '$2'"
terraform import module.${1}.github_repository.repo ${2}

echo "Importing '$2' branch protection settings"
terraform import module.${1}.github_branch_protection.require_checks_and_review ${2}:main
