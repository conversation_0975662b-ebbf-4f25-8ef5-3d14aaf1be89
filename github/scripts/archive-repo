#! /usr/bin/env bash

if [[ $# -lt 1 ]]; then
    echo "Usage: $0 <module-name>"
    exit 1
fi

# Get the directory where this script is located
SCRIPT_DIR=$(dirname "$(realpath "$0")")

# Get the parent directory of where the script is located
PARENT_SCRIPT_DIR=$(dirname "$SCRIPT_DIR")

# Get the current working directory
CURRENT_DIR=$(realpath "$(pwd)")

# Compare the current directory to the parent of the script directory
if [ "$CURRENT_DIR" != "$PARENT_SCRIPT_DIR" ]; then
    echo "You need to be in $PARENT_SCRIPT_DIR to run this script."
    exit 1
fi

# Check if GitHub CLI is installed
if command -v gh >/dev/null 2>&1; then
    echo "GitHub CLI is installed."
else
    echo "GitHub CLI is not installed."
    echo "You can install it by following the instructions at https://github.com/cli/cli#installation"
fi

echo "Initializing terraform"
terraform init

echo "Extracting Repository name from module $1"
REPO_FULL_NAME=$(terraform state show module.$1.github_repository.repo \
    | grep 'full_name' | awk '{print $3}' | tr -d '"')

echo "Removing github repo module '$1' from terraform state"
terraform state rm module.${1}

# Check if the REPO_FULL_NAME environment variable is set
if [ -z "$REPO_FULL_NAME" ]; then
    echo "The REPO_FULL_NAME environment variable is not set."
else
    echo "Archiving the repository: $REPO_FULL_NAME"
    # Use the GitHub CLI to archive the repository
    gh repo archive $REPO_FULL_NAME
fi