# Apella Infrastructure

This repository contains the infrastructure as code for Apella's infrastructure

## Getting Started

To get started, you'll need to install Terraform and configure your environment.
This is one way to do that:
```shell
brew tap hashicorp/tap
```

```shell
brew install hashicorp/tap/terraform
```

## Code Organization

The Terraform modules in this repository are organized as monorepo with subdirectories which contain
`.tf` files treated as individual Terraform projects, each with their own lockfiles. 

To manage these modules, you'll generally need to `cd` into the directory or use the
`-chdir=<directory>` option to the `terraform` CLI.

As you iterate on a module, make sure to run `terraform fmt` and `terraform validate` locally to
ensure that your code will pass checks in Github CI.

## Upgrading Dependencies

It's always a good idea to keep our TF modules as up to date with upstream modules and providers as
possible, subject to the version constraints configured in the module.

To do so, run the following commands in the directory of the module whose dependencies wish to
update:

```shell
terraform init -upgrade
terraform providers lock -platform=linux_amd64
```

If you don't run `terraform providers lock ...`, you'll see an error in CI that looks something like
this:

```
│ Error: missing or corrupted provider plugins:
│   - registry.terraform.io/hashicorp/archive: the cached package for registry.terraform.io/hashicorp/archive 2.4.2 (in .terraform/providers) does not match any of the checksums recorded in the dependency lock file
│   - registry.terraform.io/hashicorp/google: the cached package for registry.terraform.io/hashicorp/google 5.21.0 (in .terraform/providers) does not match any of the checksums recorded in the dependency lock file
Error: Terraform exited with code 1.
```

This is due to [a limitation in Terraform](https://github.com/hashicorp/terraform/issues/32809)
which exists up until at least v1.7.
