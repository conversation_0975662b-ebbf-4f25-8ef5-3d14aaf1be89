terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "googleworkspace"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    googleworkspace = {
      source  = "hashicorp/googleworkspace"
      version = "~> 0.7.0"
    }
  }
}

provider "googleworkspace" {
  credentials       = var.workspace_credentials
  customer_id       = var.workspace_customer_id
  oauth_credentials = var.workspace_credentials
}
