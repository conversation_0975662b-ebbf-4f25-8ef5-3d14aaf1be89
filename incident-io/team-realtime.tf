data "incident_user" "jesse" {
  email = "<EMAIL>"
}

data "incident_user" "na" {
  email = "<EMAIL>"
}

data "incident_user" "sam" {
  email = "<EMAIL>"
}

data "incident_user" "juandiego" {
  email = "<EMAIL>"
}

module "realtime_team" {
  source = "./modules/incident-team"


  team_name = "Realtime"
  oncall_member_ids = [
    data.incident_user.sam.id,
    data.incident_user.na.id,
    data.incident_user.jesse.id,
  ]
  escalation_user_id = data.incident_user.juandiego.id
  slack_channel_id   = "C03UNLWQGRZ" # team-realtime-alert-prod
}
