locals {
  # Choosing an arbitrary Monday morning so that schedules run from Monday->Monday.
  schedule_start_time = "2025-04-21T00:00:00Z"
}

resource "incident_schedule" "team_schedule" {
  count    = var.disable_on_call_schedule ? 0 : 1
  name     = "${var.team_name} Team"
  timezone = "America/New_York"
  holidays_public_config = {
    country_codes = ["US"]
  }

  rotations = [
    {
      id   = "team-${lower(replace(var.team_name, " ", "-"))}-schedule"
      name = "Rotation"
      versions = [
        {
          effective_from = local.schedule_start_time
          users          = var.oncall_member_ids

          layers = [
            {
              name = "Primary"
              id   = "team-${lower(replace(var.team_name, " ", "-"))}-primary"
            },
          ]

          handover_start_at = local.schedule_start_time
          handovers = [
            {
              interval_type = "weekly"
              interval      = 1
            },
          ]
        },
      ]
    },
  ]
}

