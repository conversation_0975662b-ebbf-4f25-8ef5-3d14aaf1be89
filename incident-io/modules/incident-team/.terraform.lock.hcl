# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/incident-io/incident" {
  version     = "5.4.2"
  constraints = "~> 5.4.1"
  hashes = [
    "h1:Q8tZAIj2y/zfECKFxlexTFQy0bhgYDRDxf0F5/Zeppc=",
    "zh:03d573810b1432a7be2c1e381d2afdca4230b442ee64eeef4ebd14f9b9e6772a",
    "zh:07f34b7be2951b52747e27e82ee7a64cf63575b2e218baba5a805f3f0a576a7f",
    "zh:126e96596c7517a5e513427af4f8a899ba464543cead7afb753e4c02131de425",
    "zh:22694a76053c0bf65a8f1b652939423ac99313fd996600dfa9616b9ccb87f27c",
    "zh:3adb1eaf1866179a6942cdfbe083038387d7063bcf16adfb1f4f6804c871b1c4",
    "zh:4499a602864fd6e7df10b340bd2f74db2185f8def67d88b10258171c65e1e0fc",
    "zh:5777d94c64a1387d7fab0368a514a3a147e7b96a21c0ea43dd20e10c481942e3",
    "zh:5b45bbc6df08977254a47b3ed3068f8897f6eb50d1fc212dd7e3174d60679a82",
    "zh:786d061f42d5d76d75d1b81b4eb2e1c98b6fbce229922b8a18e84073c7216247",
    "zh:8ce9eed9709153472b61fe47545c1844a9b0785782a8f7e0efb65bbc064f36e9",
    "zh:94d914b62381dd70f63dd9740b9235c3ab7526f3e76df84d7ea646b7d50c4b4f",
    "zh:9b9b3bd47eca0f233eb39e02e6d16dac8223d63e8ff2dcbade806bc39b9bc0f2",
    "zh:ecf20415a53a87b735fd56dbaa85e940bda16e992303a285973687e9701beef3",
    "zh:f583fc0fa6b25eb8ac88e90a73d25e124ede567d20d6ae68ddfc8981c93582ce",
    "zh:f809ab383cca0a5f83072981c64208cbd7fa67e986a86ee02dd2c82333221e32",
  ]
}
