variable "team_name" {
  description = "The name of the team."
  type        = string
}

variable "schedule_versions" {
  description = "A list of schedule versions with their effective dates and user assignments."
  type = list(object({
    effective_from = string
    users          = list(string)
  }))
}

variable "escalation_user_id" {
  description = "The user ID to escalate to if no team member acknowledges the alert."
  type        = string
}

variable "working_hours" {
  description = "Configuration for the team's working hours, including time zones and weekday intervals."
  type = list(object({
    id       = string
    name     = string
    timezone = string
    weekday_intervals = list(object({
      end_time   = string
      start_time = string
      weekday    = string
    }))
  }))
  default = [
    {
      id       = "default"
      name     = "Working Hours"
      timezone = "America/New_York"
      weekday_intervals = [
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "monday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "tuesday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "wednesday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "thursday"
        },
        {
          end_time   = "21:00"
          start_time = "07:00"
          weekday    = "friday"
        },
      ]
    },
  ]
}

variable "time_to_ack_seconds" {
  description = "The time (in seconds) allowed for acknowledging an alert."
  type        = number
  default     = 900
}

variable "slack_channel_id" {
  description = "The Slack channel ID where alert notifications will be sent."
  type        = string
}

variable "disable_on_call_schedule" {
  description = "Set to true to disable the creation of an on-call schedule. Use this if the team requires a custom schedule."
  type        = bool
  default     = false
}

variable "disable_escalation_policy" {
  description = "Set to true to disable the creation of an escalation policy. Use this if the team requires a custom policy."
  type        = bool
  default     = false
}

variable "schedule_id_override" {
  description = "Provide a schedule ID to use in the escalation policy if the on-call schedule is disabled but the escalation policy is enabled."
  type        = string
  default     = null
  validation {
    condition     = (!var.disable_on_call_schedule || var.disable_escalation_policy) || var.schedule_id_override != null
    error_message = "A schedule ID must be provided if the on-call schedule is disabled but the escalation policy is enabled."
  }
}

variable "escalation_policy_id_override" {
  description = "Provide an escalation policy ID to use for the team if the escalation policy is disabled. Every team must have an escalation policy."
  type        = string
  default     = null
  validation {
    condition     = !var.disable_escalation_policy || var.escalation_policy_id_override != null
    error_message = "An escalation policy ID must be provided if the escalation policy is disabled."
  }
}
