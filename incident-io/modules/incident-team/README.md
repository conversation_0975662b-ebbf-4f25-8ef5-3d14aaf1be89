# Incident Team Module

This module is used to configure and manage incident response teams in Incident.io. It allows you to define team members, escalation paths, and Slack channels for effective incident management.

## Features

- **Team**: Define team
- **Schedule**: Define the team's on-call schedule.
- **Escalation Policy**: Set an escalation user for the team.

# Default Configuration

## Schedule

- **Working Hours**: 
  - Timezone: `America/New_York`
  - Weekday Intervals:
    - Monday to Friday: `07:00` to `21:00`


## Escalation Policy

- **Time to Acknowledge**: `900 seconds` (15 minutes)
- **Primary Path**:
  1. Page the on-call person.
  2. If unacknowledged, page the entire team.
  3. If still unacknowledged, escalate to the escalation user.

- **Fallback Path**:
  - If outside working hours:
    - Check if the alert is a P1 priority.
    - If P1, follow the primary path.
    - Otherwise, send a Slack alert to the configured channel. Escalate to a page once working hours begin.
