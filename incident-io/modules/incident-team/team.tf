data "incident_catalog_type" "team" {
  name = "Team"
}

data "incident_catalog_type_attribute" "team_escalation_path" {
  catalog_type_id = data.incident_catalog_type.team.id
  name            = "Escalation path"
}

resource "incident_catalog_entry" "team" {
  catalog_type_id = data.incident_catalog_type.team.id
  name            = var.team_name
  # For datadog alerts to route to a team, either the external_id or an alias must match the `team` tag.
  aliases = [lower(replace(var.team_name, " ", "-"))]

  attribute_values = [
    {
      attribute = data.incident_catalog_type_attribute.team_escalation_path.id
      value     = incident_escalation_path.team_escalation[0] != null ? incident_escalation_path.team_escalation[0].id : var.escalation_policy_id_override
    }
  ]
}
