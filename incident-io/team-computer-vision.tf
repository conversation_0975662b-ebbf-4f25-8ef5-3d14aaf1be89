data "incident_user" "nathan" {
  email = "<EMAIL>"
}

data "incident_user" "oren" {
  email = "<EMAIL>"
}

data "incident_user" "zac" {
  email = "<EMAIL>"
}

data "incident_user" "michael" {
  email = "<EMAIL>"
}

module "computer_vision_ops_team" {
  source = "./modules/incident-team"


  team_name = "Computer Vision"
  oncall_member_ids = [
    data.incident_user.nathan.id,
    data.incident_user.oren.id,
    data.incident_user.zac.id
  ]
  escalation_user_id = data.incident_user.michael.id
  slack_channel_id   = "C088J8G5NPJ" # bot-ops-computer-vision-prod
  working_hours = [
    {
      id       = "default"
      name     = "Working Hours"
      timezone = "America/New_York"
      weekday_intervals = [
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "monday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "tuesday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "wednesday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "thursday"
        },
        {
          end_time   = "20:00"
          start_time = "09:00"
          weekday    = "friday"
        },
      ]
    },
  ]
}
