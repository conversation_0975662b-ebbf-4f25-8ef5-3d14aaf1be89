data "incident_user" "celeste" {
  email = "<EMAIL>"
}

data "incident_user" "mike" {
  email = "<EMAIL>"
}

data "incident_user" "ziad" {
  email = "<EMAIL>"
}

data "incident_user" "maurice" {
  email = "<EMAIL>"
}

module "clinical_data_team" {
  source = "./modules/incident-team"

  team_name = "Clinical Data"
  schedule_versions = [
    {
      effective_from = "2025-04-21T00:00:00Z"
      users = [
        data.incident_user.celeste.id,
        data.incident_user.mike.id,
        data.incident_user.ziad.id,
        data.incident_user.maurice.id,
      ]
    }
  ]
  escalation_user_id = data.incident_user.dorian.id
  slack_channel_id   = "C05EGHNTMSS" # team-ehr-interfaces-alert-prod
}
