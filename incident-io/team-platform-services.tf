data "incident_user" "abhay" {
  email = "<EMAIL>"
}

data "incident_user" "christopher" {
  email = "<EMAIL>"
}

data "incident_user" "james" {
  email = "<EMAIL>"
}

data "incident_user" "dorian" {
  email = "<EMAIL>"
}

module "platform_services_team" {
  source = "./modules/incident-team"


  team_name = "Platform Services"
  oncall_member_ids = [
    data.incident_user.james.id,
    data.incident_user.christopher.id,
    data.incident_user.abhay.id,
  ]
  escalation_user_id = data.incident_user.dorian.id
  slack_channel_id   = "C03TU8EFGDT" # team-platform-services-alert-prod
}
