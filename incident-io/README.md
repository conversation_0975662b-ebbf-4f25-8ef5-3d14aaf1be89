# incident.io Terraform

This directory manages incident.io catalog resources using Terraform and the [incident-io/incident](https://registry.terraform.io/providers/incident-io/incident/latest) provider.

incident.io is an on-call and incident management platform.

## Structure

- **Catalog Types**: Define categories like Service, Team, Incident Severity, Alert Priority, Escalation Path, and Schedule.
- **Catalog Type Attributes**: Define fields for each type (e.g., Description).
- **Catalog Entries**: Define individual instances (e.g., a specific service or team).
