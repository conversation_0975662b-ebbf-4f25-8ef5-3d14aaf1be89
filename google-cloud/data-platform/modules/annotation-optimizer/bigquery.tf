locals {
  annotation_needs_table_schema = <<EOF
      [
          {
              "name": "run_id",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "Unique identifier for the run"
          },
          {
              "name": "run_timestamp",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The timestamp this run started"
          },
          {
              "name": "created_at",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "Timestamp this row was first created"
          },
          {
              "name": "updated_at",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "Timestamp this row was updated"
          },
          {
              "name": "deleted_at",
              "type": "TIMESTAMP",
              "mode": "NULLABLE",
              "description": "Timestamp this row was deleted"
          },
          {
              "name": "room_id",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "The room id for this need"
          },
          {
              "name": "event_type_id",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "The event_type_id (or 'all') that needs annotating"
          },
          {
              "name": "start_time",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The start of the time range that needs annotating"
          },
          {
              "name": "end_time",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The end of the time range that needs annotating"
          },
          {
              "name": "category",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "The category of the need (image outage, low confidence, etc)"
          },
          {
              "name": "description",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "A specific description, like 'patient draped confidence 0.35'"
          },
          {
              "name": "priority",
              "type": "FLOAT",
              "mode": "REQUIRED",
              "description": "The priority of the need (0-1), where 0 means ignore this row"
          }
      ]
      EOF

  # I don't know if we want to include some sort of "success" column here, but we could add it.
  # I am also not including any "room list" or "event type list" columns, since I think it would
  # add a lot of bloat and the plan is to run for all rooms for all events, since that is the
  # more efficient way to query the data.
  annotation_needs_run_info_schema = <<EOF
      [
          {
              "name": "run_id",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "Unique identifier for the run"
          },
          {
              "name": "run_timestamp",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The timestamp this run started"
          },
          {
              "name": "run_description",
              "type": "STRING",
              "mode": "REQUIRED",
              "description": "A description of the run"
          },
          {
              "name": "range_start_time",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The start of the time range that needs were calculated for"
          },
          {
              "name": "range_end_time",
              "type": "TIMESTAMP",
              "mode": "REQUIRED",
              "description": "The end of the time range that needs were calculated for"
          }
      ]
      EOF
}

resource "google_bigquery_dataset" "annotation_needs_dataset" {
  project    = var.project_id
  dataset_id = "annotation_needs"

  # Even though we have a history table below, we also want to keep history for
  # the main table, in case we need to debug interactions between runs.  Unfortunately
  # this cannot be set on a single table.
  max_time_travel_hours = 168 # 7 days
}


resource "google_bigquery_table" "annotation_needs_main_table" {
  # This is the main table for annotation needs that will be used by the annotation task
  # management system
  project             = var.project_id
  dataset_id          = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  table_id            = "annotation_needs"
  deletion_protection = true

  time_partitioning {
    # This is partitioned by start_time, because generally the api server will query for
    # a specific 6 hour time range, usually within 24 hours of "now"
    field = "start_time"
    type  = "DAY"
  }

  # Cluster by room_id, as the api server is likely to be querying by room_id
  clustering = [
    "room_id"
  ]

  # We want to require that the query has a partition filter, so that we don't accidentally
  # scan the entire table
  require_partition_filter = true

  schema = local.annotation_needs_table_schema
}

resource "google_bigquery_table" "annotation_needs_history_table" {
  # This table will contain all runs of the annotation optimizer.
  project             = var.project_id
  dataset_id          = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  table_id            = "annotation_needs_history"
  deletion_protection = true

  time_partitioning {
    # This table is partitioned by run_timestamp, because generally we will be looking
    # at the what happened for a specific run that lead up to a task being created or skipped.
    field = "run_timestamp"
    type  = "DAY"
  }

  # We want to require that the query has a partition filter, so that we don't accidentally
  # scan the entire table
  require_partition_filter = true

  schema = local.annotation_needs_table_schema
}

resource "google_bigquery_table" "annotation_needs_experiments_table" {
  # This table will hold any sort of experimental annotation runs.  It is kept separate from
  # the other tables so that any experimental runs do not interfere with the main run.
  project             = var.project_id
  dataset_id          = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  table_id            = "annotation_needs_experiments"
  deletion_protection = true

  time_partitioning {
    # This table is partitioned by run_timestamp, because generally we will be looking
    # at the what happened for a specific run that lead up to a task being created or skipped.
    field = "run_timestamp"
    type  = "DAY"
  }

  # We want to require that the query has a partition filter, so that we don't accidentally
  # scan the entire table
  require_partition_filter = true

  schema = local.annotation_needs_table_schema
}

resource "google_bigquery_table" "annotation_needs_run_info" {
  # This table will hold the run info for the main annotation optimizer
  project             = var.project_id
  dataset_id          = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  table_id            = "annotation_needs_run_info"
  deletion_protection = true

  time_partitioning {
    # This table is partitioned by run_timestamp, because generally we will be looking
    # at the what happened for a specific run that lead up to a task being created or skipped.
    field = "run_timestamp"
    # Partitioning by month since we we will have only one row
    # per hour.
    type = "MONTH"
  }

  # We want to require that the query has a partition filter, so that we don't accidentally
  # scan the entire table
  require_partition_filter = true

  schema = local.annotation_needs_run_info_schema
}

resource "google_bigquery_table" "annotation_needs_experiments_run_info" {
  # This table will hold the run info for the annotation optimizer experiments
  project             = var.project_id
  dataset_id          = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  table_id            = "annotation_needs_experiments_run_info"
  deletion_protection = true

  # This table does not require partitioning since we will not be running a lot of
  # experiments.

  schema = local.annotation_needs_run_info_schema
}

