
# The annotation needs are a dagster job in mlops-dags,
# so mlops-dags service account needs to be able to write to the table.
resource "google_bigquery_dataset_iam_member" "mlops_dags_writes_data" {
  dataset_id = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  member     = "serviceAccount:${var.mlops_dags_sa_email}"
  role       = "roles/bigquery.dataEditor"
}

# There are multiple service accounts that need read access to the annotation needs dataset.
resource "google_bigquery_dataset_iam_member" "accessors_read_data" {
  for_each   = toset(var.accessor_sa_emails)
  dataset_id = google_bigquery_dataset.annotation_needs_dataset.dataset_id
  member     = "serviceAccount:${each.value}"
  role       = "roles/bigquery.dataViewer"
}