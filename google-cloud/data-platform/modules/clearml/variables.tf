variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

/*****************************************************************************
  Clearml Key
******************************************************************************/

variable "clearml_api_key" {
  description = "Api key token for clearml"
  type        = string
  sensitive   = true
}

variable "clearml_api_secret" {
  description = "Api secret token for clearml"
  type        = string
  sensitive   = true
}
