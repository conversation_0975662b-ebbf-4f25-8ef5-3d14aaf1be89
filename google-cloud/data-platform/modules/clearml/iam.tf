resource "google_secret_manager_secret" "clearml_api_key_secret" {
  secret_id = "${var.environment}-clearml-api-key"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret" "clearml_api_secret_secret" {
  secret_id = "${var.environment}-clearml-api-secret"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "clearml_api_key_version" {
  secret = google_secret_manager_secret.clearml_api_key_secret.id

  secret_data = var.clearml_api_key
}
resource "google_secret_manager_secret_version" "clearml_api_secret_version" {
  secret = google_secret_manager_secret.clearml_api_secret_secret.id

  secret_data = var.clearml_api_secret
}
