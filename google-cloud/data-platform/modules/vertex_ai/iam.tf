# Give vertex ai permission to access customer buckets

resource "google_storage_bucket_iam_member" "vertex_ai_reads_video_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-video-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${var.vertex_ai_sa_email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_reads_image_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-image-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${var.vertex_ai_sa_email}"
}
