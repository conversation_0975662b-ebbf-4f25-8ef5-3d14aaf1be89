resource "google_service_account" "service_account" {
  project      = var.project_id
  account_id   = "decodable-service-account"
  display_name = "Decodable service account"
}

resource "google_service_account_key" "decodable_service_account_key" {
  service_account_id = google_service_account.service_account.id
}

resource "google_secret_manager_secret" "decodable_service_account_key_secret" {
  project   = var.project_id
  secret_id = "decodable_service_account_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "decodable_service_account_key_secret_version" {
  secret      = google_secret_manager_secret.decodable_service_account_key_secret.id
  secret_data = base64decode(google_service_account_key.decodable_service_account_key.private_key)
}

resource "google_secret_manager_secret_iam_member" "decodable_service_account_key_read_access" {
  secret_id = google_secret_manager_secret.decodable_service_account_key_secret.id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.deployment_service_account_email}"
}

resource "google_pubsub_subscription" "event_model_subscriptions" {
  for_each = toset(var.event_model_pubsub_topics)
  name     = "decodable-${each.value}-subscription"
  topic    = each.value
  # 1.5 hours - this reflects the context window of the event model + padding
  message_retention_duration = "5400s"
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  expiration_policy {
    ttl = ""
  }
}

resource "google_pubsub_subscription_iam_member" "event_model_subscriptions_access" {
  for_each     = google_pubsub_subscription.event_model_subscriptions
  subscription = each.value.name
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${google_service_account.service_account.email}"
}

resource "google_pubsub_subscription_iam_member" "event_model_subscriptions_viewer" {
  for_each     = google_pubsub_subscription.event_model_subscriptions
  subscription = each.value.name
  role         = "roles/pubsub.viewer"
  member       = "serviceAccount:${google_service_account.service_account.email}"
}
