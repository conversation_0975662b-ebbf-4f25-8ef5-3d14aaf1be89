/***********************************************************************
Global Variables
***********************************************************************/

variable "project_id" {
  description = "GCP Project ID"
}

variable "environment" {
  description = "The environment prefix to append to external variable names"
}

variable "region" {
  description = "The GCP Region to create Google resources inside of"
}

/***********************************************************************
Datadog
***********************************************************************/

variable "datadog_api_key" {
  description = "The parent project's API key"
}

variable "datadog_app_key" {
  description = "The parent project's app key"
}

/***********************************************************************
Pub/Sub
***********************************************************************/

variable "event_model_pubsub_topics" {
  type        = list(string)
  description = "Pub/Sub topics to set up subscriptions to for the event model"
}

/***********************************************************************
Alerts
***********************************************************************/

variable "oncall_team_critical_alert_handle" {
  description = "The critical alert handle for the Data Platform pod"
  type        = string
}

/***********************************************************************
Deployment
***********************************************************************/

variable "deployment_service_account_email" {
  description = "The service account email to use for deployment"
}
