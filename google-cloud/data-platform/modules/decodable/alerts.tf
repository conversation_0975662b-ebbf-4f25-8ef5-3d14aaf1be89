locals {
  alert_message = <<EOT
See failure in the Decodable UI:

https://app.decodable.co/apella-${var.environment}/{{resource_type.name}}s/{{resource_id.name}}
${var.oncall_team_critical_alert_handle}
EOT
  tags = [
    "team:data-platform",
    "env:${var.environment}"
  ]
}


resource "datadog_monitor" "checkpoints_not_completing_alert" {
  include_tags    = false
  new_group_delay = 0
  monitor_thresholds {
    critical          = 1
    critical_recovery = 4
  }
  name    = "${var.environment}: Checkpoints not completing for decodable resource {{resource_name.name}}"
  type    = "query alert"
  query   = <<EOT
max(last_10m):per_minute(max:decodable_checkpoints_completed{env:${var.environment} and not resource_name:sandbox* and resource_name not in ("event_model_features", "customer_image_embedding_outputs_pubsub_connection_source")} by {resource_name,resource_type,resource_id}) < 1
EOT
  message = local.alert_message
  tags    = local.tags
}


resource "datadog_monitor" "unhealthy_transition_alert" {
  include_tags        = false
  require_full_window = false
  on_missing_data     = "show_no_data"
  name                = "${var.environment}: Unhealthy transition state detected for decodable resource {{resource_name.name}}"
  type                = "query alert"
  query               = <<EOT
sum(last_15m):sum:decodable_unhealthy_transition_event{env:${var.environment} and not resource_name:sandbox*} by {resource_id,resource_type,resource_name} >= 1
EOT
  message             = local.alert_message
  tags                = local.tags
}

resource "datadog_monitor" "datadog_connection_missing_checkpoints" {
  include_tags        = false
  on_missing_data     = "show_and_notify_no_data"
  require_full_window = false
  name                = "${var.environment}: Decodable Datadog connection data missing"
  type                = "query alert"
  query               = <<EOT
max(last_10m):per_minute(max:decodable_checkpoints_completed{env:${var.environment} AND ( resource_name:datadog_connection or resource_name:datadog_transition_metrics_connection )} by {resource_id,resource_name,resource_type}) < 1
EOT
  message             = local.alert_message
  tags                = local.tags
}

resource "datadog_downtime_schedule" "decodable_datadog_connection_data_missing" {
  count                            = var.environment == "dev" ? 1 : 0
  scope                            = "env:${var.environment}"
  display_timezone                 = "UTC"
  message                          = ""
  mute_first_recovery_notification = false

  monitor_identifier {
    monitor_id = datadog_monitor.datadog_connection_missing_checkpoints.id
  }

  recurring_schedule {
    timezone = "UTC"

    recurrence {
      duration = "13h"
      rrule    = "FREQ=DAILY;INTERVAL=1;BYDAY=MO,TU,WE,TH,FR"
      start    = "2025-01-03T01:00:00"
    }

    recurrence {
      duration = "2d"
      rrule    = "FREQ=WEEKLY;INTERVAL=1;BYDAY=SA"
      start    = "2025-01-03T01:00:00"
    }
  }
}

resource "datadog_monitor" "datastream_vs_decodable_object_count_sec" {
  evaluation_delay    = 300
  include_tags        = false
  require_full_window = false
  name                = "${var.environment}: Decodable has low postgres source record count"
  type                = "query alert"
  query               = <<EOT
avg(last_30m):sum:decodable_records_out_per_second{env:${var.environment}, resource_name:source_postgres_web_api}.rollup(avg, 600) / throughput(sum:gcp.datastream.streamobject.event_count{project_id:${var.project_id} and (object_name in (public_events, public_cases, public_case_forecast, public_phases, public_observations, public_sites, public_blocks, public_block_releases, public_rooms, public_block_surgeons, public_case_staff)) and dd_resource_key:* and stream_id:postgres-to-bigquery-stream}.rollup(sum, 600)) < 0.8
EOT
  message             = <<EOT
Compared to Datastream metrics, Decodable is getting lower throughput than expected in public__ streams.

${var.oncall_team_critical_alert_handle}
EOT
  tags                = local.tags
}

resource "datadog_monitor" "records_lag_not_going_to_zero" {
  include_tags        = false
  new_group_delay     = 60
  on_missing_data     = "show_no_data"
  require_full_window = true
  name                = "${var.environment}: Decodable record lag detected for {{resource_name.name}}"
  type                = "query alert"
  query               = <<EOT
min(last_4h):sum:decodable_records_lag_total{env:${var.environment} and not resource_name:sandbox*} by {resource_id,resource_name,resource_type} > 0
EOT
  message             = <<EOT
${local.alert_message}
This pipeline likely can’t keep up with the rate at which messages are being produced. To resolve this, increase the number of tasks and/or task size on the pipeline.
EOT
  tags                = local.tags
}
