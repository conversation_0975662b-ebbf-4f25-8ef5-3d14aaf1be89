locals {
  forecasting_feature_store_topic_name        = "${var.environment}-forecasting-feature-store-topic"
  shared_forecasting_feature_store_topic_name = "${var.environment}-shared-forecasting-feature-store-topic"
  event_model_features_topic_name             = "${var.environment}-event-model-features-topic"

  output_topics = [
    local.forecasting_feature_store_topic_name,
    local.shared_forecasting_feature_store_topic_name,
    local.event_model_features_topic_name
  ]

  # Define schemas for topics that need them
  # NOTE: DO NOT BUILD ON THIS PATTERN FOR WRITING TO BIGQUERY. If you need to sink to BigQuery from Decodable Pub/Sub, please reach out to #guild-data-delegates
  topic_schemas = {
    (local.event_model_features_topic_name) = {
      name = "event_model_features_schema_v3"
      type = "AVRO"
      definition = jsonencode({
        name = "event_model_features"
        type = "record"
        fields = [
          {
            name = "room_id"
            type = "string"
          },
          {
            name = "time_window_start_utc"
            type = "string"
          },
          {
            name = "time_window_end_utc"
            type = "string"
          },
          {
            name = "updated_time"
            type = "string"
          },
          {
            name = "float_features"
            type = {
              type = "array"
              items = {
                name = "float_feature"
                type = "record"
                fields = [
                  {
                    name = "name"
                    type = "string"
                  },
                  {
                    name = "value"
                    type = "float"
                  }
                ]
              }
            }
          },
          {
            name = "float_array_features"
            type = {
              type = "array"
              items = {
                name = "float_array_feature"
                type = "record"
                fields = [
                  {
                    name = "name"
                    type = "string"
                  },
                  {
                    name = "value"
                    type = {
                      type  = "array"
                      items = "float"
                    }
                  }
                ]
              }
            }
          }
        ]
      })
    }
  }
}

# Create schemas for topics that have them defined
resource "google_pubsub_schema" "topic_schemas" {
  for_each = local.topic_schemas

  name       = each.value.name
  type       = each.value.type
  definition = each.value.definition
}

resource "google_pubsub_topic" "output_topics" {
  for_each = toset(local.output_topics)

  name = each.value
  message_storage_policy {
    allowed_persistence_regions = [
      var.region,
    ]
  }
  message_retention_duration = "604800s"

  # Set schema if one is defined for this topic
  dynamic "schema_settings" {
    for_each = contains(keys(local.topic_schemas), each.value) ? [1] : []
    content {
      schema   = "projects/${var.project_id}/schemas/${google_pubsub_schema.topic_schemas[each.value].name}"
      encoding = "JSON"
    }
  }
}

resource "google_pubsub_topic_iam_member" "output_topics_editors" {
  for_each = google_pubsub_topic.output_topics
  role     = "roles/pubsub.editor"
  topic    = each.value.name
  member   = "serviceAccount:${google_service_account.service_account.email}"
}
