/***********************************************************************
Create Datadog API key
***********************************************************************/

resource "datadog_api_key" "api_key" {
  name = "${var.environment}-decodable"
}

/***********************************************************************
Create a GCP secret for it
***********************************************************************/

resource "google_secret_manager_secret" "api_key_secret" {
  project   = var.project_id
  secret_id = "decodable_datadog_api_key"

  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

/***********************************************************************
Populate the GCP secret with the API key
***********************************************************************/

resource "google_secret_manager_secret_version" "api_key_secret_version" {
  secret      = google_secret_manager_secret.api_key_secret.id
  secret_data = datadog_api_key.api_key.key
}
