variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

/*****************************************************************************
  Amplitude Keys
******************************************************************************/

variable "amplitude_dashboard_api_key" {
  description = "Api key for Amplitude Dashboard project"
  type        = string
  sensitive   = true
}

variable "amplitude_dashboard_secret_key" {
  description = "Secret key for Amplitude Dashboard project"
  type        = string
  sensitive   = true
}

variable "amplitude_secret_accessors" {
  description = "List of accounts to sign up for access to Amplitude secrets"
  type        = list(string)
}