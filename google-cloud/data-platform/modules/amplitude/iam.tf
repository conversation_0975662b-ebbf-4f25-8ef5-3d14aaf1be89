locals {
  accessor_members = [
    for email in var.amplitude_secret_accessors : "serviceAccount:${email}"
  ]
}

resource "google_secret_manager_secret" "amplitude_dashboard_api_key_secret" {
  secret_id = "${var.environment}-amplitude-dashboard-api-key"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret" "amplitude_dashboard_secret_key_secret" {
  secret_id = "${var.environment}-amplitude-dashboard-secret-key"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "amplitude_dashboard_api_key_version" {
  secret = google_secret_manager_secret.amplitude_dashboard_api_key_secret.id

  secret_data = var.amplitude_dashboard_api_key
}

resource "google_secret_manager_secret_version" "amplitude_dashboard_secret_key_version" {
  secret = google_secret_manager_secret.amplitude_dashboard_secret_key_secret.id

  secret_data = var.amplitude_dashboard_secret_key
}

resource "google_secret_manager_secret_iam_member" "amplitude_dashboard_api_key_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.amplitude_dashboard_api_key_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}

resource "google_secret_manager_secret_iam_member" "amplitude_dashboard_secret_key_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.amplitude_dashboard_secret_key_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}