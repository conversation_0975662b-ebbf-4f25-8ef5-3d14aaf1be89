########################################################################
# Event Model Decodable Features outputs
########################################################################

locals {
  create_event_model_decodable_features = var.event_model_features_topic_name != null
}

module "event_model_decodable_features_to_bigquery_subscription" {
  count   = local.create_event_model_decodable_features ? 1 : 0
  source  = "app.terraform.io/apella/pubsub-subscription-with-deadletter/google"
  version = "0.1.1"

  project_id             = var.project_id
  topic_id               = var.event_model_features_topic_name
  subscription_base_name = "event-model-decodable-features-to-bigquery"
  labels                 = var.labels
  environment            = var.environment

  bigquery_config = {
    table               = var.realtime_event_model_decodable_features_table
    use_topic_schema    = true
    write_metadata      = true
    drop_unknown_fields = true
  }

  retry_policy = {
    minimum_backoff = "5s"
    maximum_backoff = "600s"
  }

  deadletter_enabled    = true
  max_delivery_attempts = 5
}

resource "google_pubsub_subscription_iam_member" "event_model_decodable_features_sa_subscriber_member" {
  count        = local.create_event_model_decodable_features ? 1 : 0
  project      = var.project_id
  member       = "serviceAccount:${var.dataflow_sa_email}"
  role         = "roles/pubsub.subscriber"
  subscription = module.event_model_decodable_features_to_bigquery_subscription[0].subscription_id
}

module "event_model_decodable_features_to_bigquery_alerts" {
  count  = local.create_event_model_decodable_features ? 1 : 0
  source = "../pubsub-subscription-alerting"

  enable_monitors = var.team_metadata.computer_vision.slack_channel != null || var.team_metadata.computer_vision.should_page

  human_readable_subscription_name                = "Event Model Decodable Features to BigQuery"
  age_of_unacked_messages_alert_threshold_seconds = 600
  age_of_unacked_messages_no_data_timeframe       = 60
  age_of_unacked_messages_averaging_window        = "last_15m"
  num_unacked_messages_alert_threshold            = 1800
  dead_lettered_count_alert_threshold             = 0
  link_to_playbook                                = "https://www.notion.so/apella/Event-Predictor-Playbook-11c8b6ae11af4cbf940bc0853240e06e"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id
  subscription_name  = module.event_model_decodable_features_to_bigquery_subscription[0].subscription_name
}
