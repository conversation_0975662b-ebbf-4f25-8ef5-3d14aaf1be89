########################################################################
# Object and color model outputs
########################################################################

#
# To write to BigQuery, we need a schema
#
resource "google_pubsub_schema" "image_processing_output_schema" {
  name       = "image_processing_output_schema"
  type       = "AVRO"
  definition = <<EOT
{
  "name": "image_processing_output",
  "type": "record",
  "fields" : [
    {"name": "guid", "type": "string"},
    {"name": "org_id", "type": "string"},
    {"name": "room_id", "type": "string"},
    {"name": "camera_id", "type": "string"},
    {"name": "source", "type": "string"},
    {"name": "frame_url", "type": "string"},
    {"name": "object_model_version", "type": "string"},
    {"name": "color_model_version", "type": "string"},
    {"name": "frame_time", "type": "string"},
    {
      "name": "object_model_output",
      "type": {
        "type": "array",
        "items": {
          "name": "object_detections",
          "type": "record",
          "fields" : [
              {"name": "label_display_name", "type": "string"},
              {"name": "confidence", "type": "float"},
              {"name": "x1", "type": "float"},
              {"name": "x2", "type": "float"},
              {"name": "y1", "type": "float"},
              {"name": "y2", "type": "float"}
          ]
        }
      }
    },
    {
      "name": "color_model_output",
      "type": {
        "name": "color_model_output",
        "type": "record",
        "fields": [
          {"name": "average_r", "type": "float"},
          {"name": "average_g", "type": "float"},
          {"name": "average_b", "type": "float"}
        ]
      }
    }
  ]
}
EOT
}

#
# This topic will hold all outputs from the realtime image processing
#
resource "google_pubsub_topic" "image_processing_outputs" {
  name   = "${var.environment}-customer-image-processing-outputs"
  labels = var.labels

  schema_settings {
    schema   = "projects/${var.project_id}/schemas/${google_pubsub_schema.image_processing_output_schema.name}"
    encoding = "JSON"
  }
}

module "image_processing_outputs_to_bigquery_subscription" {
  source  = "app.terraform.io/apella/pubsub-subscription-with-deadletter/google"
  version = "0.1.1"

  project_id             = var.project_id
  topic_id               = google_pubsub_topic.image_processing_outputs.id
  subscription_base_name = "image-processing-outputs-to-bigquery"
  labels                 = var.labels
  environment            = var.environment

  bigquery_config = {
    table               = var.realtime_image_processing_outputs_table
    use_topic_schema    = true
    write_metadata      = true
    drop_unknown_fields = true
  }

  retry_policy = {
    minimum_backoff = "5s"
    maximum_backoff = "600s"
  }

  deadletter_enabled    = true
  max_delivery_attempts = 5
}

resource "google_pubsub_subscription_iam_member" "dataflow_reads_image_processing_outputs_new" {
  project      = var.project_id
  subscription = module.image_processing_outputs_to_bigquery_subscription.subscription_id
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${var.dataflow_sa_email}"
}

module "image_processing_outputs_to_bigquery_deadletter_alerts" {
  source = "../pubsub-subscription-alerting"

  # Enable/disable based on whether monitoring keys/integrations are set
  enable_monitors = var.team_metadata.computer_vision.slack_channel != null || var.team_metadata.computer_vision.should_page

  human_readable_subscription_name = "Image Processing Outputs to BigQuery"

  age_of_unacked_messages_alert_threshold_seconds = 600
  age_of_unacked_messages_no_data_timeframe       = 60
  age_of_unacked_messages_averaging_window        = "last_15m"
  num_unacked_messages_alert_threshold            = 1800
  dead_lettered_count_alert_threshold             = 0

  link_to_playbook = "https://www.notion.so/apella/Image-Processor-Playbook-02ff0b51ea7a4a8597c5dfda363700fa"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id

  subscription_name = module.image_processing_outputs_to_bigquery_subscription.subscription_name
}


########################################################################
# Image embedding outputs
########################################################################

#
# To write to BigQuery, we need a schema
#
resource "google_pubsub_schema" "image_embedding_output_schema" {
  name       = "image_embedding_output_schema"
  type       = "AVRO"
  definition = <<EOT
{
  "name": "image_embedding_output",
  "type": "record",
  "fields" : [
    {"name": "guid", "type": "string"},
    {"name": "org_id", "type": "string"},
    {"name": "room_id", "type": "string"},
    {"name": "camera_id", "type": "string"},
    {"name": "source", "type": "string"},
    {"name": "frame_url", "type": "string"},
    {
      "name": "frame_time",
      "type": { "type": "long", "logicalType": "timestamp-micros" }
    },
    {"name": "model_name", "type": "string"},
    {"name": "model_version", "type": "string"},
    {
      "name": "embedding",
      "type": {
        "type": "array",
        "items": {
          "type": "float"
        }
      }
    }
  ]
}
EOT
}

#
# This topic will hold all image embeddings from the image processor service
#
resource "google_pubsub_topic" "image_embedding_outputs" {
  name = "${var.environment}-customer-image-embedding-outputs"

  schema_settings {
    schema   = "projects/${var.project_id}/schemas/${google_pubsub_schema.image_embedding_output_schema.name}"
    encoding = "JSON"
  }
}

module "image_embedding_outputs_to_bigquery_subscription" {
  source  = "app.terraform.io/apella/pubsub-subscription-with-deadletter/google"
  version = "0.1.1"

  project_id             = var.project_id
  topic_id               = google_pubsub_topic.image_embedding_outputs.id
  subscription_base_name = "image-embedding-outputs-to-bigquery"
  labels                 = var.labels
  environment            = var.environment

  bigquery_config = {
    table               = var.realtime_image_embeddings_outputs_table
    use_topic_schema    = true
    write_metadata      = true
    drop_unknown_fields = true
  }

  retry_policy = {
    minimum_backoff = "5s"
    maximum_backoff = "600s"
  }

  deadletter_enabled    = true
  max_delivery_attempts = 5
}

resource "google_pubsub_subscription_iam_member" "image_embedding_sa_subscriber_member_new" {
  project      = var.project_id
  member       = "serviceAccount:${var.dataflow_sa_email}"
  role         = "roles/pubsub.subscriber"
  subscription = module.image_embedding_outputs_to_bigquery_subscription.subscription_id
}

module "image_embedding_outputs_to_bigquery_alerts" {
  source = "../pubsub-subscription-alerting"

  enable_monitors = var.team_metadata.computer_vision.slack_channel != null || var.team_metadata.computer_vision.should_page

  human_readable_subscription_name                = "Image Embedding Outputs to BigQuery"
  age_of_unacked_messages_alert_threshold_seconds = 600
  age_of_unacked_messages_no_data_timeframe       = 60
  age_of_unacked_messages_averaging_window        = "last_15m"
  num_unacked_messages_alert_threshold            = 1800
  dead_lettered_count_alert_threshold             = 0
  link_to_playbook                                = "https://www.notion.so/apella/Image-Processor-Playbook-02ff0b51ea7a4a8597c5dfda363700fa"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id
  subscription_name  = module.image_embedding_outputs_to_bigquery_subscription.subscription_name
}
