########################################################################
# Dummy cloud function
#
# In order for a function to be declared in terraform, the code
# must exist in terraform.  However, we don't want to actually include
# the real code here, since we will be doing deployments from elsewhere.
# So we make an dummy .zip file as code stored in storage.
########################################################################

resource "google_storage_bucket" "realtime_functions_bucket" {
  name                        = "${var.environment}-realtime-functions-bucket"
  location                    = var.region
  uniform_bucket_level_access = true
  labels                      = var.labels
}

data "archive_file" "dummy_cloud_function_code_archive" {
  source_dir  = "../modules/realtime_processing_services/assets/dummy-cloud-function"
  type        = "zip"
  output_path = "../modules/realtime_processing_services/assets/dummy-cloud-function.zip"
}

resource "google_storage_bucket_object" "dummy_cloud_function_code_storage_object" {
  name   = "dummy-cloud-function.zip"
  bucket = google_storage_bucket.realtime_functions_bucket.name
  source = "../modules/realtime_processing_services/assets/dummy-cloud-function.zip"
}
