locals {
  # Remove any topics that are not in this environment.
  # This is needed because the feature_processor is in realtime-processing-services module,
  # which is in dev/staging/prod, but these topics are in the decodable module, which is not
  # in staging.
  processor_configs = [
    for config in [
      {
        processor_name             = "feature-processor"
        kubernetes_service_account = "feature-processor"
        topic_name                 = var.forecasting_feature_store_topic_name
        subscription_name          = "${var.environment}-forecasting-feature-store-subscription"
        slack_alert_channel        = var.team_metadata.forecasting.slack_channel
        should_page                = var.team_metadata.forecasting.should_page
        datadog_team_id            = var.team_metadata.forecasting.datadog_team_id
        playbook_link              = "https://www.notion.so/apella/Forecasting-Ops-Team-18baf54ad37a80a4999ad48828c5eb04?showMoveTo=true"
      },
      {
        processor_name             = "shared-forecasting-feature-processor"
        kubernetes_service_account = "shared-forecasting-feature-processor"
        topic_name                 = var.shared_forecasting_feature_store_topic_name
        subscription_name          = "${var.environment}-shared-forecasting-feature-store-subscription"
        slack_alert_channel        = var.team_metadata.forecasting.slack_channel
        should_page                = var.team_metadata.forecasting.should_page
        datadog_team_id            = var.team_metadata.forecasting.datadog_team_id
        playbook_link              = "https://www.notion.so/apella/Forecasting-Ops-Team-18baf54ad37a80a4999ad48828c5eb04?showMoveTo=true"
      },
      {
        processor_name             = "event-model-feature-processor"
        kubernetes_service_account = "event-model-feature-processor"
        topic_name                 = var.event_model_features_topic_name
        subscription_name          = "${var.environment}-event-model-features-subscription"
        slack_alert_channel        = var.team_metadata.computer_vision.slack_channel
        should_page                = var.team_metadata.computer_vision.should_page
        datadog_team_id            = var.team_metadata.computer_vision.datadog_team_id
        playbook_link              = "https://www.notion.so/apella/Event-Predictor-Playbook-11c8b6ae11af4cbf940bc0853240e06e"
      }
    ] : config if config.topic_name != null
  ]
  processor_configs_map = {
    for config in local.processor_configs : config.processor_name => config
  }
  map_by_subscription_name = {
    for config in local.processor_configs : config.subscription_name => config
  }
}

# Add k8s workload identity federation support for running the feature processor as a k8s service
resource "google_service_account_iam_member" "workload_identity_mapping_feature_processor" {
  for_each           = local.processor_configs_map
  service_account_id = google_service_account.realtime_processing_services.id
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.internal_project_id}.svc.id.goog[realtime-processing-services/${each.value.kubernetes_service_account}]"
}


# TODO: when switching to the deadletter subscriptino module, change the foreach to use processor_configs_map if we need to change the map keys anyway.
resource "google_pubsub_subscription" "feature_processor_input_subscriptions" {
  for_each = local.map_by_subscription_name

  name    = each.value.subscription_name
  topic   = each.value.topic_name
  project = var.project_id
  labels  = var.labels
  expiration_policy {
    ttl = "" // never expire this subscription
  }
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "300s"
  }
}

# Give service account permissions to pull from the input topics
resource "google_pubsub_subscription_iam_member" "feature_processor_topic_permissions" {
  for_each     = google_pubsub_subscription.feature_processor_input_subscriptions
  subscription = each.value.name
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

# Add a monitor for the oldest unacked message metrics
module "feature_processing_pull_subscription_alerts" {
  for_each = local.map_by_subscription_name

  source = "../pubsub-subscription-alerting"

  # Only enable monitors if there is a slack channel or should_page configured for this topic
  enable_monitors = (
    each.value.slack_alert_channel != null ||
    each.value.should_page
  )

  human_readable_subscription_name                = "Feature Processor ${each.value.subscription_name} Pull Subscription"
  age_of_unacked_messages_alert_threshold_seconds = 600
  age_of_unacked_messages_no_data_timeframe       = 10
  age_of_unacked_messages_averaging_window        = "last_15m"
  num_unacked_messages_alert_threshold            = 1800 # 30 per second, 60 second age is ~ 1800
  link_to_playbook                                = each.value.playbook_link

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = each.value.slack_alert_channel
  should_page        = each.value.should_page
  subscription_name  = each.value.subscription_name
  datadog_team_id    = each.value.datadog_team_id
}

module "feature_processing_error_monitor" {
  source = "app.terraform.io/apella/gcp-monitor/datadog"

  name               = "Feature Processor Errors (last_15m)"
  type               = "metric alert"
  message            = <<END_MESSAGE
{{#is_alert}} Feature Processor is encountering errors. {{/is_alert}}
{{#is_recovery}} Feature Processor has recovered {{/is_recovery}}
${var.team_metadata.forecasting.slack_channel != null ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
END_MESSAGE
  query              = "sum(last_15m):sum:feature_processor_features_status.count{env:${var.environment}, status:failure}.as_count() > 2"
  threshold_critical = 2
  threshold_warning  = 1
  notify_no_data     = false

  project_id  = var.project_id
  environment = var.environment
  tags        = ["team:${var.team_metadata.forecasting.datadog_team_id}"]
}


module "feature_processing_no_data_monitor" {
  source = "app.terraform.io/apella/gcp-monitor/datadog"

  name               = "Feature Processor No Data"
  type               = "metric alert"
  message            = <<END_MESSAGE
{{#is_alert}}
The Feature Processor is experiencing a low quantity of messages.
${(var.team_metadata.forecasting.slack_channel != null) && (!var.team_metadata.forecasting.should_page) ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
{{/is_alert}}
{{#is_warning}}
Feature Processor is experiencing a low quantity of messages.
${var.team_metadata.forecasting.slack_channel != null ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
{{/is_warning}}
{{#is_no_data}}
Feature Processor is experiencing a low quantity of messages.
${(var.team_metadata.forecasting.slack_channel != null) && (!var.team_metadata.forecasting.should_page) ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
{{/is_no_data}}
{{#is_recovery}}
Feature Processor has recovered
${var.team_metadata.forecasting.slack_channel != null ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
{{/is_recovery}}
END_MESSAGE
  query              = "sum(last_45m):sum:feature_processor_features_status.count{env:${var.environment}, status:success}.as_count() < 5"
  threshold_critical = 5
  threshold_warning  = 10
  no_data_timeframe  = 15
  notify_no_data     = true

  project_id  = var.project_id
  environment = var.environment
  tags        = ["team:${var.team_metadata.forecasting.datadog_team_id}"]
}


module "feature_processing_staleness_monitor" {
  source = "app.terraform.io/apella/gcp-monitor/datadog"

  name               = "Feature Processor Feature Staleness"
  type               = "metric alert"
  message            = <<END_MESSAGE
{{#is_alert}}
Feature Processor is seeing very stale features.
${(var.team_metadata.forecasting.slack_channel != null) && (!var.team_metadata.forecasting.should_page) ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
{{/is_alert}}
{{#is_warning}}
Feature Processor has stale features
${var.team_metadata.forecasting.slack_channel != null ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
{{/is_warning}}
{{#is_recovery}}
Feature Processor has fresh features
${var.team_metadata.forecasting.slack_channel != null ? "@slack-${var.team_metadata.forecasting.slack_channel}" : "No Team"}
${var.team_metadata.forecasting.should_page ? "@webhook-incident_io" : "No Team"}
{{/is_recovery}}
END_MESSAGE
  query              = "percentile(last_15m):p75:feature_staleness_seconds{env:${var.environment}} >= 600"
  threshold_critical = 600
  threshold_warning  = 300
  notify_no_data     = false

  project_id  = var.project_id
  environment = var.environment
  tags        = ["team:${var.team_metadata.forecasting.datadog_team_id}"]
}
