# The image_processor performs object inference and color detection

resource "google_pubsub_topic_iam_member" "image_processor_acknowledges_messages" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.customer_realtime_image_processing.id
  member = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

# The ML project handles subscriptions to image image_embeddings

resource "google_pubsub_topic_iam_member" "image_embedding_output_access" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.image_embedding_outputs.id
  member = "serviceAccount:${var.ml_project_sa_email}"
}

#
# The image processor needs to be able to read the images in these buckets
#

resource "google_storage_bucket_iam_member" "customer_image_data_buckets_roles" {
  for_each = toset(var.customer_list)
  bucket   = "${var.environment}-customer-${each.value}-image-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

#
# The image processor publishes to these topics
#
resource "google_pubsub_topic_iam_member" "image_processor_publishes_to_outputs" {
  topic  = google_pubsub_topic.image_processing_outputs.id
  role   = "roles/pubsub.publisher"
  member = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

resource "google_pubsub_topic_iam_member" "image_embedding_publishes_to_outputs" {
  topic  = google_pubsub_topic.image_embedding_outputs.id
  role   = "roles/pubsub.publisher"
  member = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

# Add k8s workload identity federation support for running the image processor as a k8s service
resource "google_service_account_iam_member" "workload_identity_mapping" {
  service_account_id = google_service_account.realtime_processing_services.id
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.internal_project_id}.svc.id.goog[realtime-processing-services/image-processor]"
}

# Pull subscription for k8s service, with deadletter queue
module "image_processing_pull_subscription" {
  source                     = "app.terraform.io/apella/pubsub-subscription-with-deadletter/google"
  version                    = "0.1.1"
  project_id                 = var.project_id
  environment                = var.environment
  topic_id                   = google_pubsub_topic.customer_realtime_image_processing.id
  subscription_base_name     = "${var.environment}-image-processing-pull"
  labels                     = var.labels
  message_retention_duration = "604800s"
  retry_policy = {
    minimum_backoff = "5s"
    maximum_backoff = "600s"
  }
  max_delivery_attempts = 12 # setting this to a high value to avoid losing messages during outages. That's about 1 hour if we take into account only the backoff time. "Broken" messages are not very common, and they will eventually be moved to the deadletter queue
  deadletter_enabled    = true
}

module "image_processing_pull_subscription_alerts" {
  source          = "../pubsub-subscription-alerting"
  enable_monitors = var.team_metadata.computer_vision.slack_channel != null || var.team_metadata.computer_vision.should_page

  human_readable_subscription_name                = "Image Processing Pull Subscription"
  age_of_unacked_messages_alert_threshold_seconds = 600
  age_of_unacked_messages_no_data_timeframe       = 10
  age_of_unacked_messages_averaging_window        = "last_15m"
  num_unacked_messages_alert_threshold            = 1800 # 30 per second, 60 second age is ~ 1800
  link_to_playbook                                = "https://www.notion.so/apella/Image-Processor-Playbook-02ff0b51ea7a4a8597c5dfda363700fa"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id
  subscription_name  = module.image_processing_pull_subscription.subscription_name
}

resource "google_pubsub_subscription_iam_member" "topic_subscription_permissions" {
  subscription = module.image_processing_pull_subscription.subscription_id
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}
