# This topic will have all of the images to process in it
resource "google_pubsub_topic" "customer_realtime_image_processing" {
  name   = "${var.environment}-customer-realtime-image-processing"
  labels = var.labels
}


# This topic is populated by the customer data bucket notifications
resource "google_storage_notification" "customer_image_data_notification_image_processor" {
  count          = length(var.customer_list)
  bucket         = "${var.environment}-customer-${element(var.customer_list, count.index)}-image-data"
  payload_format = "JSON_API_V1"
  topic          = google_pubsub_topic.customer_realtime_image_processing.id
  event_types    = ["OBJECT_FINALIZE"]
  custom_attributes = {
    asset_type = "image"
    customer   = element(var.customer_list, count.index)
  }
}

#
# The internal gcs account needs permission to write to this topic
#
resource "google_pubsub_topic_iam_member" "gcs_permission_to_publish_to_image_processing" {
  topic  = google_pubsub_topic.customer_realtime_image_processing.id
  role   = "roles/pubsub.publisher"
  member = "serviceAccount:${var.gcs_service_account.email_address}"
}
