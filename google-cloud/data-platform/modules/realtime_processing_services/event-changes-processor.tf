resource "google_cloudfunctions_function" "event_changes_processor" {
  name        = "event-changes-processor"
  description = "Performs processing when an event changes in the API SQL database"
  runtime     = "python39"

  # The code is not in terraform repo, so we use this dummy function
  source_archive_bucket = google_storage_bucket.realtime_functions_bucket.name
  source_archive_object = google_storage_bucket_object.dummy_cloud_function_code_storage_object.name
  entry_point           = "dummy"

  # We want trigger_http so we can use retries.  trigger_topic does not allow retries.
  trigger_http = true

  # Since this updates all of the phases for a few days around an event,
  # if we have multiple instances, then those instances may be working on the same time period.
  # In the worst case, this will cause errors when both instances try to delete the same phase.
  # But, we experimented with having a higher max instance, and there was not any errors.
  # So we have max instance set to 10 here, arbitrarily, to help when there is a backlog of messages
  max_instances = 10

  # Sometimes this can take a long time, if there is a lot of diffs
  timeout = 180

  # And require a lot of memory.
  available_memory_mb = 512

  lifecycle {
    # This prevents this dummy function from overwriting
    ignore_changes = [
      entry_point,
      source_archive_bucket,
      source_archive_object,
      labels,
      environment_variables,
      secret_environment_variables,
      runtime,
    ]
  }
}

module "event_changes_function_alerts" {
  source          = "../cloud-function-alerting"
  enable_monitors = var.team_metadata.computer_vision.slack_channel != null || var.team_metadata.computer_vision.should_page

  human_readable_function_name = "Event Changes Processor"
  # This processor can take a long time, but it shouldn't take a long time for many requests.
  # So we want to use the "min" aggregator to only alert when it consistently takes a long time
  execution_time_aggregator                  = "min"
  execution_time_evaluation_window           = "last_10m"
  p99_execution_time_alert_threshold_seconds = 60
  p95_execution_time_alert_threshold_seconds = 15
  error_percent_alert_threshold              = 0.2
  link_to_playbook                           = "https://www.notion.so/apella/Event-Changes-Processor-Playbook-4df000849f1d408f9a847807f0094532"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id
  function_name      = google_cloudfunctions_function.event_changes_processor.name
}

resource "google_pubsub_subscription" "event_changes_subscription" {
  name                       = "${var.environment}-event-changes-processor-subscription"
  topic                      = google_pubsub_topic.event_changes.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = true
  expiration_policy {
    ttl = "" // never expire this subscription
  }

  ack_deadline_seconds = 60

  push_config {
    push_endpoint = "https://${var.region}-${var.project_id}.cloudfunctions.net/${google_cloudfunctions_function.event_changes_processor.name}"

    // We want the push to be authenticated, so we do not need to make the
    oidc_token {
      service_account_email = google_service_account.realtime_processing_services.email
    }
  }

  # We do not need a dead letter policy, we would rather just keep retrying, but we can retry later
  # to not block the subscription queue
  retry_policy {
    minimum_backoff = "10s"
  }

  # messages do not need to be ordered
  enable_message_ordering = false
}

module "event_changes_subscription_alerts" {
  source          = "../pubsub-subscription-alerting"
  enable_monitors = var.team_metadata.computer_vision.slack_channel == null && !var.team_metadata.computer_vision.should_page ? false : true

  human_readable_subscription_name                = "Event Changes Subscription"
  age_of_unacked_messages_alert_threshold_seconds = 5 * 60
  num_unacked_messages_alert_threshold            = 50
  link_to_playbook                                = "https://www.notion.so/apella/Event-Changes-Processor-Playbook-4df000849f1d408f9a847807f0094532"

  project_id         = var.project_id
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  environment        = var.environment
  slack_channel_name = var.team_metadata.computer_vision.slack_channel
  should_page        = var.team_metadata.computer_vision.should_page
  datadog_team_id    = var.team_metadata.computer_vision.datadog_team_id
  subscription_name  = google_pubsub_subscription.event_changes_subscription.name
}

resource "google_pubsub_topic_iam_member" "event_changes_processor_acknowledges_messages" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.event_changes.id
  member = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}

# Above, the subscription uses the realtime_processing_services oidc_token to to invoke the endpoint
# so we need to give this token permission to invoke the function
resource "google_cloudfunctions_function_iam_member" "realtime_processing_invokes_event_changes" {
  role           = "roles/cloudfunctions.invoker"
  cloud_function = google_cloudfunctions_function.event_changes_processor.name
  member         = "serviceAccount:${google_service_account.realtime_processing_services.email}"
}
