
#
# This topic will hold all changes to all events, after they are committed to the DB.
#
resource "google_pubsub_topic" "event_changes" {
  name   = "${var.environment}-customer-event-changes"
  labels = var.labels
}

#
# The API server will publish to this topic
#
resource "google_pubsub_topic_iam_member" "api_publishes_event_changes" {
  topic  = google_pubsub_topic.event_changes.id
  role   = "roles/pubsub.publisher"
  member = "serviceAccount:${var.api_server_sa}"
}
