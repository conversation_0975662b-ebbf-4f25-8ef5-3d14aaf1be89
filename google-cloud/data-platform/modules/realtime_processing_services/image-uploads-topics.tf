
resource "google_storage_notification" "customer_image_data_notification" {
  count          = length(var.customer_list)
  bucket         = "${var.environment}-customer-${element(var.customer_list, count.index)}-image-data"
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-upload-topic"
  event_types    = ["OBJECT_FINALIZE"]
  custom_attributes = {
    asset_type = "image"
    customer   = element(var.customer_list, count.index)
  }
}

resource "google_storage_notification" "customer_image_data_delete_notification" {
  count          = length(var.customer_list)
  bucket         = "${var.environment}-customer-${element(var.customer_list, count.index)}-image-data"
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types    = ["OBJECT_DELETE"]
  custom_attributes = {
    asset_type = "image"
    customer   = element(var.customer_list, count.index)
  }
}
