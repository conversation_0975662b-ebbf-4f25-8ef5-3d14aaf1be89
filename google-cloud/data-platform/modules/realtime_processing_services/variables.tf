variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "project_number" {
  description = "GCP Project Number"
}

variable "internal_project_id" {
  type        = string
  description = "the project id for the dev/staging/prod-internal"
}

variable "zone" {
  description = "GCP zone for resources"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "api_server_sa" {
  description = "The API server service account"
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "dataflow_tmp_bucket" {
  description = "Bucket to use for temporary dataflow files"
}

variable "realtime_image_processing_outputs_table" {
  description = "The bigquery table to write the realtime image processing outputs to"
}

variable "realtime_image_embeddings_outputs_table" {
  description = "The bigquery table containing the image embeddings"
}

variable "network_project_id" {
  description = "The project id that hosts the vpc network"
}

variable "subnetwork" {
  description = "The name of the subnetwork"
}

variable "dataflow_sa_email" {
  description = "The service account to run the dataflow jobs as"
}

variable "ml_project_sa_email" {
  description = "ML Terraform SA email address"
}

variable "gcs_service_account" {
  description = "Google's internal service account"
}

variable "customer_list" {
  description = "The names of customers to create buckets for"
  type        = list(string)
  default     = []
}

variable "team_metadata" {
  type = object({
    forecasting = object({
      should_page     = bool
      slack_channel   = optional(string)
      datadog_team_id = string
    })
    computer_vision = object({
      should_page     = bool
      slack_channel   = optional(string)
      datadog_team_id = string
    })
  })
  description = "Metadata for teams. Slack channel - Do not prefix with #.  If not specified, alerts will be disabled."
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform, only provide if you want alerts"
  type        = string
  sensitive   = true
  default     = null
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform, only provide if you want alerts"
  type        = string
  sensitive   = true
  default     = null
}

variable "media_asset_service_project" {
  description = "Media Asset Service project id"
  type        = string
  default     = "dev-media-asset-93e8c3"
}

variable "vpc_access_connector" {
  description = "The vpc connector id for cloud functions to connect to"
}

variable "forecasting_feature_store_topic_name" {
  description = "The pubsub topic for the forecasting feature store"
  default     = null
}

variable "shared_forecasting_feature_store_topic_name" {
  description = "The pubsub topic for the shared forecasting feature store"
  default     = null
}

variable "event_model_features_topic_name" {
  description = "The pubsub topic for the event model features"
  default     = null
}

variable "deadletter_enabled" {
  description = "whether to build deadlettering topics/subscriptions/policies"
  type        = bool
  default     = true
}

variable "realtime_event_model_decodable_features_table" {
  description = "The bigquery table to write the event model decodable features to"
  default     = null
}
