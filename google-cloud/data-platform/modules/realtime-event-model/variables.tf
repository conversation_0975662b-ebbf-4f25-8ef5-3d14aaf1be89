
variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "deployment_suffix" {
  type        = string
  description = "deployment suffix, such as '-canary'"
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "internal_project_id" {
  type        = string
  description = "the project id for the dev/staging/prod-internal"
}

variable "vpc_network_id" {
  description = "data.terraform_remote_state.$ENV_vpc.outputs.network.id"
}

variable "zone" {
  description = "GCP zone for resources"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "big_table_instance_name" {
  description = "the name of the big table instance used as a feature store"
}

variable "image_embeddings_table_name" {
  description = "the name of the big table table used for image embeddings"
}

variable "event_model_forecasts_table_name" {
  description = "the name of the big table table used for image embeddings"
}

variable "aggregated_features_table_name" {
  description = "the name of the big table table used for aggregated features"
}

variable "github_runner_sa" {
  description = "The service account email of the github runner"
}

variable "metabase_sa_email" {
  description = "The service account email of the metabase"
}

variable "bastion_host_sa_email" {
  description = "The service account email of the bastion host"
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "monitor_channel" {
  description = "The slack or incident.io channel to send monitoring alerts to"
}

variable "db_backup_location" {
  description = "The region to which backups for this db will be stored. By default stored in the US multiregion"
  type        = string
  default     = "us"
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
