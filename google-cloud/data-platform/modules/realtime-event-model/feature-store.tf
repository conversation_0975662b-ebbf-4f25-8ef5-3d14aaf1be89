#
# The feature store uses postgres, and for that we need a user name.
# We need to store that username in a secret so that we can load it
# up in the services that need it.
#
resource "google_secret_manager_secret" "postgres_user_name_secret" {
  project   = var.project_id
  secret_id = "${var.environment}-feature-store-username${var.deployment_suffix}"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "postgres_user_name_secret_version" {
  secret = google_secret_manager_secret.postgres_user_name_secret.id

  secret_data = "feature_store_user"
}

resource "google_secret_manager_secret_iam_member" "realtime_event_model_uses_postgres_user_name" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.postgres_user_name_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.realtime_event_model.email}"
}

resource "google_secret_manager_secret_iam_member" "github_runner_uses_postgres_user_name" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.postgres_user_name_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.github_runner_sa}"
}

#
# Similarly, it also needs a password. This one is randomly generated.
#
resource "random_password" "feature_store_password" {
  length  = 16
  special = true
}
resource "google_secret_manager_secret" "postgres_user_password_secret" {
  project   = var.project_id
  secret_id = "${var.environment}-feature-store-password${var.deployment_suffix}"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "postgres_user_password_secret_version" {
  secret = google_secret_manager_secret.postgres_user_password_secret.id

  secret_data = random_password.feature_store_password.result
}

resource "google_secret_manager_secret_iam_member" "realtime_event_model_uses_postgres_password" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.postgres_user_password_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.realtime_event_model.email}"
}

resource "google_secret_manager_secret_iam_member" "github_runner_uses_postgres_password" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.postgres_user_password_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.github_runner_sa}"
}

#
# And we need the database instance itself
#
module "feature_store" {
  source  = "app.terraform.io/apella/cloud-sql/google"
  version = "3.0.2"

  depends_on = [
    google_secret_manager_secret_version.postgres_user_name_secret_version,
    google_secret_manager_secret_version.postgres_user_password_secret_version,
  ]

  name                     = "${var.environment}-feature-store-01${var.deployment_suffix}"
  random_instance_name     = false
  availability_type        = "REGIONAL"
  db_name                  = "transformer_features"
  database_version         = "POSTGRES_13"
  project_id               = var.project_id
  zone                     = var.zone
  region                   = var.region
  tier                     = "db-custom-8-15360"
  deletion_protection      = false
  secret_project_id        = var.project_id
  secretname_user_name     = google_secret_manager_secret.postgres_user_name_secret.secret_id
  secretname_user_password = google_secret_manager_secret.postgres_user_password_secret.secret_id
  max_connections          = 500
  monitor_enabled          = true
  monitor_tags             = ["env:${var.environment}", "project-id:${var.project_id}", "team:${var.datadog_team_id}"]
  monitor_warning_channel  = var.monitor_channel
  monitor_critical_channel = var.monitor_channel

  maintenance_window_day          = 7 # sunday
  maintenance_window_hour         = 8 # 08:00 UTC
  maintenance_window_update_track = "stable"

  ip_configuration = {
    ipv4_enabled        = false
    private_network     = var.vpc_network_id
    ssl_mode            = "TRUSTED_CLIENT_CERTIFICATE_REQUIRED"
    authorized_networks = []
  }

  backup_configuration = {
    enabled                        = true
    start_time                     = "00:00"
    location                       = var.db_backup_location
    point_in_time_recovery_enabled = true
  }

  user_labels = var.labels

  insights_config = {
    query_string_length     = 1024
    record_application_tags = true
    record_client_address   = true
  }
}

resource "google_project_iam_member" "realtime_event_model_uses_postgres" {
  project = var.project_id
  member  = "serviceAccount:${google_service_account.realtime_event_model.email}"
  role    = "roles/cloudsql.client"
}

resource "google_project_iam_member" "github_action_runner_uses_postgres" {
  project = var.project_id
  member  = "serviceAccount:${var.github_runner_sa}"
  role    = "roles/cloudsql.client"
}

resource "google_project_iam_member" "metabase_sa_uses_postgres" {
  project = var.project_id
  member  = "serviceAccount:${var.metabase_sa_email}"
  role    = "roles/cloudsql.client"
}

resource "google_project_iam_member" "bastion_host_uses_postgres" {
  for_each = toset([
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${var.bastion_host_sa_email}"
}
