module "event_model_cache_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-event-prediction-cache${var.deployment_suffix}"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}

resource "google_storage_bucket_iam_member" "event_predictor_uses_cache_bucket" {
  bucket = module.event_model_cache_bucket.name
  role   = "roles/storage.objectUser"
  member = "serviceAccount:${google_service_account.realtime_event_model.email}"

  # Terraform needs to apply these in the correct order
  depends_on = [module.event_model_cache_bucket]
}
