
# The event-predictor is in k8s, so this specific internal k8s service account needs permission
# to use our standard service account.
locals {
  k8s_namespace               = "realtime-processing-services"
  event_predictor_k8s_service = "event-predictor${var.deployment_suffix}"
}

resource "google_service_account_iam_member" "realtime_event_model_sa_used_by_event_predictor" {
  service_account_id = google_service_account.realtime_event_model.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.internal_project_id}.svc.id.goog[${local.k8s_namespace}/${local.event_predictor_k8s_service}]"
}

resource "google_bigtable_table_iam_member" "bigtable_feature_store_used_by_event_predictor" {
  instance = var.big_table_instance_name
  table    = var.image_embeddings_table_name
  role     = "roles/bigtable.reader"

  member = "serviceAccount:${google_service_account.realtime_event_model.email}"
}

resource "google_bigtable_table_iam_member" "bigtable_feature_store_forecasts_table_used_by_event_predictor" {
  instance = var.big_table_instance_name
  table    = var.event_model_forecasts_table_name
  role     = "roles/bigtable.user" # read and write

  member = "serviceAccount:${google_service_account.realtime_event_model.email}"
}

resource "google_bigtable_table_iam_member" "bigtable_aggregated_feature_store_used_by_event_predictor" {
  instance = var.big_table_instance_name
  table    = var.aggregated_features_table_name
  role     = "roles/bigtable.user" # read and write

  member = "serviceAccount:${google_service_account.realtime_event_model.email}"
}
