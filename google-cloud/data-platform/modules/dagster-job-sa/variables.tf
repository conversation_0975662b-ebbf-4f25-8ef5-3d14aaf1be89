variable "project_id" {
  description = "GCP Project ID"
}

variable "service_account_id" {
  description = "GKE Service account ID (name)"
}

variable "service_account_description" {
  description = "Description for the service account"
}

variable "gke_project_id" {
  description = "GCP project ID of the GKE clsuter where Dagster jobs run"
}

variable "bucket_list" {
  description = "List of bucket names the SA may access"
  type        = list(string)
  default     = []
}

variable "roles" {
  description = "List of roles to grant the SA"
  type        = list(string)
  default     = []
}

variable "self_hosted_ksa_name" {
  description = "GKE Service Account name in self hosted Dagster"
}

variable "self_hosted_ksa_namespace" {
  description = "GKE namespace for the Kubernetes Service Account in self hosted Dagster"
  default     = "dagster"
}
