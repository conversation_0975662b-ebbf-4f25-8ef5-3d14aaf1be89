# Dagster MLOps SA
resource "google_service_account" "sa" {
  account_id   = var.service_account_id
  display_name = var.service_account_description
}

resource "google_service_account_iam_member" "internal_gke_workload_identity_self_hosted" {
  service_account_id = google_service_account.sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.gke_project_id}.svc.id.goog[${var.self_hosted_ksa_namespace}/${var.self_hosted_ksa_name}]"
}

## access to buckets
resource "google_storage_bucket_iam_member" "bucket_roles" {
  for_each = toset(var.bucket_list)
  bucket   = each.value
  role     = "roles/storage.admin"
  member   = "serviceAccount:${google_service_account.sa.email}"
}

## general roles
resource "google_project_iam_member" "general_roles" {
  for_each = toset(var.roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.sa.email}"
}

output "sa" {
  value = google_service_account.sa
}
