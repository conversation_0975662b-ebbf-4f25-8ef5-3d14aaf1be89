resource "google_secret_manager_secret_iam_member" "realtime_clearml_api_key" {
  project   = var.project_id
  secret_id = var.clearml_api_key_secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.dagster_realtime_dags_sa.email}"
}

resource "google_secret_manager_secret_iam_member" "realtime_clearml_api_secret" {
  project   = var.project_id
  secret_id = var.clearml_api_secret_secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.dagster_realtime_dags_sa.email}"
}
