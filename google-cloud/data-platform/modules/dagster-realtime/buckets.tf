
module "realtime-dagster-io-manager" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-realtime-dagster-io-manager"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}


#
# The realtime dagster service needs bucket.get access in addition to read/write
#
resource "google_storage_bucket_iam_member" "realtime-dagster-reads-io-manager" {
  bucket     = "${var.environment}-realtime-dagster-io-manager"
  role       = "roles/storage.admin"
  member     = "serviceAccount:${var.dagster_realtime_dags_sa.email}"
  depends_on = [module.realtime-dagster-io-manager]
}
