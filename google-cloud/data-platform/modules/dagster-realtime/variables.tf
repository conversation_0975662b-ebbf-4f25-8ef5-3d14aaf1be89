variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

variable "dagster_realtime_dags_sa" {
  description = "The service account for the dagster realtime dags"
}

variable "mlops_dags_sa_email" {
  description = "The service account for the mlops dags"
}

/*****************************************************************************
  Clearml Key
******************************************************************************/

variable "clearml_api_key_secret_id" {
  description = "Api key token for clearml"
  type        = string
  sensitive   = true
}

variable "clearml_api_secret_secret_id" {
  description = "Api secret token for clearml"
  type        = string
  sensitive   = true
}

variable "forecast_dataset" {
  description = "The BigQuery dataset used to organize forecast tables"
  type = object({
    dataset_id  = string
    description = string
  })
  default = {
    dataset_id  = "case_forecasting"
    description = "This dataset encapsulates all tables related to case forecasting metrics."
  }
}

variable "accessor_sa_emails" {
  description = "Service accounts that get read access to realtime bigquery tables"
  type        = list(string)
}
