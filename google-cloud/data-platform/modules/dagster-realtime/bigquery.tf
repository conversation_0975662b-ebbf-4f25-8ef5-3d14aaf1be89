resource "google_bigquery_dataset_iam_member" "realtime_dagster_bigquery_forecast_editor" {
  dataset_id = google_bigquery_dataset.forecast_dataset.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${var.dagster_realtime_dags_sa.email}"
}

resource "google_bigquery_dataset_iam_member" "mlops_dagster_bigquery_forecast_editor" {
  dataset_id = google_bigquery_dataset.forecast_dataset.dataset_id
  role       = "roles/bigquery.dataEditor"
  member     = "serviceAccount:${var.mlops_dags_sa_email}"
}

resource "google_bigquery_dataset_iam_member" "bigquery_case_forecast_viewer" {
  for_each   = toset(var.accessor_sa_emails)
  dataset_id = google_bigquery_dataset.forecast_dataset.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${each.value}"
}

resource "google_bigquery_dataset" "forecast_dataset" {
  dataset_id  = var.forecast_dataset.dataset_id
  labels      = var.labels
  description = var.forecast_dataset.description
}

#
# Define the table in BigQuery
#
resource "google_bigquery_table" "case_forecast_info_table" {
  project    = var.project_id
  dataset_id = google_bigquery_dataset.forecast_dataset.dataset_id
  table_id   = "case_forecast_info"
  time_partitioning {
    type  = "DAY"
    field = "forecast_date"
  }
  clustering = ["forecast_variant", "case_id", "pythia_prediction_tag", "run_id"]
  schema     = <<-SCHEMA
    [
      { 
        "name": "source",
        "type": "STRING"
      },
      { 
        "name": "forecast_run_time",
        "type": "TIMESTAMP"
      },
      { 
        "name": "forecast_date",
        "type": "DATE"
      },
      { 
        "name": "room_id",
        "type": "STRING"
      },
      { 
        "name": "case_id",
        "type": "STRING"
      },
      { 
        "name": "forecast_start_time",
        "type": "TIMESTAMP"
      },
      { 
        "name": "forecast_end_time",
        "type": "TIMESTAMP"
      },
      { 
        "name": "start_time_model",
        "type": "STRING"
      },
      { 
        "name": "end_time_model",
        "type": "STRING"
      },
      { 
        "name": "case_start_offset_model_version",
        "type": "STRING"
      },
      { 
        "name": "case_duration_model_version",
        "type": "STRING"
      },
      { 
        "name": "case_day_of_start_model_version",
        "type": "STRING"
      },
      {
        "name": "bayesian_model_version",
        "type": "STRING"
      },
      {
        "name": "run_id",
        "type": "STRING"
      },
      {
        "name": "forecast_combiner_version",
        "type": "STRING"
      },
      {
        "name": "forecast_variant",
        "type": "STRING"
      },
      {
        "name": "case_turnover_duration_model_version",
        "type": "STRING"
      },
      {
        "name": "event_model_forecasts_model_version",
        "type": "STRING"
      },
      {
        "name": "case_dynamic_end_model_version",
        "type": "STRING"
      },
      {
        "name": "static_start_offset_minutes",
        "type": "FLOAT"
      },
      {
        "name": "turnover_duration_minutes",
        "type": "FLOAT"
      },
      {
        "name": "static_duration_minutes",
        "type": "FLOAT"
      },
      {
        "name": "pythia_duration_minutes",
        "type": "FLOAT"
      },
      {
        "name": "transformer_end_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "pythia_end_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "static_duration_end_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "is_auto_follow",
        "type": "BOOLEAN"
      },
      {
        "name": "is_overtime",
        "type": "BOOLEAN"
      },
      {
        "name": "pythia_prediction_tag",
        "type": "STRING"
      },
      {
        "name": "bayesian_duration_minutes",
        "type": "FLOAT"
      },
      {
        "name": "bayesian_end_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "bayesian_duration_full_posterior",
        "type": "FLOAT",
        "mode": "REPEATED"
      },
      {
        "name": "case_start_source",
        "type": "STRING"
      }
    ]
    SCHEMA
}
