locals {
  accessor_members = [
    for email in var.slack_secret_accessors : "serviceAccount:${email}"
  ]
}

resource "google_secret_manager_secret" "slack_auth_token_secret" {
  secret_id = "${var.environment}-slack-auth-token"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "slack_auth_token_version" {
  secret = google_secret_manager_secret.slack_auth_token_secret.id

  secret_data = var.slack_auth_token
}

resource "google_secret_manager_secret_iam_member" "slack_auth_token_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.slack_auth_token_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}
