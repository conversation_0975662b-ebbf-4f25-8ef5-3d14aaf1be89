variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}


variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

/*****************************************************************************
  Auth Token
******************************************************************************/

variable "slack_auth_token" {
  description = "Auth token for slack"
  type        = string
  sensitive   = true
}

variable "slack_secret_accessors" {
  description = "List of accounts to sign up for access to Slack secrets"
  type        = list(string)
}
