resource "google_service_account" "datadog_service_account" {
  account_id   = "${var.environment}-data-datadog-sa"
  display_name = "Datadog Service Account"
  description  = "Exports logs and metrics from GCP to Datadog"
}

locals {
  roleList = [
    "roles/compute.viewer",
    "roles/monitoring.viewer",
    "roles/cloudasset.viewer",
    "roles/browser",
  ]
}

resource "google_project_iam_member" "datadog_service_account_iam_member" {
  for_each = toset(local.roleList)
  project  = google_service_account.datadog_service_account.project
  role     = each.value
  member   = "serviceAccount:${google_service_account.datadog_service_account.email}"
}

resource "datadog_integration_gcp_sts" "datadog_sts" {
  client_email    = google_service_account.datadog_service_account.email
  host_filters    = []
  automute        = true
  is_cspm_enabled = false
}

// Grant token creator role to the Datadog principal account.
resource "google_service_account_iam_member" "datadog_token_creator_iam" {
  service_account_id = google_service_account.datadog_service_account.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = format("serviceAccount:%s", datadog_integration_gcp_sts.datadog_sts.delegate_account_email)
}

