variable "project_id" {
  description = "GCP Project ID"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}
