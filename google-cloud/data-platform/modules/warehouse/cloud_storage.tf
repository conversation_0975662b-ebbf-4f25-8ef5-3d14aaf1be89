resource "google_storage_bucket" "data_warehouse" {
  name                        = "${var.environment}-data-warehouse"
  project                     = var.data_platform_project_id
  location                    = var.region
  uniform_bucket_level_access = true
}

resource "google_storage_bucket" "data_warehouse_io_manager" {
  name                        = "${var.environment}-data-warehouse-dagster-io-manager"
  project                     = var.data_platform_project_id
  location                    = var.region
  uniform_bucket_level_access = true
}