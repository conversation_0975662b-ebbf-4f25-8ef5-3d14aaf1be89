locals {
  hex_sa_member                   = "serviceAccount:${var.hex_sa_email}"
  dagster_sa_member               = "serviceAccount:${var.dagster_sa_email}"
  dagster_realtime_dags_sa_member = "serviceAccount:${var.dagster_realtime_dags_sa_email}"
  gh_runner_sa_member             = "serviceAccount:${var.gh_runner_sa_email}"
  # these are all service accounts that should be able to read "DWH datasets" i.e., sandbox, bronze, silver, gold
  accessor_members = [
    for email in var.accessor_sa_emails : "serviceAccount:${email}"
  ]
  terraform_sa_member = "serviceAccount:${var.data_platform_tf_sa_email}"
}

resource "google_project_iam_member" "data_platform_tf_sa_network_admin_role" {
  project = var.data_platform_project_id
  role    = "roles/datastream.admin"
  member  = local.terraform_sa_member
}

#
# Project-level BigQuery entitlements
#
resource "google_project_iam_member" "bigquery_job_user" {
  for_each = toset(concat(local.accessor_members, [local.dagster_sa_member]))
  project  = var.data_platform_project_id
  role     = "roles/bigquery.jobUser"
  member   = each.value
}

resource "google_project_iam_member" "bigquery_session_reader" {
  for_each = toset(concat(local.accessor_members, [local.dagster_sa_member]))
  project  = var.data_platform_project_id
  role     = "roles/bigquery.readSessionUser"
  member   = each.value
}

resource "google_project_iam_member" "bigquery_viewer" {
  # these are "DWH service accounts" - they should have read-access to everything in in the project
  for_each = toset([local.hex_sa_member, local.dagster_sa_member])
  project  = var.data_platform_project_id
  role     = "roles/bigquery.dataViewer"
  member   = each.value
}

#
# Read-only access to metal datasets
#
resource "google_bigquery_dataset_iam_member" "bronze_viewer" {
  for_each   = toset(local.accessor_members)
  dataset_id = google_bigquery_dataset.bronze.dataset_id
  member     = each.value
  role       = "roles/bigquery.dataViewer"
}

resource "google_bigquery_dataset_iam_member" "silver_viewer" {
  for_each   = toset(local.accessor_members)
  dataset_id = google_bigquery_dataset.silver.dataset_id
  member     = each.value
  role       = "roles/bigquery.dataViewer"
}

resource "google_bigquery_dataset_iam_member" "gold_viewer" {
  for_each   = toset(local.accessor_members)
  dataset_id = google_bigquery_dataset.gold.dataset_id
  member     = each.value
  role       = "roles/bigquery.dataViewer"
}

#
# Read/write access to metal datasets
#
resource "google_bigquery_dataset_iam_member" "bronze_editor" {
  dataset_id = google_bigquery_dataset.bronze.dataset_id
  member     = local.dagster_sa_member
  role       = "roles/bigquery.dataEditor"
}

resource "google_bigquery_dataset_iam_member" "silver_editor" {
  dataset_id = google_bigquery_dataset.silver.dataset_id
  member     = local.dagster_sa_member
  role       = "roles/bigquery.dataEditor"
}

resource "google_bigquery_dataset_iam_member" "gold_editor" {
  dataset_id = google_bigquery_dataset.gold.dataset_id
  member     = local.dagster_sa_member
  role       = "roles/bigquery.dataEditor"
}

#
# Read/write access to Sandbox for SAs
#
resource "google_bigquery_dataset_iam_member" "sandbox_editor" {
  for_each   = toset(concat(local.accessor_members, [local.dagster_sa_member]))
  dataset_id = google_bigquery_dataset.sandbox.dataset_id
  member     = each.value
  role       = "roles/bigquery.dataEditor"
}

#
# Read access to GCS/data-warehouse for SAs
#
resource "google_storage_bucket_iam_member" "data_warehouse_gcs_bucket_reader" {
  for_each = {
    hex              = local.hex_sa_member,
    dagster          = local.dagster_sa_member,
    dagster_realtime = local.dagster_realtime_dags_sa_member,
  }
  bucket = google_storage_bucket.data_warehouse.name
  role   = "roles/storage.objectViewer"
  member = each.value
}

# Upload access to bucket for GH runner
resource "google_storage_bucket_iam_member" "gh_runner_sa_bucket_writer" {
  bucket = google_storage_bucket.data_warehouse.name
  role   = "roles/storage.objectUser"
  member = local.gh_runner_sa_member
}

#
# Read/write and bucket.get access for Dagster IO Manager
#
resource "google_storage_bucket_iam_member" "data_warehouse_io_manager_bucket_admin" {
  bucket = google_storage_bucket.data_warehouse_io_manager.name
  role   = "roles/storage.admin"
  member = local.dagster_sa_member
}

#
# Read access to Auth0 Management API variables
#
resource "google_secret_manager_secret_iam_member" "auth0_management_client_secret_read_access" {
  secret_id = google_secret_manager_secret.auth0_management_client_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = local.dagster_sa_member
}

resource "google_secret_manager_secret_iam_member" "auth0_management_client_id_read_access" {
  secret_id = google_secret_manager_secret.auth0_management_client_id.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = local.dagster_sa_member
}
