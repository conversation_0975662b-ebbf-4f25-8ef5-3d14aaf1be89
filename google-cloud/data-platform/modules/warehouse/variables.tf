variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  type        = string
  description = "GCP Region"
}

variable "vpc_network_project_id" {
  description = "GCP VPC Project ID"
}

variable "web_api_project_id" {
  description = "GCP Web API Project ID"
}

variable "platform_project_id" {
  description = "GCP Platform Project ID"
}

variable "bastion_host_zone" {
  description = "Zone of the bastion host"
}

variable "sql_proxy_datastream_port" {
  description = "Listening port for bastion host's Datastream SQL proxy"
}

variable "ehr_sql_proxy_datastream_port" {
  description = "Listening port for the EHR db bastion hosts's sql proxy"
}

variable "ehr_data_warehouse_username_secret_id" {
  type        = string
  description = "The secret id for the ehr db data warehouse username"
}

variable "ehr_data_warehouse_password_secret_id" {
  type        = string
  description = "The secret id for the ehr db data warehouse password"
}

variable "datastream_vpc_subnet_ip_range" {
  description = "Allocated IP range for Datastream on the VPC network"
}

variable "vpc_network_name" {
  description = "VPC network name"
}

variable "data_platform_tf_sa_email" {
  description = "Data Platform Terraform SA email address"
}

variable "data_platform_project_id" {
  description = "Data Platform project ID"
}

variable "dagster_sa_email" {
  description = "Dagster service account email address"
  type        = string
}

variable "dagster_realtime_dags_sa_email" {
  description = "Dagster Realtime Dagster service account email address"
  type        = string
}

variable "hex_sa_email" {
  description = "Hex service account email address"
  type        = string
}

variable "accessor_sa_emails" {
  description = "Service accounts that get read only to metal tables, and read/write to sandbox"
  type        = list(string)
}

variable "slack_auth_token" {
  description = "Slack auth token needed for channel notifications"
  type        = string
}

variable "auth0_management_api_client_id" {
  description = "The client ID of the auth0 Machine-to-Machine Application for using the Management API."
  type        = string
}

variable "auth0_management_api_client_secret" {
  description = "The client secret of the auth0 Machine-to-Machine Application for using the Management API."
  type        = string
}

variable "ehr_project_id" {
  description = "GCP EHR project id"
  type        = string
}

variable "oncall_team_critical_alert_handle" {
  description = "The critical alert handle for the Data Platform pod"
  type        = string
}

variable "oncall_team_warning_alert_handle" {
  description = "The warning alert handle for the Data Platform pod"
  type        = string
}

variable "datadog_api_key" {
  description = "The parent project's API key"
}

variable "datadog_app_key" {
  description = "The parent project's app key"
}

variable "gh_runner_sa_email" {
  description = "The email address of the github runner service account"
  type        = string
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
