resource "google_secret_manager_secret" "auth0_management_client_secret" {
  secret_id = "${var.environment}-auth0-management-client-secret"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}
resource "google_secret_manager_secret_version" "auth0_management_client_secret_version" {
  secret      = google_secret_manager_secret.auth0_management_client_secret.id
  secret_data = var.auth0_management_api_client_secret
}

resource "google_secret_manager_secret" "auth0_management_client_id" {
  secret_id = "${var.environment}-auth0-management-client-id"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}
resource "google_secret_manager_secret_version" "auth0_management_client_id_version" {
  secret      = google_secret_manager_secret.auth0_management_client_id.id
  secret_data = var.auth0_management_api_client_id
}
