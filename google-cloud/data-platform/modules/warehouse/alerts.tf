resource "google_monitoring_notification_channel" "team_platform_services_alerts_slack" {
  display_name = "Platform Services GCP Alerts"
  type         = "slack"
  labels = {
    "channel_name" = "#team-platform-services-alert-${var.environment}"
  }
  sensitive_labels {
    auth_token = var.slack_auth_token
  }
}

resource "datadog_monitor_json" "job_failure_alert" {
  monitor = jsonencode(
    {
      message = <<EOT
                    {{#is_alert}}

                    Playbook:
                    https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1

                    See failure in the Dagster UI:
                    {{#is_exact_match "event.tags.dagster_definition_type" "job"}}
                    {{event.attributes.dagster_webserver_host}}/runs/{{event.attributes.run_id}}

                    Run ID:
                    {{event.attributes.run_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "sensor"}}
                    {{event.attributes.dagster_webserver_host}}/overview/sensors

                    Tick ID:
                    {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "schedule"}}
                    {{event.attributes.dagster_webserver_host}}/overview/schedules

                    Tick ID:
                    {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    ${var.oncall_team_critical_alert_handle}
                    {{/is_alert}}
                EOT
      name    = "Data Warehouse job {{event.attributes.job_name}} failed: {{event.attributes.run_id}}"
      options = {
        enable_logs_sample       = false
        groupby_simple_monitor   = false
        include_tags             = false
        new_group_delay          = 60
        notification_preset_name = "hide_all"
        notify_audit             = false
        on_missing_data          = "default"
        thresholds = {
          critical = 1
        }
      }
      priority = null
      query    = "events(\"source:my_apps status:error env:${var.environment} service:dagster/data-warehouse/*\").rollup(\"count\").by(\"message\").last(\"5m\") >= 1",
      tags = [
        "team:${var.datadog_team_id}",
        "env:${var.environment}"
      ]
      type = "event-v2 alert"
    }
  )
}

resource "datadog_monitor" "datastream_total_latency_alert" {
  evaluation_delay    = 300
  include_tags        = false
  on_missing_data     = "show_and_notify_no_data"
  require_full_window = false
  monitor_thresholds {
    critical          = 1
    critical_recovery = 0
  }
  tags = [
    "team:${var.datadog_team_id}",
    "env:${var.environment}"
  ]
  name    = "Datastream Total Latency Regression Alert"
  type    = "query alert"
  query   = <<EOT
    avg(last_1h):anomalies(avg:gcp.datastream.stream.total_latencies.avg{project_id:${var.data_platform_project_id}, stream_id:postgres-to-bigquery-stream}, 'agile', 2, direction='above', interval=60, alert_window='last_15m', count_default_zero='true', seasonality='weekly') >= 1
  EOT
  message = <<EOT
    Playbook:
    https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1

    ${var.oncall_team_warning_alert_handle}
  EOT
}

resource "datadog_monitor" "bigquery_bytes_billed_alert" {
  tags = [
    "team:${var.datadog_team_id}",
    "env:${var.environment}"
  ]
  include_tags        = false
  require_full_window = false
  name                = "BigQuery Bytes Billed Alert"
  type                = "query alert"
  query               = <<EOT
sum(last_1d):sum:gcp.bigquery.query.statement_scanned_bytes_billed{project_id:${var.data_platform_project_id}}.as_count() > 15000000000000
EOT
  message             = <<EOT
The scanned bytes billed for BigQuery has exceeded the expected threshold of 15TB per day. This is likely due to malformed user queries running offline (Hex, BigQuery editor).

Playbook:
https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1

${var.oncall_team_warning_alert_handle}
EOT
}

# -----------------------------------------------------------------------------
# Custom Metric for monitors below: dagster.data_warehouse.job_status
#
# This metric is created in the Datadog UI as an event-based custom metric.
#
# - Source: my_apps
# - Service: dagster/data-warehouse/*
# - Group by: env, service, status
# - Aggregation: count
#
# Description:
#   Counts the number of Dagster data warehouse job events, grouped by environment,
#   service, and status (ok or error). Used for uptime and reliability monitoring
#   of DBT and Datastream jobs.
#
# Creation Steps (Datadog UI):
#   1. Go to Events in Datadog.
#   2. Filter: source:my_apps, service:dagster/data-warehouse/*
#   3. Click "Create Metric".
#   4. Set metric name: dagster.data_warehouse.job_status
#   5. Aggregation: Count, group by env, service, status
#   6. Save the metric.
#
# Billing Note: Metrics created from events are billed as Custom Metrics.
# -----------------------------------------------------------------------------

resource "datadog_monitor" "daily_dbt_assets_uptime" {
  name                = "Daily DBT Assets Uptime Monitor"
  type                = "metric alert"
  message             = <<EOT
    Daily DBT assets pipeline uptime has dropped below the threshold.

    Playbook:
    https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1

    ${var.oncall_team_warning_alert_handle}
  EOT
  include_tags        = false
  require_full_window = false
  on_missing_data     = "default"

  query = <<EOT
sum(last_1d):dagster.data_warehouse.job_status{service:dagster/data-warehouse/daily_dbt_assets,status:error,env:${var.environment}} - dagster.data_warehouse.job_status{service:dagster/data-warehouse/daily_dbt_assets,status:ok,env:${var.environment}} > 0
EOT

  tags = [
    "team:${var.datadog_team_id}",
    "env:${var.environment}",
    "service:data-warehouse"
  ]
}

resource "datadog_monitor" "datastream_check_assets_uptime" {
  name                = "Datastream Check Assets Uptime Monitor"
  type                = "metric alert"
  message             = <<EOT
    Datastream check assets pipeline uptime has dropped below the threshold.

    Playbook:
    https://www.notion.so/apella/Data-Warehouse-Playbook-df7eddaf65f742b7bd7aa1291170ddb1

    ${var.oncall_team_warning_alert_handle}
  EOT
  include_tags        = false
  require_full_window = false
  on_missing_data     = "default"

  query = <<EOT
sum(last_1h):dagster.data_warehouse.job_status{service:dagster/data-warehouse/datastream_check_assets,status:error,env:${var.environment}} - dagster.data_warehouse.job_status{service:dagster/data-warehouse/datastream_check_assets,status:ok,env:${var.environment}} > 0
EOT

  tags = [
    "team:${var.datadog_team_id}",
    "env:${var.environment}",
    "service:data-warehouse"
  ]
}
