resource "google_datastream_private_connection" "vpc_config" {
  display_name          = "VPC Connection Config"
  location              = var.region
  private_connection_id = "vpc-connection-config"

  vpc_peering_config {
    vpc    = "projects/${var.vpc_network_project_id}/global/networks/${var.vpc_network_name}"
    subnet = var.datastream_vpc_subnet_ip_range
  }
}

data "google_secret_manager_secret_version_access" "api_db_username" {
  project = var.web_api_project_id
  secret  = "${var.environment}-api-user-name"
  version = "latest"
}

data "google_secret_manager_secret_version_access" "api_db_password" {
  project = var.web_api_project_id
  secret  = "${var.environment}-api-user-password"
  version = "latest"
}

data "google_secret_manager_secret_version_access" "ehr_db_username" {
  project = var.ehr_project_id
  secret  = var.ehr_data_warehouse_username_secret_id
}

data "google_secret_manager_secret_version_access" "ehr_db_password" {
  project = var.ehr_project_id
  secret  = var.ehr_data_warehouse_password_secret_id
}

data "google_compute_instance" "bastion_host" {
  name    = "bastion-host"
  project = var.platform_project_id
  zone    = var.bastion_host_zone
}

resource "google_datastream_connection_profile" "postgres" {
  display_name          = "Postgresql Connection"
  location              = var.region
  connection_profile_id = "pg-vpc-connection"

  private_connectivity {
    private_connection = google_datastream_private_connection.vpc_config.id
  }

  postgresql_profile {
    hostname = data.google_compute_instance.bastion_host.network_interface.0.network_ip
    port     = var.sql_proxy_datastream_port
    username = data.google_secret_manager_secret_version_access.api_db_username.secret_data
    password = data.google_secret_manager_secret_version_access.api_db_password.secret_data
    database = "postgres"
  }
}

resource "google_datastream_connection_profile" "ehr_db_postgres" {
  display_name          = "EHR DB Postgresql Connection"
  location              = var.region
  connection_profile_id = "ehr-pg-vpc-connection"

  private_connectivity {
    private_connection = google_datastream_private_connection.vpc_config.id
  }

  postgresql_profile {
    hostname = data.google_compute_instance.bastion_host.network_interface.0.network_ip
    port     = var.ehr_sql_proxy_datastream_port
    username = data.google_secret_manager_secret_version_access.ehr_db_username.secret_data
    password = data.google_secret_manager_secret_version_access.ehr_db_password.secret_data
    database = "postgres"
  }
}

resource "google_datastream_connection_profile" "bigquery" {
  display_name          = "BigQuery Connection"
  location              = var.region
  connection_profile_id = "bigquery-connection"

  bigquery_profile {}
}

resource "google_datastream_stream" "postgres_to_bigquery_stream" {
  display_name  = "Postgres to BigQuery Stream"
  location      = var.region
  stream_id     = "postgres-to-bigquery-stream"
  desired_state = "RUNNING"

  source_config {
    source_connection_profile = google_datastream_connection_profile.postgres.id
    postgresql_source_config {
      publication      = "api_publication"
      replication_slot = "api_replication_slot"
    }
  }

  destination_config {
    destination_connection_profile = google_datastream_connection_profile.bigquery.id
    bigquery_destination_config {
      data_freshness = "900s"
      single_target_dataset {
        dataset_id = google_bigquery_dataset.bronze.id
      }
    }
  }

  backfill_all {}
}

resource "google_datastream_stream" "ehr_postgres_to_bigquery_stream" {
  display_name  = "EHR DB Postgres to BigQuery Stream"
  location      = var.region
  stream_id     = "ehr-db-postgres-to-bigquery-stream"
  desired_state = "RUNNING"

  source_config {
    source_connection_profile = google_datastream_connection_profile.ehr_db_postgres.id
    postgresql_source_config {
      publication      = "data_warehouse_publication"
      replication_slot = "data_warehouse_replication_slot"
      include_objects {
        postgresql_schemas {
          schema = "scribe"
          postgresql_tables {
            table = "personnel"
          }
          postgresql_tables {
            table = "appointment_personnel"
          }
          postgresql_tables {
            table = "resources_group"
          }
          postgresql_tables {
            table = "appointment"
          }
        }
        postgresql_schemas {
          schema = "report"
          postgresql_tables {
            table = "case"
          }
        }
      }
    }
  }

  destination_config {
    destination_connection_profile = google_datastream_connection_profile.bigquery.id
    bigquery_destination_config {
      data_freshness = "900s"
      single_target_dataset {
        dataset_id = google_bigquery_dataset.bronze.id
      }
    }
  }

  backfill_all {}
}
