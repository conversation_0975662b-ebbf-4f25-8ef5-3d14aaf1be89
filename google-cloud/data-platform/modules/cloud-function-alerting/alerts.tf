
module "crash_count_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors

  type               = "metric alert"
  name               = "${var.human_readable_function_name} error count"
  query              = "avg(last_15m):sum:gcp.cloudfunctions.function.execution_count{project_id:${var.project_id},function_name:${var.function_name},!status:ok}.as_rate() / sum:gcp.cloudfunctions.function.execution_count{project_id:${var.project_id},function_name:${var.function_name}}.as_rate() > ${var.error_percent_alert_threshold}"
  threshold_critical = var.error_percent_alert_threshold
  message            = <<END_MESSAGE
  ${var.human_readable_function_name} has many crashes or timeouts.
  See the playbook: ${var.link_to_playbook}
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
  notify_no_data     = false # If the system is running well, GCP stops sending this metric
}

module "p99_execution_time_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors

  type               = "metric alert"
  name               = "${var.human_readable_function_name} p99 execution time"
  query              = "${var.execution_time_aggregator}(${var.execution_time_evaluation_window}):avg:gcp.cloudfunctions.function.execution_times.p99{project_id:${var.project_id},function_name:${var.function_name},status:ok} > ${var.p99_execution_time_alert_threshold_seconds * 1000 * 1000 * 1000}"
  threshold_critical = var.p99_execution_time_alert_threshold_seconds * 1000 * 1000 * 1000
  message            = <<END_MESSAGE
  ${var.human_readable_function_name} p99 execution time is > ${var.p99_execution_time_alert_threshold_seconds}.
  See the playbook: ${var.link_to_playbook}
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
}

module "p95_execution_time_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors


  type               = "metric alert"
  name               = "${var.human_readable_function_name} p95 execution time"
  query              = "${var.execution_time_aggregator}(${var.execution_time_evaluation_window}):avg:gcp.cloudfunctions.function.execution_times.p95{project_id:${var.project_id},function_name:${var.function_name},status:ok} > ${var.p95_execution_time_alert_threshold_seconds * 1000 * 1000 * 1000}"
  threshold_critical = var.p95_execution_time_alert_threshold_seconds * 1000 * 1000 * 1000
  message            = <<END_MESSAGE
  ${var.human_readable_function_name} p95 execution time is > ${var.p95_execution_time_alert_threshold_seconds}.
  See the playbook: ${var.link_to_playbook}
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
}
