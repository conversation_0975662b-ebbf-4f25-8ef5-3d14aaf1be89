variable "environment" {
  description = "The environment (dev, staging, prod)"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "function_name" {
  description = "the name of the cloud_function"
}

variable "human_readable_function_name" {
  description = "the human readable name of the subscription"
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "should_page" {
  type        = bool
  description = "Whether this alert should page Incident.io"
}

variable "link_to_playbook" {
  type        = string
  description = "The link to the playbook for these alerts"
}

variable "enable_monitors" {
  type        = bool
  description = "To enable alerts"
}

variable "error_percent_alert_threshold" {
  type        = number
  description = "The percent of error executions to trigger an alert"
  default     = 0.01
}

variable "execution_time_evaluation_window" {
  type        = string
  description = "How far back to look when calculating the alert aggregations for execution time"
  default     = "last_5m"
}

variable "p99_execution_time_alert_threshold_seconds" {
  type        = number
  description = "The p99 number of seconds to trigger an alert"
  default     = 25
}

variable "p95_execution_time_alert_threshold_seconds" {
  type        = number
  description = "The p95 number of seconds to trigger an alert"
  default     = 15
}

variable "min_run_count_threshold" {
  type        = number
  description = "Minimum executions in 5 minutes"
  default     = 15
}

variable "execution_time_aggregator" {
  type        = string
  description = "avg, min, or max"
  default     = "max"
}

variable "datadog_team_id" {
  type        = string
  description = "Datadog team ID for tagging alerts"
}
