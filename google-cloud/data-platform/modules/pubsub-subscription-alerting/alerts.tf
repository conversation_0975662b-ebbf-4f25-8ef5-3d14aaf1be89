
module "outstanding_messages_count_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors

  type               = "metric alert"
  name               = "${var.human_readable_subscription_name} unacked count"
  query              = "avg(last_5m):sum:gcp.pubsub.subscription.num_unacked_messages_by_region{project_id:${var.project_id},subscription_id:${var.subscription_name}} > ${var.num_unacked_messages_alert_threshold}"
  threshold_critical = var.num_unacked_messages_alert_threshold
  message            = <<END_MESSAGE
  ${var.human_readable_subscription_name} has too many unacked messages.
  See the playbook: ${var.link_to_playbook}.
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
}

module "unacked_messages_age_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors

  type               = "metric alert"
  name               = "${var.human_readable_subscription_name} unacked age"
  query              = "avg(${var.age_of_unacked_messages_averaging_window}):avg:gcp.pubsub.subscription.oldest_unacked_message_age{project_id:${var.project_id},subscription_id:${var.subscription_name}} > ${var.age_of_unacked_messages_alert_threshold_seconds}"
  threshold_critical = var.age_of_unacked_messages_alert_threshold_seconds
  no_data_timeframe  = var.age_of_unacked_messages_no_data_timeframe
  message            = <<END_MESSAGE
  ${var.human_readable_subscription_name} has old unacked messages.
  See the playbook: ${var.link_to_playbook}.
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
}

module "dead_lettered_count_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enable_monitors

  type               = "metric alert"
  name               = "${var.human_readable_subscription_name} dead letter count"
  query              = "avg(last_5m):avg:gcp.pubsub.subscription.dead_letter_message_count{project_id:${var.project_id},subscription_id:${var.subscription_name}} > ${var.dead_lettered_count_alert_threshold}"
  threshold_critical = var.dead_lettered_count_alert_threshold
  message            = <<END_MESSAGE
  ${var.human_readable_subscription_name} has dead lettered messages.
  See the playbook: ${var.link_to_playbook}.
  ${local.datadog_slack_channel}
  ${local.page_handle}
  END_MESSAGE
  tags               = local.datadog_tags
  notify_no_data     = false # If the system is running well, GCP stops sending this metric
}
