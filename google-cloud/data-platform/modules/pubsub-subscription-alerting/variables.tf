variable "environment" {
  description = "The environment (dev, staging, prod)"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "subscription_name" {
  description = "the name of the subscription (google_pubsub_subscription.your_sub.name)"
}

variable "human_readable_subscription_name" {
  description = "the human readable name of the subscription"
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "should_page" {
  type        = bool
  description = "Whether this alert should page Incident.io"
}

variable "datadog_team_id" {
  type        = string
  description = "Datadog team ID"
}

variable "num_unacked_messages_alert_threshold" {
  type        = number
  description = "The number of unacked messages in the subscription to trigger an alert"
  default     = 100
}

variable "age_of_unacked_messages_alert_threshold_seconds" {
  type        = number
  description = "The age of unacked messages, in seconds, in the subscription to trigger an alert"
  default     = 60
}

variable "age_of_unacked_messages_averaging_window" {
  type        = string
  description = "The averaging window to use for the age of unacked messages alert"
  default     = "last_5m"
}

variable "age_of_unacked_messages_no_data_timeframe" {
  type        = number
  description = "The timeframe to check for no data"
  default     = 1440
}

variable "dead_lettered_count_alert_threshold" {
  type        = number
  description = "The count of dead lettered messages in the subscription to trigger an alert"
  default     = 1
}

variable "link_to_playbook" {
  type        = string
  description = "The link to the playbook for these alerts"
}

variable "enable_monitors" {
  type        = bool
  description = "To enable alerts"
}
