resource "google_service_account" "service_account" {
  project      = var.project_id
  account_id   = "openmetadata-sa"
  display_name = "OpenMetadata service account"
}

// from here https://docs.open-metadata.org/latest/connectors/database/bigquery/create-credentials
resource "google_project_iam_member" "service_account_roles" {
  for_each = toset([
    "roles/logging.viewer",
    "roles/bigquery.dataViewer",
    "roles/bigquery.readSessionUser",
    "roles/bigquery.jobUser"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.service_account.email}"
}