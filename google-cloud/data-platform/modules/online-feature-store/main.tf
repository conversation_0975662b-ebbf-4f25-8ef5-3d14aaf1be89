resource "google_bigtable_table" "cases_features" {
  name          = "ml_features_cases"
  instance_name = var.bigtable_instance_name

  deletion_protection = "PROTECTED"

  column_family {
    family = "features"
  }
}

resource "google_bigtable_table" "image_features" {
  name          = "ml_features_image_features"
  instance_name = var.bigtable_instance_name

  deletion_protection = "PROTECTED"

  column_family {
    family = "features"
  }
}

resource "google_bigtable_gc_policy" "cases_gc_policy" {
  instance_name   = var.bigtable_instance_name
  table           = google_bigtable_table.cases_features.name
  column_family   = "features"
  deletion_policy = "ABANDON"

  # Delete data when it it is superseded by a new version OR when it becomes 30 days old
  gc_rules = <<EOF
      {
        "mode": "union",
        "rules": [
            {
                "max_age": "30d"
            },
            {
                "max_version": ${var.forecast_max_versions}
            }
        ]
      }
      EOF
}

resource "google_bigtable_gc_policy" "image_features_gc_policy" {
  instance_name   = var.bigtable_instance_name
  table           = google_bigtable_table.image_features.name
  column_family   = "features"
  deletion_policy = "ABANDON"

  # Delete data when it it is superseded by a new version OR when it becomes 2 days old
  gc_rules = <<EOF
      {
        "mode": "union",
        "rules": [
            {
                "max_age": "2d"
            },
            {
                "max_version": 1
            }
        ]
      }
      EOF
}

resource "google_bigtable_table" "forecast_results" {
  name          = "ml_forecast_results"
  instance_name = var.bigtable_instance_name

  deletion_protection = "PROTECTED"

  column_family {
    family = "features"
  }
}

resource "google_bigtable_gc_policy" "forecast_results_gc_policy" {
  instance_name   = var.bigtable_instance_name
  table           = google_bigtable_table.forecast_results.name
  column_family   = "features"
  deletion_policy = "ABANDON"

  # Delete data when it is superseded by a new version OR when it becomes 7 days old
  gc_rules = <<EOF
      {
        "mode": "union",
        "rules": [
            {
                "max_age": "7d"
            },
            {
                "max_version": ${var.forecast_max_versions}
            }
        ]
      }
      EOF
}

resource "google_bigtable_table" "event_model_forecasts" {
  name          = "ml_features_event_model_forecasts"
  instance_name = var.bigtable_instance_name

  deletion_protection = "PROTECTED"

  column_family {
    family = "features"
  }
}

resource "google_bigtable_gc_policy" "event_model_forecasts_gc_policy" {
  instance_name   = var.bigtable_instance_name
  table           = google_bigtable_table.event_model_forecasts.name
  column_family   = "features"
  deletion_policy = "ABANDON"

  # Delete data when it it is superseded by a new version OR when it becomes 1 hour old
  gc_rules = <<EOF
      {
        "mode": "union",
        "rules": [
            {
                "max_age": "1h"
            },
            {
                "max_version": 1
            }
        ]
      }
      EOF
}

resource "google_bigtable_table" "event_model_features" {
  name          = "ml_event_model_features"
  instance_name = var.bigtable_instance_name

  deletion_protection = "PROTECTED"

  column_family {
    family = "features"
  }
}

resource "google_bigtable_gc_policy" "event_model_features_gc_policy" {
  instance_name   = var.bigtable_instance_name
  table           = google_bigtable_table.event_model_features.name
  column_family   = "features"
  deletion_policy = "ABANDON"

  # Delete data when it is superseded by a new version OR when it becomes 2 days old
  gc_rules = <<EOF
      {
        "mode": "union",
        "rules": [
            {
                "max_age": "2d"
            },
            {
                "max_version": 1
            }
        ]
      }
      EOF
} 