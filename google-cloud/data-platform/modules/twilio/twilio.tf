locals {
  accessor_members = [
    for email in var.twilio_secret_accessors : "serviceAccount:${email}"
  ]
}

resource "google_secret_manager_secret" "twilio_account_sid_secret" {
  secret_id = "${var.environment}-twilio-account-sid"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "twilio_account_sid_version" {
  secret = google_secret_manager_secret.twilio_account_sid_secret.id

  secret_data = var.twilio_account_sid
}

resource "google_secret_manager_secret_iam_member" "twilio_account_sid_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.twilio_account_sid_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}

resource "google_secret_manager_secret" "twilio_auth_token_secret" {
  secret_id = "${var.environment}-twilio-auth-token"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "twilio_auth_token_version" {
  secret = google_secret_manager_secret.twilio_auth_token_secret.id

  secret_data = var.twilio_auth_token
}

resource "google_secret_manager_secret_iam_member" "twilio_auth_token_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.twilio_auth_token_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}

resource "google_secret_manager_secret" "twilio_from_number_secret" {
  secret_id = "${var.environment}-twilio-from-number"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "twilio_from_number_version" {
  secret = google_secret_manager_secret.twilio_from_number_secret.id

  secret_data = var.twilio_from_number
}

resource "google_secret_manager_secret_iam_member" "twilio_from_number_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.twilio_from_number_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}

resource "google_secret_manager_secret" "sendgrid_api_key_secret" {
  secret_id = "${var.environment}-sendgrid-api-key"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "sendgrid_api_key_version" {
  secret = google_secret_manager_secret.sendgrid_api_key_secret.id

  secret_data = var.sendgrid_api_key
}

resource "google_secret_manager_secret_iam_member" "sendgrid_api_key_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.sendgrid_api_key_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}
