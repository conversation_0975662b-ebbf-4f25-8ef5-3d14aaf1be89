variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

/*****************************************************************************
  Twilio Key
******************************************************************************/

variable "twilio_account_sid" {
  description = "Account id for Twilio"
  type        = string
  sensitive   = true
}

variable "twilio_auth_token" {
  description = "Auth token for Twilio"
  type        = string
  sensitive   = true
}

variable "twilio_from_number" {
  description = "Phone number Twi<PERSON> sends from"
  type        = string
  sensitive   = true
}

variable "sendgrid_api_key" {
  description = "Sendgrid API Key"
  type        = string
  sensitive   = true
}

variable "twilio_secret_accessors" {
  description = "List of accounts to sign up for access to Twilio secrets"
  type        = list(string)
}
