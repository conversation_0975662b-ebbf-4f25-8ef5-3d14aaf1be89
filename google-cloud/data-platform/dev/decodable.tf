module "decodable" {
  source = "../modules/decodable"

  project_id      = var.project_id
  environment     = var.environment
  region          = var.region
  datadog_api_key = var.datadog_api_key
  datadog_app_key = var.datadog_app_key
  event_model_pubsub_topics = [
    module.realtime_processing_services.image_processing_outputs_topic.name,
    module.realtime_processing_services.image_embedding_outputs_topic.name
  ]
  oncall_team_critical_alert_handle = "@slack-bot-ops-data-platform-${var.environment}"
  deployment_service_account_email  = local.nonprod_gh_runner_sa
}
