/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

region         = "us-central1"
project_id     = "dev-data-platform-439b4c"
project_number = 439236065502
environment    = "dev"

customer_upload_bucket_changes_topic = "customer-upload-changes"
customer_deletion_topic              = "customer-deletion-topic"

media_asset_deletion_processor_sa_email = "<EMAIL>"

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "dev-data-platform"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-dev-environment"
}

/******************************************
 Customer data bucket Resource Related Variables
 *****************************************/

customer_uploader_group = "<EMAIL>"

# XXX - refactor to replace customer list with customer_organization_ids
customer_list = [
  "apellainternal0",
  "sacred-heart",
  "testorg0"
]

customer_organization_ids = [
  "apella_internal_0",
  "sacred_heart",
  "test_org_0"
]

customer_data_bucket_iam_members = [{
  role   = "roles/storage.admin"
  member = "group:<EMAIL>"
}]

cors_origins         = ["https://internal.dev.apella.io", "http://localhost:3000", "http://localhost:3020", "https://dashboard.dev.apella.io"]
cors_methods         = ["GET"]
cors_response_header = ["Content-Type", "x-goog-resumable"]
cors_max_age_seconds = 3600

customer_data_lifecycle_rules = [{
  action = {
    type          = "SetStorageClass"
    storage_class = "COLDLINE"
  }
  condition = {
    age        = 7
    with_state = "ANY"
  }
}]

customer_audio_data_lifecycle_rules = [{
  action = {
    type          = "SetStorageClass"
    storage_class = "COLDLINE"
  }
  condition = {
    age        = 7
    with_state = "ANY"
  }
}]


data_health_notification_channel_id = 9486563933143814926
image_upload_lower_threshold        = 0.6
image_upload_upper_threshold        = 1

media_asset_service_project  = "dev-media-asset-93e8c3"
media_asset_service_sa_email = "<EMAIL>"

forecast_dataset = {
  dataset_id  = "case_forecasting"
  description = "This dataset encapsulates all tables related to case forecasting metrics."
}
