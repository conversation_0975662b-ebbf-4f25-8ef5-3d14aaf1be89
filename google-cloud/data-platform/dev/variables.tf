/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "project_number" {
  description = "GCP Project Number"
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "customer_upload_bucket_changes_topic" {
  description = "Name of pubsub topic for changes to customer upload bucket"
}

variable "customer_deletion_topic" {
  description = "Name of pubsub topic for deletion of object in customer data buckets"
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

/******************************************
	GCS Bucket Related Variables
 *****************************************/
variable "customer_list" {
  description = "The names of customers to create buckets for"
  type        = list(string)
  default     = []
}

variable "customer_uploader_group" {
  description = "Group which contains customer uploaders"
  type        = string
}

variable "customer_organization_ids" {
  description = "The organization ids of customers"
  type        = list(string)
  default     = []
}

variable "customer_data_bucket_iam_members" {
  description = "The list of IAM members to grant permissions on the bucket."
  type = list(object({
    role   = string
    member = string
  }))
  default = []
}

variable "customer_data_lifecycle_rules" {
  description = "The bucket's Lifecycle Rules configuration."
  type = list(object({
    action    = any
    condition = any
  }))
  default = []
}

variable "customer_audio_data_lifecycle_rules" {
  description = "The audio bucket's Lifecycle Rules configuration."
  type = list(object({
    action    = any
    condition = any
  }))
  default = []
}

variable "cors_origins" {
  description = "The list of website origins allowed to access certain buckets"
  type        = list(string)
  default     = []
}

variable "cors_methods" {
  description = "The list of website methods allowed to access certain buckets"
  type        = list(string)
  default     = []
}

variable "cors_response_header" {
  description = "The list of response headers"
  type        = list(string)
  default     = ["*"]
}


variable "cors_max_age_seconds" {
  description = "The duration for which the results of a preflight request can be cached."
  type        = number
  default     = 3000
}

variable "media_asset_service_project" {
  description = "Media Asset Service project id"
  type        = string
  default     = "dev-media-asset-93e8c3"
}

/*****************************************************************************
  Vanta Key
******************************************************************************/
variable "vanta_key" {
  description = "Vanta Account Key"
  type        = string
  sensitive   = true
}

variable "data_health_notification_channel_id" {
  description = "The notification channel for slack"
}

variable "image_upload_upper_threshold" {
  description = "The upper image upload threshold"
}

variable "image_upload_lower_threshold" {
  description = "The lower image upload threshold"
}

variable "slack_api_token" {
  type        = string
  description = "Slack API Token"
  sensitive   = true
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Amplitude Keys
******************************************************************************/

variable "amplitude_dashboard_api_key" {
  description = "Api key for Amplitude Dashboard project"
  type        = string
  sensitive   = true
}

variable "amplitude_dashboard_secret_key" {
  description = "Secret key for Amplitude Dashboard project"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Twilio Key
******************************************************************************/

variable "twilio_account_sid" {
  description = "Account id for Twilio"
  type        = string
  sensitive   = true
}

variable "twilio_auth_token" {
  description = "Auth token for Twilio"
  type        = string
  sensitive   = true
}

variable "twilio_from_number" {
  description = "Phone number Twilio sends from"
  type        = string
  sensitive   = true
}

variable "sendgrid_api_key" {
  description = "Sendgrid API Key"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Service Accounts
******************************************************************************/

variable "media_asset_deletion_processor_sa_email" {
  description = "The service account email for the media asset deletion processor"
}

variable "media_asset_service_sa_email" {
  description = "The service account email for the media asset service"
}

/*****************************************************************************
  Slack Key
******************************************************************************/

variable "slack_auth_token" {
  description = "Auth token for slack"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Clearml Api Entered from TF secrets
******************************************************************************/

variable "clearml_api_key" {
  description = "Api key token for clearml"
  type        = string
  sensitive   = true
}

variable "clearml_api_secret" {
  description = "Api secret token for clearml"
  type        = string
  sensitive   = true
}

variable "forecast_dataset" {
  description = "The BigQuery dataset used to organize forecast tables"
  type = object({
    dataset_id  = string
    description = string
  })
}
