module "realtime_processing_services" {
  source = "../modules/realtime_processing_services"

  project_id     = var.project_id
  project_number = var.project_number
  region         = var.region
  zone           = var.zone
  api_server_sa  = local.api_server_sa
  environment    = var.environment
  labels         = var.labels

  internal_project_id  = local.internal_project.project_id
  vpc_access_connector = local.vpc_access_connector

  dataflow_sa_email                       = local.dataflow_sa_email
  ml_project_sa_email                     = local.ml_project.tf_sa_email
  dataflow_tmp_bucket                     = local.tmp_bucket
  realtime_image_processing_outputs_table = "${local.ml_project.project_id}:dev_realtime.image_processing_output"
  realtime_image_embeddings_outputs_table = "${local.image_embeddings_table.project}:${local.image_embeddings_table.dataset_id}.${local.image_embeddings_table.table_id}"

  forecasting_feature_store_topic_name          = module.decodable.forecasting_features_topic.name
  shared_forecasting_feature_store_topic_name   = module.decodable.shared_forecasting_feature_store_topic.name
  event_model_features_topic_name               = module.decodable.event_model_features_topic.name
  realtime_event_model_decodable_features_table = "${local.event_model_decodable_features_table.project}:${local.event_model_decodable_features_table.dataset_id}.${local.event_model_decodable_features_table.table_id}"

  network_project_id  = local.network-project.project_id
  subnetwork          = "nonprod-central1-compute"
  gcs_service_account = data.google_storage_project_service_account.gcs_account
  customer_list       = var.customer_list

  datadog_api_key = var.datadog_api_key
  datadog_app_key = var.datadog_app_key

  media_asset_service_project = var.media_asset_service_project

  team_metadata = {
    forecasting = {
      should_page     = false
      slack_channel   = "bot-ops-forecasting-${var.environment}"
      datadog_team_id = "forecasting"
    }
    computer_vision = {
      should_page     = false
      slack_channel   = "bot-ops-computer-vision-${var.environment}"
      datadog_team_id = "computer-vision"
    }
  }
}
