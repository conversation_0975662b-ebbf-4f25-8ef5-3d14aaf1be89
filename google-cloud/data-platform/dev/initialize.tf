/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-data-platform-dev"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.25"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
  }
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "terraform_remote_state" "auth0_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "auth0-dev"
    }
  }
}

data "terraform_remote_state" "nonprod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "web_api_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-web-api-dev"
    }
  }
}

data "terraform_remote_state" "platform_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-platform-nonprod"
    }
  }
}

data "terraform_remote_state" "ml_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-machine-learning-dev"
    }
  }
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_network" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "dev_internal" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-internal-dev"
    }
  }
}

data "terraform_remote_state" "ehr_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "ehr-dev"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}
provider "google-beta" {
  project = var.project_id
  region  = var.region
}

data "google_organization" "org" {
  domain = var.domain_name
}

locals {
  org_id                                = data.google_organization.org.org_id
  api_server_sa                         = data.terraform_remote_state.web_api_project.outputs.api_server_sa
  nonprod_gh_runner_sa                  = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  ml_project                            = data.terraform_remote_state.nonprod_project_factory.outputs.dev-ml-project
  tmp_bucket                            = "${var.environment}-apella-dataflow-tmp"
  dataflow_sa_email                     = data.terraform_remote_state.nonprod_security.outputs.nonprod-dataflow-sa
  dev_project_number                    = data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_number
  dev_project_id                        = data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_id
  web_api_project                       = data.terraform_remote_state.nonprod_project_factory.outputs.dev-web-api-project
  network-project                       = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-network-project
  platform_project                      = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-platform-project
  internal_project                      = data.terraform_remote_state.nonprod_project_factory.outputs.dev-internal-project
  ehr_project                           = data.terraform_remote_state.nonprod_project_factory.outputs.dev-ehr-project
  dataflow-default-sa                   = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  ml_model_sa                           = data.terraform_remote_state.ml_project.outputs.automl_model_training_sa
  rtedgemodel_sa                        = data.terraform_remote_state.nonprod_security.outputs.nonprod-rtedgemodel-sa
  vpc_access_connector                  = data.terraform_remote_state.nonprod_network.outputs.vpc_access_connectors[var.region].id
  metabase_sa_email                     = data.terraform_remote_state.dev_internal.outputs.metabase_sa_email
  sql_proxy_datastream_port_dev         = data.terraform_remote_state.platform_project.outputs.cloudsqlproxy_datastream_port_dev
  ehr_sql_proxy_datastream_port_dev     = data.terraform_remote_state.platform_project.outputs.ehr_cloudsqlproxy_datastream_port_dev
  ehr_data_warehouse_username_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_username_secret_id
  ehr_data_warehouse_password_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_password_secret_id
  bastion_host_zone                     = data.terraform_remote_state.platform_project.outputs.bastion_host_zone
  data_platform_tf_sa_email             = data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.tf_sa_email
  nonprod_vpc_network_name              = data.terraform_remote_state.nonprod_network.outputs.network_name
  mlops_dags_sa_email                   = data.terraform_remote_state.ml_project.outputs.mlops_dags_sa.email
  cubejs_sa_email                       = data.terraform_remote_state.web_api_project.outputs.cubejs_sa_email
  auth0_management_api_client_id        = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_id
  auth0_management_api_client_secret    = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_secret
  ml_services_sa_email                  = data.terraform_remote_state.ml_project.outputs.ml_services_sa.email
  ray_sa_email                          = data.terraform_remote_state.dev_internal.outputs.ray_sa.email
  image_embeddings_table                = data.terraform_remote_state.ml_project.outputs.image_embeddings_table
  event_model_decodable_features_table  = data.terraform_remote_state.ml_project.outputs.event_model_decodable_features_table
}

/******************************************
  Vanta Installer
 *****************************************/

locals {
  vanta_install_script = <<EOT
VANTA_KEY="${var.vanta_key}" bash -c \
"$(curl -L https://raw.githubusercontent.com/VantaInc/vanta-agent-scripts/master/install-linux.sh)"
EOT
}
