/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-data-platform-nonprod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.25"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
  }
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "terraform_remote_state" "auth0_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "auth0-staging"
    }
  }
}

data "terraform_remote_state" "nonprod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "platform_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-platform-nonprod"
    }
  }
}

data "terraform_remote_state" "web_api_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-web-api-staging"
    }
  }
}

data "terraform_remote_state" "ml_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-machine-learning-staging"
    }
  }
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_network" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "staging_internal" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-internal-staging"
    }
  }
}

data "terraform_remote_state" "ehr_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "ehr-staging"
    }
  }
}


provider "google" {
  project = var.project_id
  region  = var.region
}

data "google_organization" "org" {
  domain = var.domain_name
}

locals {
  org_id                                = data.google_organization.org.org_id
  api_server_sa                         = data.terraform_remote_state.web_api_project.outputs.api_server_sa
  nonprod_gh_runner_sa                  = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  ml_project                            = data.terraform_remote_state.nonprod_project_factory.outputs.staging-ml-project
  tmp_bucket                            = "${var.environment}-apella-dataflow-tmp"
  dataflow_sa_email                     = data.terraform_remote_state.nonprod_security.outputs.nonprod-dataflow-sa
  staging_project_number                = data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number
  web_api_project                       = data.terraform_remote_state.nonprod_project_factory.outputs.staging-web-api-project
  platform_project                      = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-platform-project
  internal_project                      = data.terraform_remote_state.nonprod_project_factory.outputs.staging-internal-project
  ehr_project                           = data.terraform_remote_state.nonprod_project_factory.outputs.staging-ehr-project
  network-project                       = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-network-project
  dataflow-default-sa                   = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  ml_model_sa                           = data.terraform_remote_state.ml_project.outputs.automl_model_training_sa
  rtedgemodel_sa                        = data.terraform_remote_state.nonprod_security.outputs.nonprod-rtedgemodel-sa
  vpc_access_connector                  = data.terraform_remote_state.nonprod_network.outputs.vpc_access_connectors[var.region].id
  metabase_sa_email                     = data.terraform_remote_state.staging_internal.outputs.metabase_sa_email
  mlops_dags_sa_email                   = data.terraform_remote_state.ml_project.outputs.mlops_dags_sa.email
  nonprod_vpc_network_name              = data.terraform_remote_state.nonprod_network.outputs.network_name
  bastion_host_zone                     = data.terraform_remote_state.platform_project.outputs.bastion_host_zone
  sql_proxy_datastream_port_staging     = data.terraform_remote_state.platform_project.outputs.cloudsqlproxy_datastream_port_staging
  ehr_sql_proxy_datastream_port_staging = data.terraform_remote_state.platform_project.outputs.ehr_cloudsqlproxy_datastream_port_staging
  ehr_data_warehouse_username_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_username_secret_id
  ehr_data_warehouse_password_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_password_secret_id
  data_platform_tf_sa_email             = data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.tf_sa_email
  staging_project_id                    = data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_id
  cubejs_sa_email                       = data.terraform_remote_state.web_api_project.outputs.cubejs_sa_email
  auth0_management_api_client_id        = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_id
  auth0_management_api_client_secret    = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_secret
  ray_sa_email                          = data.terraform_remote_state.staging_internal.outputs.ray_sa.email
  image_embeddings_table                = data.terraform_remote_state.ml_project.outputs.image_embeddings_table
}

/******************************************
  Vanta Installer
 *****************************************/

locals {
  vanta_install_script = <<EOT
VANTA_KEY="${var.vanta_key}" bash -c \
"$(curl -L https://raw.githubusercontent.com/VantaInc/vanta-agent-scripts/master/install-linux.sh)"
EOT
}
