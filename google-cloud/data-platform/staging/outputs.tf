
output "customer_data_buckets" {
  value       = module.customer_data_bucket.*.bucket.name
  description = "List of provisioned customer buckets"
}

output "customer_audio_buckets" {
  value = {
    for k, v in module.customer_audio_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer audio buckets"
}

output "customer_video_buckets" {
  value = {
    for k, v in module.customer_video_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer video buckets"
}

output "customer_image_buckets" {
  value = {
    for k, v in module.customer_image_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer image buckets"
}

output "edge_customer_uploader_sa" {
  value = google_service_account.edge_customer_uploader
}

output "image_embedding_outputs_topic_id" {
  value = module.realtime_processing_services.image_embedding_outputs_topic.id
}

output "realtime_processing_services_sa" {
  value = module.realtime_processing_services.realtime_processing_sa
}

output "dagster_realtime_dags_sa" {
  value = module.dagster_realtime_dags_sa.sa
}

output "data_platform_sa_email" {
  value = data.google_storage_project_service_account.gcs_account.email_address
}
