
locals {
  customer_data_buckets = module.customer_data_bucket.*.bucket.name
  customer_audio_buckets = {
    for k, v in module.customer_audio_bucket : k => v.bucket.name
  }
  customer_image_buckets = {
    for k, v in module.customer_image_bucket : k => v.bucket.name
  }
  customer_video_buckets = {
    for k, v in module.customer_video_bucket : k => v.bucket.name
  }
  all_customer_data_buckets = toset(concat(values(local.customer_audio_buckets), values(local.customer_image_buckets), values(local.customer_video_buckets)))
}

# We've added staging notifications here, but there is no actual data/processor to process.
resource "google_storage_notification" "customer_data_notification_hls" {
  count              = length(local.customer_data_buckets)
  bucket             = element(local.customer_data_buckets, count.index)
  payload_format     = "JSON_API_V1"
  topic              = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-upload-topic"
  event_types        = ["OBJECT_FINALIZE"]
  object_name_prefix = "hls/"
  custom_attributes = {
    asset_type = "hls"
    customer = element(
      split("-", element(local.customer_data_buckets, count.index)),
      2
    )
    org_id = element(var.customer_organization_ids, count.index)
  }
}

resource "google_storage_notification" "customer_data_delete_notification" {
  count              = length(local.customer_data_buckets)
  bucket             = element(local.customer_data_buckets, count.index)
  payload_format     = "JSON_API_V1"
  topic              = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types        = ["OBJECT_DELETE"]
  object_name_prefix = "videos_streaming/"
  custom_attributes = {
    customer = element(
      split("-", element(local.customer_data_buckets, count.index)),
      2
    )
    org_id = element(var.customer_organization_ids, count.index)
  }
}

resource "google_storage_notification" "customer_data_delete_notification_hls" {
  count              = length(local.customer_data_buckets)
  bucket             = element(local.customer_data_buckets, count.index)
  payload_format     = "JSON_API_V1"
  topic              = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types        = ["OBJECT_DELETE"]
  object_name_prefix = "hls/"
  custom_attributes = {
    asset_type = "hls"
    customer = element(
      split("-", element(local.customer_data_buckets, count.index)),
      2
    )
    org_id = element(var.customer_organization_ids, count.index)
  }
}

resource "google_storage_notification" "customer_data_notification_audio" {
  for_each       = local.customer_audio_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-upload-topic"
  event_types    = ["OBJECT_FINALIZE"]
  custom_attributes = {
    asset_type = "audio"
    customer = element(
      split("-", each.value),
      2
    )
  }
}

resource "google_storage_notification" "customer_data_delete_notification_audio" {
  for_each       = local.customer_audio_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types    = ["OBJECT_DELETE"]
  custom_attributes = {
    asset_type = "audio"
    customer = element(
      split("-", each.value),
      2
    )
  }
}

resource "google_storage_notification" "customer_data_notification_image" {
  for_each       = local.customer_image_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-upload-topic"
  event_types    = ["OBJECT_FINALIZE"]
  custom_attributes = {
    asset_type = "image"
    customer = element(
      split("-", each.value),
      2
    )
  }
}

resource "google_storage_notification" "customer_data_delete_notification_image" {
  for_each       = local.customer_image_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types    = ["OBJECT_DELETE"]
  custom_attributes = {
    asset_type = "image"
    customer = element(
      split("-", each.value),
      2
    )
  }
}

resource "google_storage_notification" "customer_data_notification_video" {
  for_each       = local.customer_video_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-upload-topic"
  event_types    = ["OBJECT_FINALIZE"]
  custom_attributes = {
    asset_type = "hls"
    customer = element(
      split("-", each.value),
      2
    )
  }
}

resource "google_storage_notification" "customer_data_delete_notification_video" {
  for_each       = local.customer_video_buckets
  bucket         = each.value
  payload_format = "JSON_API_V1"
  topic          = "projects/${var.media_asset_service_project}/topics/${var.environment}-asset-delete-topic"
  event_types    = ["OBJECT_DELETE"]
  custom_attributes = {
    asset_type = "hls"
    customer = element(
      split("-", each.value),
      2
    )
  }
}


// Enable notifications by giving the correct IAM permission to the unique service account.

data "google_storage_project_service_account" "gcs_account" {
}

resource "google_pubsub_topic_iam_binding" "binding" {
  topic   = google_pubsub_topic.customer_notification_topic.id
  role    = "roles/pubsub.publisher"
  members = ["serviceAccount:${data.google_storage_project_service_account.gcs_account.email_address}"]
}

// End enabling notifications

resource "google_pubsub_topic" "customer_notification_topic" {
  name   = "${var.environment}-${var.customer_upload_bucket_changes_topic}"
  labels = var.labels
}
