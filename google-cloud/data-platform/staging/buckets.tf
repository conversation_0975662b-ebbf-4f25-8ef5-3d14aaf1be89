/******************************************
	Customer Buckets
 *****************************************/

module "customer_data_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  count                       = length(var.customer_list)
  bucket_name                 = "${var.environment}-customer-${element(var.customer_list, count.index)}-data"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  iam_members                 = var.customer_data_bucket_iam_members
  labels                      = var.labels
  lifecycle_rules             = var.customer_data_lifecycle_rules


  cors = {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }
}

module "customer_audio_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  for_each                    = toset(var.customer_list)
  bucket_name                 = "${var.environment}-customer-${each.value}-audio-data"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  iam_members                 = var.customer_data_bucket_iam_members
  labels                      = var.labels
  lifecycle_rules             = var.customer_audio_data_lifecycle_rules

  cors = {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }
}

module "customer_video_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  for_each                    = toset(var.customer_list)
  bucket_name                 = "${var.environment}-customer-${each.value}-video-data"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  iam_members                 = var.customer_data_bucket_iam_members
  labels                      = var.labels
  lifecycle_rules             = var.customer_data_lifecycle_rules

  cors = {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }
}

module "customer_image_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  for_each                    = toset(var.customer_list)
  bucket_name                 = "${var.environment}-customer-${each.value}-image-data"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  iam_members                 = var.customer_data_bucket_iam_members
  labels                      = var.labels
  lifecycle_rules             = var.customer_data_lifecycle_rules

  cors = {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }
}

# https://linear.app/apella/issue/PS-963/tf-data-platform-has-iam-role-in-incorrect-location
resource "google_storage_bucket_iam_member" "edge_customer_uploads" {
  for_each = {
    for k, v in module.customer_audio_bucket : k => v.bucket.name
  }
  bucket = each.value
  role   = "roles/storage.objectCreator"
  member = "serviceAccount:${google_service_account.edge_customer_uploader.email}"
}

module "public_tensorflow_models" {
  # This bucket is "public" in the sense that a user with the right permissions
  # can access anything in it via the api server.  The bucket is still secured
  # against unauthenticated requests.
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-public-tensorflow-models"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels

  cors = {
    origin          = var.cors_origins
    method          = var.cors_methods
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }
}

# Bucket for temp folder for DataFlow
module "dataflow_tmp_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = local.tmp_bucket
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}

# Bucket for nostalgic android phone videos
module "nostalgic_android_phone_videos" {
  source                      = "app.terraform.io/apella/cloud-storage/google"
  version                     = "1.2.9"
  storage_class               = "COLDLINE"
  bucket_name                 = "${var.environment}-historical_artifacts"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}
