locals {
  roleListGithubRunner = [
    "roles/cloudfunctions.admin",  # To deploy cloud functions
    "roles/iam.serviceAccountUser" # To deploy cloud functions that run as a different service account
  ]
}

#github runner service account
resource "google_project_iam_member" "github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.nonprod_gh_runner_sa}"
}
