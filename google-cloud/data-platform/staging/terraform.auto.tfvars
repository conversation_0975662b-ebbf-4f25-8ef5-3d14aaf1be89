/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

region         = "us-central1"
project_id     = "staging-data-platform-6227fa"
project_number = 840324366845
environment    = "staging"

customer_upload_bucket_changes_topic = "customer-upload-changes"
customer_deletion_topic              = "customer-deletion-topic"

media_asset_deletion_processor_sa_email = "<EMAIL>"

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "staging-data-platform"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-staging-environment"
}

/******************************************
 Customer data bucket Resource Related Variables
 *****************************************/

customer_uploader_group = "<EMAIL>"

customer_list = [
  "apella",
  "sacred-heart"
]

customer_organization_ids = [
  "apella_internal_0",
  "sacred_heart"
]

customer_data_bucket_iam_members = [{
  role   = "roles/storage.admin"
  member = "group:<EMAIL>"
}]

cors_origins         = ["https://internal.staging.apella.io", "http://localhost:3000", "https://dashboard.staging.apella.io"]
cors_methods         = ["GET"]
cors_response_header = ["Content-Type", "x-goog-resumable"]
cors_max_age_seconds = 3600

customer_data_lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 28
    with_state = "ANY"
  }
}]

customer_audio_data_lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 7
    with_state = "ANY"
  }
}]

data_health_notification_channel_id = 12485122513921473890
image_upload_lower_threshold        = 0
image_upload_upper_threshold        = 0

media_asset_service_project  = "staging-media-asset-6be7e3"
media_asset_service_sa_email = "<EMAIL>"

forecast_dataset = {
  dataset_id  = "case_forecasting"
  description = "This dataset encapsulates all tables related to case forecasting metrics."
}
