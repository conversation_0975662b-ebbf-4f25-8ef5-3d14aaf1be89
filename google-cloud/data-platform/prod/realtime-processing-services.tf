module "realtime_processing_services" {
  source = "../modules/realtime_processing_services"

  project_id     = var.project_id
  project_number = var.project_number
  region         = var.region
  zone           = var.zone
  api_server_sa  = local.api_server_sa
  environment    = var.environment
  labels         = var.labels

  internal_project_id = local.internal_project.project_id

  dataflow_sa_email                       = local.dataflow_sa_email
  ml_project_sa_email                     = local.ml_project.tf_sa_email
  dataflow_tmp_bucket                     = local.tmp_bucket
  realtime_image_processing_outputs_table = "${local.ml_project.project_id}:prod_realtime.image_processing_output"
  realtime_image_embeddings_outputs_table = "${local.image_embeddings_table.project}:${local.image_embeddings_table.dataset_id}.${local.image_embeddings_table.table_id}"
  network_project_id                      = local.network-project.project_id
  subnetwork                              = "prod-central1-compute"
  gcs_service_account                     = data.google_storage_project_service_account.gcs_account
  customer_list                           = var.customer_list
  vpc_access_connector                    = local.vpc_access_connector

  forecasting_feature_store_topic_name          = module.decodable.forecasting_features_topic.name
  shared_forecasting_feature_store_topic_name   = module.decodable.shared_forecasting_feature_store_topic.name
  event_model_features_topic_name               = module.decodable.event_model_features_topic.name
  realtime_event_model_decodable_features_table = "${local.event_model_decodable_features_table.project}:${local.event_model_decodable_features_table.dataset_id}.${local.event_model_decodable_features_table.table_id}"

  datadog_api_key = var.datadog_api_key
  datadog_app_key = var.datadog_app_key

  team_metadata = {
    computer_vision = {
      slack_channel   = null
      should_page     = true
      datadog_team_id = "computer-vision"
    },
    forecasting = {
      slack_channel   = "bot-ops-forecasting"
      should_page     = true
      datadog_team_id = "forecasting"
    }
  }

  media_asset_service_project = var.media_asset_service_project
}
