module "slack" {
  source           = "../modules/slack"
  environment      = var.environment
  project_id       = var.project_id
  region           = var.region
  slack_auth_token = var.slack_auth_token
  slack_secret_accessors = [
    module.dagster_realtime_dags_sa.sa.email,
    data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email
  ]
}


moved {
  from = module.dagster-realtime.google_secret_manager_secret.slack_auth_token_secret
  to   = module.slack.google_secret_manager_secret.slack_auth_token_secret
}

moved {
  from = module.dagster-realtime.google_secret_manager_secret_iam_member.realtime_slack_auth_token
  to   = module.slack.google_secret_manager_secret_iam_member.slack_auth_token_iams["serviceAccount:<EMAIL>"]
}

moved {
  from = module.dagster-realtime.google_secret_manager_secret_version.slack_auth_token_version
  to   = module.slack.google_secret_manager_secret_version.slack_auth_token_version
}
