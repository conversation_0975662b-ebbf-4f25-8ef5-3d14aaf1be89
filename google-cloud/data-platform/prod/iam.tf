/******************************************
 Various service accounts
******************************************/

locals {
  roleListCloudFunctionSA = [
    "roles/cloudfunctions.developer",
    "roles/logging.logWriter",
    "roles/run.serviceAgent",
    "roles/storage.objectViewer"
  ]
  roleListWebApiSA = [
    "roles/storage.admin"
  ]
  roleListMachineLearningModelSA = [
    "roles/storage.objectAdmin"
  ]
  roleListDataFlowSA = [
    "roles/dataflow.admin",
    "roles/dataflow.worker",
    "roles/storage.admin",
    "roles/iam.serviceAccountUser",
    "roles/pubsub.editor",
    "roles/compute.networkUser",
    "roles/compute.serviceAgent"
  ]
  roleListDataFlowComputeEngineServiceAgentSA = [
    "roles/compute.networkUser",
    "roles/compute.serviceAgent"
  ]
  roleListDataFlowServiceAgentSA = [
    "roles/iam.serviceAccountUser",
    "roles/iam.serviceAccountTokenCreator"
  ]
  roleListrtedgemodelSA = [
    "roles/storage.objectCreator"
  ]
  roleListKedaSA = [
    "roles/monitoring.viewer"
  ]
  gke_project_id = data.terraform_remote_state.prod_project_factory.outputs.prod-internal-project.project_id
}

/******************************************
 Service accounts for cloud functions and cloud scheduler
******************************************/

resource "google_service_account" "cloud_function_sa" {
  account_id   = "cloud-funtion-sa"
  display_name = "Cloud Function service account"
}

resource "google_project_iam_member" "cloud_function_group_roles" {
  for_each = toset(local.roleListCloudFunctionSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.cloud_function_sa.email}"
}

# Service account to invoke cloud functions
resource "google_service_account" "cloud_functions_invoke_sa" {
  account_id   = "cloud-functions-invoke-sa"
  display_name = "Invoke cloud functions service account"
}

resource "google_project_iam_member" "cloud_functions_invoke_binding" {
  project = var.project_id
  role    = "roles/cloudfunctions.invoker"
  member  = "serviceAccount:${google_service_account.cloud_functions_invoke_sa.email}"
}

# web api access

resource "google_project_iam_member" "web_api_group_roles" {
  for_each = toset(local.roleListWebApiSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.api_server_sa}"
}

# machine learning project access

resource "google_project_iam_member" "ml_model_group_roles" {
  for_each = toset(local.roleListMachineLearningModelSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.ml_model_sa}"
}


/******************************************
 Role for uploading to customer buckets
******************************************/

resource "google_project_iam_custom_role" "list_buckets_role" {
  role_id     = "list_buckets_role"
  title       = "List Buckets Role"
  description = "A role to list buckets in a project"
  permissions = ["storage.buckets.list"]
}

resource "google_project_iam_binding" "list_buckets_binding" {
  project = var.project_id
  role    = "projects/${var.project_id}/roles/${google_project_iam_custom_role.list_buckets_role.role_id}"

  members = [
    "group:${var.customer_uploader_group}"
  ]
}

/******************************************
 Service account for uploading to customer buckets
******************************************/

resource "google_service_account" "edge_customer_uploader" {
  account_id   = "edge-customer-uploader"
  display_name = "Edge uploader service account"
}

#
# The edge uploader service accounts needs to write into the data bucket
#

resource "google_storage_bucket_iam_member" "edge_customer_uploads_to_video_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-video-data"
  role     = "roles/storage.objectCreator"
  member   = "serviceAccount:${google_service_account.edge_customer_uploader.email}"
  depends_on = [
    module.customer_video_bucket
  ]
}

resource "google_storage_bucket_iam_member" "edge_customer_uploads_to_image_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-image-data"
  role     = "roles/storage.objectCreator"
  member   = "serviceAccount:${google_service_account.edge_customer_uploader.email}"
  depends_on = [
    module.customer_image_bucket
  ]
}


# /******************************************
#  Service account for dataflow
# ******************************************/


# Assigning roles on current project for sa
resource "google_project_iam_member" "dataflow_role_binding" {
  for_each = toset(local.roleListDataFlowSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.dataflow_sa_email}"
}

# assigning roles to the compute sa
resource "google_project_iam_member" "compute_engine_service_agent_binding" {
  for_each = toset(local.roleListDataFlowComputeEngineServiceAgentSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:service-${local.prod_project_number}@compute-system.iam.gserviceaccount.com"
}

# assigning roles to the default service dataflow sa
resource "google_project_iam_member" "dataflow_service_agent_binding" {
  for_each = toset(local.roleListDataFlowServiceAgentSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:service-${local.prod_project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
}

# /******************************************
#  Adding permissions to rtedgemodel-sa
# ******************************************/

# Assigning roles on current project for sa
resource "google_project_iam_member" "rtedgemodel_role_binding" {
  for_each = toset(local.roleListrtedgemodelSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.rtedgemodel_sa}"
}

# /******************************************
#  Dagster service accounts
# ******************************************/
module "dagster_realtime_dags_sa" {
  source = "../modules/dagster-job-sa"

  project_id                  = var.project_id
  gke_project_id              = local.gke_project_id
  service_account_id          = "dagster-realtime-dags"
  service_account_description = "Service account for Realtime Dagster dags"
  self_hosted_ksa_name        = "realtime-dags-ksa"
  bucket_list                 = []
  roles                       = []
}

# /******************************************
#  KEDA service accounts
# ******************************************/
resource "google_project_iam_member" "keda_role_binding" {
  for_each = toset(local.roleListKedaSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:keda-sa@${local.gke_project_id}.iam.gserviceaccount.com"
}

# /******************************************
#  Giving Metabase access to BigQuery
# ******************************************/
resource "google_project_iam_member" "metabase_bigquery_role_binding" {
  for_each = toset([
    "roles/bigquery.dataViewer"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${local.metabase_sa_email}"
}

# /************************************************************************************
#  Giving Media Asset Service download access to data buckets
# ******************************************/******************************************

resource "google_storage_bucket_iam_member" "media_asset_service_read_access_to_customer_data_buckets" {
  for_each = local.all_customer_data_buckets
  bucket   = each.value
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${var.media_asset_service_sa_email}"
}

# /******************************************
#  Create Hex Service Account
# ******************************************/

resource "google_service_account" "hex_sa" {
  account_id   = "hex-sa"
  display_name = "Access for Hex data connectivity"
}

# /************************************************************************************
#  Giving the mlops-dags SA download access to data buckets
# ******************************************/******************************************


resource "google_storage_bucket_iam_member" "ml_ops_dags_read_access_to_customer_data_buckets" {
  for_each = local.all_customer_data_buckets
  bucket   = each.value
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${local.mlops_dags_sa_email}"
}

# /******************************************
#  ML Online Feature Store
# ******************************************/

resource "google_bigtable_table_iam_member" "realtime_dags_sa_cases_feature_store_access" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.cases_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${module.dagster_realtime_dags_sa.sa.email}"
}

resource "google_bigtable_table_iam_member" "ml_services_sa_cases_feature_store_access" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.cases_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${data.terraform_remote_state.ml_project.outputs.ml_services_sa.email}"
}

resource "google_bigtable_table_iam_member" "ml_services_sa_event_model_forecasts_feature_store_access" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.event_model_forecasts_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${data.terraform_remote_state.ml_project.outputs.ml_services_sa.email}"
}

resource "google_bigtable_table_iam_member" "image_processor_image_feature_store_access" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.image_features_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${module.realtime_processing_services.realtime_processing_sa.email}"
}

resource "google_bigtable_table_iam_member" "realtime_processing_sa_cases_feature_store_access_realtime_processor" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.cases_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${module.realtime_processing_services.realtime_processing_sa.email}"
}

resource "google_bigtable_table_iam_member" "realtime_processing_sa_event_model_features_access" {
  instance = google_bigtable_instance.general_ssd.name
  table    = module.online_feature_store.event_model_features_table.name
  role     = "roles/bigtable.user"
  member   = "serviceAccount:${module.realtime_processing_services.realtime_processing_sa.email}"
}

/******************************************
 Adding customer bucket permissions to Hex
******************************************/
resource "google_storage_bucket_iam_member" "hex_sa_reads_image_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-image-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${google_service_account.hex_sa.email}"
}


/******************************************
 Adding customer bucket permissions to ray-sa
******************************************/

resource "google_storage_bucket_iam_member" "ray_sa_video_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-video-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${local.ray_sa_email}"

}

resource "google_storage_bucket_iam_member" "ray_sa_image_data_buckets" {
  for_each = toset(var.customer_list)
  bucket   = "prod-customer-${each.value}-image-data"
  role     = "roles/storage.objectViewer"
  member   = "serviceAccount:${local.ray_sa_email}"
}
