/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-data-platform-prod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.25"
    }
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
  }
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "terraform_remote_state" "auth0_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "auth0-prod"
    }
  }
}

data "terraform_remote_state" "prod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

data "terraform_remote_state" "web_api_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-web-api-prod"
    }
  }
}
data "terraform_remote_state" "ml_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-machine-learning-prod"
    }
  }
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "prod_network" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-prod"
    }
  }
}

data "terraform_remote_state" "prod_internal" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-internal-prod"
    }
  }
}

data "terraform_remote_state" "platform_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-platform-prod"
    }
  }
}

data "terraform_remote_state" "ehr_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "ehr-prod"
    }
  }
}


provider "google" {
  project = var.project_id
  region  = var.region
}
provider "google-beta" {
  project = var.project_id
  region  = var.region
}

data "google_organization" "org" {
  domain = var.domain_name
}

locals {
  org_id                                = data.google_organization.org.org_id
  api_server_sa                         = data.terraform_remote_state.web_api_project.outputs.api_server_sa
  prod_gh_runner_sa                     = data.terraform_remote_state.prod_security.outputs.prod-github-runner-sa
  ml_project                            = data.terraform_remote_state.prod_project_factory.outputs.prod-ml-project
  tmp_bucket                            = "${var.environment}-apella-dataflow-tmp"
  dataflow_sa_email                     = data.terraform_remote_state.prod_security.outputs.prod-dataflow-sa
  prod_project_number                   = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_number
  platform_project                      = data.terraform_remote_state.prod_project_factory.outputs.prod-platform-project
  internal_project                      = data.terraform_remote_state.prod_project_factory.outputs.prod-internal-project
  ehr_project                           = data.terraform_remote_state.prod_project_factory.outputs.prod-ehr-project
  network-project                       = data.terraform_remote_state.prod_project_factory.outputs.prod-network-project
  dataflow-default-sa                   = "service-${data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  ml_model_sa                           = data.terraform_remote_state.ml_project.outputs.automl_model_training_sa
  rtedgemodel_sa                        = data.terraform_remote_state.prod_security.outputs.prod-rtedgemodel-sa
  vpc_access_connector                  = data.terraform_remote_state.prod_network.outputs.vpc_access_connectors[var.region].id
  metabase_sa_email                     = data.terraform_remote_state.prod_internal.outputs.metabase_sa_email
  sql_proxy_datastream_port             = data.terraform_remote_state.platform_project.outputs.cloudsqlproxy_datastream_port
  ehr_sql_proxy_datastream_port_prod    = data.terraform_remote_state.platform_project.outputs.ehr_cloudsqlproxy_datastream_port_prod
  ehr_data_warehouse_username_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_username_secret_id
  ehr_data_warehouse_password_secret_id = data.terraform_remote_state.ehr_project.outputs.ehr_data_warehouse_password_secret_id
  bastion_host_zone                     = data.terraform_remote_state.platform_project.outputs.bastion_host_zone
  data_platform_tf_sa_email             = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.tf_sa_email
  prod_vpc_network_name                 = data.terraform_remote_state.prod_network.outputs.network_name
  prod_project_id                       = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_id
  web_api_project                       = data.terraform_remote_state.prod_project_factory.outputs.prod-web-api-project
  mlops_dags_sa_email                   = data.terraform_remote_state.ml_project.outputs.mlops_dags_sa.email
  cubejs_sa_email                       = data.terraform_remote_state.web_api_project.outputs.cubejs_sa_email
  auth0_management_api_client_id        = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_id
  auth0_management_api_client_secret    = data.terraform_remote_state.auth0_project.outputs.data_warehouse_management_api_client_secret
  ml_services_sa_email                  = data.terraform_remote_state.ml_project.outputs.ml_services_sa.email
  vertex_ai_sa_email                    = "service-${data.terraform_remote_state.prod_project_factory.outputs.prod-ml-project.project_number}@gcp-sa-aiplatform.iam.gserviceaccount.com"
  ray_sa_email                          = data.terraform_remote_state.prod_internal.outputs.ray_sa.email
  image_embeddings_table                = data.terraform_remote_state.ml_project.outputs.image_embeddings_table
  event_model_decodable_features_table  = data.terraform_remote_state.ml_project.outputs.event_model_decodable_features_table
}

/******************************************
  Vanta Installer
 *****************************************/

locals {
  vanta_install_script = <<EOT
VANTA_KEY="${var.vanta_key}" bash -c \
"$(curl -L https://raw.githubusercontent.com/VantaInc/vanta-agent-scripts/master/install-linux.sh)"
EOT
}
