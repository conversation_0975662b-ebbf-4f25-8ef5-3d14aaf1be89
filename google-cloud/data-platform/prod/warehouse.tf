module "warehouse" {
  source = "../modules/warehouse"

  environment                           = var.environment
  region                                = var.region
  vpc_network_project_id                = local.network-project.project_id
  vpc_network_name                      = local.prod_vpc_network_name
  web_api_project_id                    = local.web_api_project.project_id
  platform_project_id                   = local.platform_project.project_id
  bastion_host_zone                     = local.bastion_host_zone
  sql_proxy_datastream_port             = local.sql_proxy_datastream_port
  ehr_sql_proxy_datastream_port         = local.ehr_sql_proxy_datastream_port_prod
  ehr_data_warehouse_username_secret_id = local.ehr_data_warehouse_username_secret_id
  ehr_data_warehouse_password_secret_id = local.ehr_data_warehouse_password_secret_id
  datastream_vpc_subnet_ip_range        = data.terraform_remote_state.prod_network.outputs.datastream_vpc_subnet_ip_range
  data_platform_tf_sa_email             = local.data_platform_tf_sa_email
  data_platform_project_id              = local.prod_project_id
  dagster_sa_email                      = data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email
  dagster_realtime_dags_sa_email        = module.dagster_realtime_dags_sa.sa.email
  hex_sa_email                          = google_service_account.hex_sa.email
  gh_runner_sa_email                    = local.prod_gh_runner_sa
  accessor_sa_emails = [
    google_service_account.hex_sa.email,
    module.dagster_realtime_dags_sa.sa.email,
    data.terraform_remote_state.ml_project.outputs.mlops_dags_sa.email,
    local.cubejs_sa_email,
    local.api_server_sa,
    local.ml_services_sa_email,
    local.prod_gh_runner_sa,
  ]
  slack_auth_token                   = var.slack_auth_token
  auth0_management_api_client_id     = local.auth0_management_api_client_id
  auth0_management_api_client_secret = local.auth0_management_api_client_secret
  ehr_project_id                     = local.ehr_project.project_id
  oncall_team_critical_alert_handle  = "@webhook-incident_io"
  oncall_team_warning_alert_handle   = "@slack-bot-ops-data-platform-${var.environment}"
  datadog_api_key                    = var.datadog_api_key
  datadog_app_key                    = var.datadog_app_key
  datadog_team_id                    = "data-platform"
}
