output "customer_audio_buckets" {
  value = {
    for k, v in module.customer_audio_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer audio buckets"
}

output "customer_video_buckets" {
  value = {
    for k, v in module.customer_video_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer video buckets"
}

output "customer_image_buckets" {
  value = {
    for k, v in module.customer_image_bucket : k => v.bucket.name
  }
  description = "List of provisioned customer image buckets"
}

output "edge_customer_uploader_sa" {
  value = google_service_account.edge_customer_uploader
}

output "realtime_processing_services_sa" {
  value = module.realtime_processing_services.realtime_processing_sa
}

output "image_embedding_outputs_topic_id" {
  value = module.realtime_processing_services.image_embedding_outputs_topic.id
}

output "dagster_realtime_dags_sa" {
  value = module.dagster_realtime_dags_sa.sa
}

output "data_platform_sa_email" {
  value = data.google_storage_project_service_account.gcs_account.email_address
}

output "hex_sa" {
  value = google_service_account.hex_sa
}

output "realtime_event_model_sa" {
  value = module.realtime-event-model.realtime_event_model_sa
}

output "realtime_event_model_shadow_sa" {
  value = module.realtime-event-model-shadow.realtime_event_model_sa
}

output "general_ssd_bigtable" {
  value = google_bigtable_instance.general_ssd
}

output "ml_online_feature_store" {
  value = module.online_feature_store
}
