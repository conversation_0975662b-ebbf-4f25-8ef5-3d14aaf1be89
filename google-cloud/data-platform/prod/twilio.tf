module "twilio" {
  source             = "../modules/twilio"
  environment        = var.environment
  project_id         = var.project_id
  region             = var.region
  twilio_account_sid = var.twilio_account_sid
  twilio_auth_token  = var.twilio_auth_token
  twilio_from_number = var.twilio_from_number
  sendgrid_api_key   = var.sendgrid_api_key
  twilio_secret_accessors = [
    module.dagster_realtime_dags_sa.sa.email,
    module.realtime_processing_services.realtime_processing_sa.email,
    data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email,
    local.api_server_sa
  ]
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret.twilio_account_sid_secret
  to   = module.twilio.google_secret_manager_secret.twilio_account_sid_secret
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret.twilio_auth_token_secret
  to   = module.twilio.google_secret_manager_secret.twilio_auth_token_secret
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret.twilio_from_number_secret
  to   = module.twilio.google_secret_manager_secret.twilio_from_number_secret
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_version.twilio_account_sid_version
  to   = module.twilio.google_secret_manager_secret_version.twilio_account_sid_version
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_version.twilio_auth_token_version
  to   = module.twilio.google_secret_manager_secret_version.twilio_auth_token_version
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_version.twilio_from_number_version
  to   = module.twilio.google_secret_manager_secret_version.twilio_from_number_version
}
