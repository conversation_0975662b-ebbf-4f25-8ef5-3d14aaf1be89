module "dagster-realtime" {
  source = "../modules/dagster-realtime"

  dagster_realtime_dags_sa = module.dagster_realtime_dags_sa.sa
  mlops_dags_sa_email      = local.mlops_dags_sa_email

  project_id  = var.project_id
  region      = var.region
  environment = var.environment
  labels      = var.labels

  clearml_api_key_secret_id    = module.clearml.clearml_api_key_secret_id
  clearml_api_secret_secret_id = module.clearml.clearml_api_secret_secret_id
  accessor_sa_emails = [
    google_service_account.hex_sa.email,
    data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email,
    local.ml_services_sa_email,
    local.prod_gh_runner_sa,
    local.mlops_dags_sa_email
  ]
}
