
# A BigTable instance meant for general use. Unless you have a good reason (performance isolation,
# data security/locale, etc), this instance can be used for general NoSQL workloads.
#
# Tables should be defined close to the applications that need them.
resource "google_bigtable_instance" "general_ssd" {
  name = "${var.environment}-general-ssd"

  cluster {
    cluster_id   = "${var.environment}-general-ssd-c1"
    zone         = "us-central1-c"
    num_nodes    = 1
    storage_type = "SSD"
  }

  lifecycle {
    prevent_destroy = true
  }
}
