module "amplitude" {
  source                         = "../modules/amplitude"
  environment                    = var.environment
  region                         = var.region
  amplitude_dashboard_api_key    = var.amplitude_dashboard_api_key
  amplitude_dashboard_secret_key = var.amplitude_dashboard_secret_key
  amplitude_secret_accessors = [
    data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email,
  ]
}