module "realtime-event-model" {
  source = "../modules/realtime-event-model"

  project_id  = var.project_id
  region      = var.region
  zone        = var.zone
  environment = var.environment

  big_table_instance_name          = google_bigtable_instance.general_ssd.name
  image_embeddings_table_name      = module.online_feature_store.image_features_table.name
  event_model_forecasts_table_name = module.online_feature_store.event_model_forecasts_table.name

  internal_project_id   = local.internal_project.project_id
  vpc_network_id        = data.terraform_remote_state.prod_network.outputs.network.id
  github_runner_sa      = local.prod_gh_runner_sa
  metabase_sa_email     = local.metabase_sa_email
  bastion_host_sa_email = data.terraform_remote_state.prod_security.outputs.prod-bastion-host-sa

  deployment_suffix = ""
  monitor_channel   = "@webhook-incident_io"

  labels                         = var.labels
  aggregated_features_table_name = module.online_feature_store.event_model_features_table.name
  datadog_team_id                = "computer-vision"
}

module "realtime-event-model-shadow" {
  source = "../modules/realtime-event-model"

  project_id  = var.project_id
  region      = var.region
  zone        = var.zone
  environment = var.environment

  big_table_instance_name          = google_bigtable_instance.general_ssd.name
  image_embeddings_table_name      = module.online_feature_store.image_features_table.name
  event_model_forecasts_table_name = module.online_feature_store.event_model_forecasts_table.name

  internal_project_id   = local.internal_project.project_id
  vpc_network_id        = data.terraform_remote_state.prod_network.outputs.network.id
  github_runner_sa      = local.prod_gh_runner_sa
  metabase_sa_email     = local.metabase_sa_email
  bastion_host_sa_email = data.terraform_remote_state.prod_security.outputs.prod-bastion-host-sa

  deployment_suffix = "-shadow"
  monitor_channel   = "@slack-bot-ops-computer-vision-shadow"

  labels = var.labels

  aggregated_features_table_name = module.online_feature_store.event_model_features_table.name
  datadog_team_id                = "computer-vision"
}


moved {
  from = module.realtime_processing_services.module.cloud_sql
  to   = module.realtime-event-model.module.feature_store
}

moved {
  from = module.realtime_processing_services.module.feature_store.data.google_secret_manager_secret_version.default_user_password
  to   = module.realtime-event-model.module.feature_store.data.google_secret_manager_secret_version.default_user_password
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_version.postgres_user_name_secret_version
  to   = module.realtime-event-model.google_secret_manager_secret_version.postgres_user_name_secret_version
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_version.postgres_user_password_secret_version
  to   = module.realtime-event-model.google_secret_manager_secret_version.postgres_user_password_secret_version
}

moved {
  from = module.realtime_processing_services.random_password.feature_store_password
  to   = module.realtime-event-model.random_password.feature_store_password
}

moved {
  from = module.realtime_processing_services.google_service_account_iam_member.realtime_processing_services_sa_used_by_event_predictor
  to   = module.realtime-event-model.google_service_account_iam_member.realtime_processing_services_sa_used_by_event_predictor
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_iam_member.realtime_services_uses_postgres_user_name
  to   = module.realtime-event-model.google_secret_manager_secret_iam_member.realtime_services_uses_postgres_user_name
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_iam_member.realtime_services_uses_postgres_password
  to   = module.realtime-event-model.google_secret_manager_secret_iam_member.realtime_services_uses_postgres_password
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_iam_member.github_runner_uses_postgres_user_name
  to   = module.realtime-event-model.google_secret_manager_secret_iam_member.github_runner_uses_postgres_user_name
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret_iam_member.github_runner_uses_postgres_password
  to   = module.realtime-event-model.google_secret_manager_secret_iam_member.github_runner_uses_postgres_password
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret.postgres_user_password_secret
  to   = module.realtime-event-model.google_secret_manager_secret.postgres_user_password_secret
}

moved {
  from = module.realtime_processing_services.google_secret_manager_secret.postgres_user_name_secret
  to   = module.realtime-event-model.google_secret_manager_secret.postgres_user_name_secret
}

moved {
  from = module.realtime_processing_services.google_pubsub_topic_iam_member.realtime_publishes_prediction_requests
  to   = module.realtime-event-model.google_pubsub_topic_iam_member.realtime_publishes_prediction_requests
}

moved {
  from = module.realtime_processing_services.google_pubsub_topic_iam_member.event_predictor_publishes_snapshots
  to   = module.realtime-event-model.google_pubsub_topic_iam_member.event_predictor_publishes_snapshots
}

moved {
  from = module.realtime_processing_services.google_pubsub_topic.prediction_snapshots
  to   = module.realtime-event-model.google_pubsub_topic.prediction_snapshots
}

moved {
  from = module.realtime_processing_services.google_pubsub_schema.prediction_snapshot_schema
  to   = module.realtime-event-model.google_pubsub_schema.prediction_snapshot_schema
}

moved {
  from = module.realtime_processing_services.google_project_iam_member.realtime_services_uses_postgres
  to   = module.realtime-event-model.google_project_iam_member.realtime_services_uses_postgres
}

moved {
  from = module.realtime_processing_services.google_project_iam_member.metabase_sa_uses_postgres
  to   = module.realtime-event-model.google_project_iam_member.metabase_sa_uses_postgres
}

moved {
  from = module.realtime_processing_services.google_project_iam_member.github_action_runner_uses_postgres
  to   = module.realtime-event-model.google_project_iam_member.github_action_runner_uses_postgres
}

moved {
  from = module.realtime_processing_services.google_project_iam_member.bastion_host_uses_postgres
  to   = module.realtime-event-model.google_project_iam_member.bastion_host_uses_postgres
}

moved {
  from = module.realtime_processing_services.google_pubsub_subscription.prediction_snapshots_subscription
  to   = module.realtime-event-model.google_pubsub_subscription.prediction_snapshots_subscription
}
