/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

region         = "us-central1"
project_id     = "prod-data-platform-027529"
project_number = 478096030626
environment    = "prod"

customer_upload_bucket_changes_topic = "customer-upload-changes"
customer_deletion_topic              = "customer-deletion-topic"

media_asset_deletion_processor_sa_email = "<EMAIL>"

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-data-platform"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "videos-of-employees-and-patients"
  "vanta-contains-ephi"      = "true"
}

/******************************************
 Customer data bucket Resource Related Variables
 *****************************************/

customer_uploader_group = "<EMAIL>"

customer_list = [
  "northbay",
  "houstonmethodist",
  "healthfirst",
  "tampageneral",
  "nyu",
  "lifebridge",
  "hackensackmeridian"
]

# TODO, refactor to make it obvious there's a positional dependency between the elements in this list and those in customer_list
customer_organization_ids = [
  "north_bay",
  "houston_methodist",
  "health_first",
  "tampa_general",
  "nyu",
  "lifebridge",
  "hackensack_meridian"
]

customer_data_bucket_iam_members = [{
  role   = "roles/storage.admin"
  member = "group:<EMAIL>"
}]

cors_origins         = ["https://internal.apella.io", "http://localhost:3000", "https://dashboard.apella.io", "http://localhost:3020"]
cors_methods         = ["GET"]
cors_response_header = ["Content-Type", "x-goog-resumable"]
cors_max_age_seconds = 3600

customer_data_lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 28
    with_state = "ANY"
  }
}]

customer_audio_data_lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 7
    with_state = "ANY"
  }
}]

data_health_notification_channel_id = 15942868840196538472
image_upload_lower_threshold        = 19
image_upload_upper_threshold        = 23

media_asset_service_project  = "prod-media-asset-5fd208"
media_asset_service_sa_email = "<EMAIL>"

forecast_dataset = {
  dataset_id  = "case_forecasting"
  description = "This dataset encapsulates all tables related to case forecasting metrics."
}
