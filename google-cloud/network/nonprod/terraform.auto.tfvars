/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "nonprod-network-9523f0"
environment = "nonprod"

/******************************************
	VPC Network Resource Related Variables
 *****************************************/
network_name            = "apella-vpc"
shared_vpc_host         = true
description             = "This is used for nonprod vpc shared host project network"
auto_create_subnetworks = false

/******************************************
	Log Settings common across all Subnetworks Related Variables
 *****************************************/
log_config = {
  aggregation_interval = "INTERVAL_5_MIN"
  flow_sampling        = 0.5
  metadata             = "INCLUDE_ALL_METADATA"
}

/******************************************
	Sub Network Resource Related Variables
 *****************************************/

/******************************************
	Dev Sub Network Resource Related Variables
 *****************************************/
dev_general_compute = {
  name                     = "nonprod-central1-dev-compute"
  description              = "Nonprod Dev General Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

dev_internal_gke = {
  name                     = "nonprod-central1-dev-internal-gke"
  description              = "Nonprod Dev Internal GKE (Kubernetes) Container Network"
  ip_cidr_range            = "***********/22"
  private_ip_google_access = "true"
  secondary_ip_range_name1 = "nonprod-dev-internal-gke-pod-range-1"
  secondary_ip_cidr1       = "**********/14"
  secondary_ip_range_name2 = "nonprod-dev-internal-gke-svc-range-1"
  secondary_ip_cidr2       = "**********/14"
}

nonprod_dev_internal_gke_master_range = "***********/28"

dev_dataproc_network_range = "**********/24"

/******************************************
	Non Prod Sub Network Resource Related Variables
 *****************************************/
vpc_connector = {
  name                     = "nonprod-central1-vpc-connector"
  description              = "Nonprod VPC Connector subnet"
  ip_cidr_range            = "************/28"
  private_ip_google_access = "true"
}

general_compute = {
  name                     = "nonprod-central1-compute"
  description              = "Nonprod General Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

gke = {
  name                     = "nonprod-central1-gke"
  description              = "Nonprod GKE (Kubernetes) Container Network"
  ip_cidr_range            = "***********/22"
  private_ip_google_access = "true"
  secondary_ip_range_name1 = "nonprod-gke-pod-range-1"
  secondary_ip_cidr1       = "240.0.0.0/14"
  secondary_ip_range_name2 = "nonprod-gke-svc-range-1"
  secondary_ip_cidr2       = "*********/14"
}

/******************************************
	Shared Sub Network Resource Related Variables
 *****************************************/

shared_compute = {
  name                     = "nonprod-central1-shared-compute"
  description              = "Nonprod Shared Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

/******************************************
	Staging Sub Network Resource Related Variables
 *****************************************/
staging_general_compute = {
  name                     = "nonprod-central1-staging-compute"
  description              = "Nonprod Staging General Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

staging_internal_gke = {
  name                     = "nonprod-central1-staging-internal-gke"
  description              = "Nonprod Staging Internal GKE (Kubernetes) Container Network"
  ip_cidr_range            = "***********/22"
  private_ip_google_access = "true"
  secondary_ip_range_name1 = "nonprod-staging-internal-gke-pod-range-1"
  secondary_ip_cidr1       = "**********/14"
  secondary_ip_range_name2 = "nonprod-staging-internal-gke-svc-range-1"
  secondary_ip_cidr2       = "**********/14"
}

nonprod_staging_internal_gke_master_range = "************/28"

/******************************************
	Cloud NAT Resource Related Variables
 *****************************************/
nat_gateway_name = "nonprod-nat"
nat_ip_count     = 2

/******************************************
	Cloud Router Resource Related Variables
 *****************************************/
router_name = "nonprod-router"

/******************************************
	Cloud Firewall Resource Related Variables
 *****************************************/
# IAP IP Address source-ranges = ************/20
iap_source_ranges = ["************/20"]

/******************************************
	private service connection Related Variables
 *****************************************/
service_connection_address       = "************"
service_connection_prefix_length = "23"

/******************************************
	serverless vpc connector=
  This allows cloud-run instances to connect to the VPC.
  The project number of each project that has a cloud-run
  must be added to the list.
 *****************************************/
serverless_vpc_service_project_nbrs = [
  "52250969628", # dev-web-api-72f12b
  "612883740328" # staging-web-api-3efef9
]

datastream_vpc_subnet_ip_range_dev     = "************/29"
datastream_vpc_subnet_ip_range_staging = "************/29"

decodable_vpc_subnet_ip_range_dev     = "**********/24"
decodable_vpc_subnet_ip_range_staging = "**********/24"
