// Palo Alto VPN Secret
resource "google_secret_manager_secret" "pa_secret" {
  secret_id = "pa-vpn-secret"
  project   = var.project_id

  labels = {
    label = "pa"
  }

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

// NYU VPN Secret
resource "google_secret_manager_secret" "nyu_secret" {
  secret_id = "nyu-vpn-secret"
  project   = var.project_id

  labels = {
    label = "nyu"
  }

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

// VPN
resource "google_compute_ha_vpn_gateway" "ha_gateway" {
  region  = var.region
  name    = "apella-cloud-vpn"
  project = var.project_id
  network = module.vpc.network.id
}

// Palo Alto VPN for testing
resource "google_compute_external_vpn_gateway" "pa_external_gateway" {
  name            = "pa-external-gateway"
  project         = var.project_id
  redundancy_type = "SINGLE_IP_INTERNALLY_REDUNDANT"
  description     = "An externally managed VPN gateway"
  interface {
    id         = 0
    ip_address = "*************"
  }
}

data "google_secret_manager_secret_version" "pa_secret" {
  secret = "pa-vpn-secret"
}

// Two Palo Alto tunnels to same IP
resource "google_compute_vpn_tunnel" "pa_tunnel1" {
  name                            = "pa-ha-vpn-tunnel1"
  region                          = var.region
  vpn_gateway                     = google_compute_ha_vpn_gateway.ha_gateway.id
  peer_external_gateway           = google_compute_external_vpn_gateway.pa_external_gateway.id
  peer_external_gateway_interface = google_compute_external_vpn_gateway.pa_external_gateway.interface[0].id
  shared_secret                   = data.google_secret_manager_secret_version.pa_secret.secret_data
  router                          = module.cloud_nat.router_name
  vpn_gateway_interface           = 0
}

resource "google_compute_vpn_tunnel" "pa_tunnel2" {
  name                            = "pa-ha-vpn-tunnel2"
  region                          = var.region
  vpn_gateway                     = google_compute_ha_vpn_gateway.ha_gateway.id
  peer_external_gateway           = google_compute_external_vpn_gateway.pa_external_gateway.id
  peer_external_gateway_interface = google_compute_external_vpn_gateway.pa_external_gateway.interface[0].id
  shared_secret                   = data.google_secret_manager_secret_version.pa_secret.secret_data
  router                          = module.cloud_nat.router_name
  vpn_gateway_interface           = 1
}
