/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-network-nonprod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

data "terraform_remote_state" "dev_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-dev"
    }
  }
}

data "terraform_remote_state" "nonprod_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-platform-nonprod"
    }
  }
}

data "terraform_remote_state" "staging_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-staging"
    }
  }
}
