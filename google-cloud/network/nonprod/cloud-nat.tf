locals {
  open_connection_limit           = var.nat_ip_count * 1088
  open_connection_alarm_threshold = local.open_connection_limit * 0.9
}

/* # Cloud NAT
module "cloud_nat" {
  source  = "app.terraform.io/apella/cloud-nat/google"
  version = "1.0.0"

  router     = module.cloud_router.router.name
  project_id = var.project_id
  region     = var.region
  name       = var.nat_gateway_name
}
 */


resource "google_compute_address" "nat" {
  count  = var.nat_ip_count
  name   = "${var.environment}-nat-address-${count.index}"
  region = var.region
}

module "cloud_nat" {
  source  = "terraform-google-modules/cloud-nat/google"
  version = "5.2.0"
  # general variables
  project_id = var.project_id
  region     = var.region
  # router variables
  create_router = true
  router        = var.router_name
  network       = module.vpc.network_name
  # nat variables
  name                             = var.nat_gateway_name
  nat_ips                          = google_compute_address.nat.*.self_link
  min_ports_per_vm                 = "128"
  icmp_idle_timeout_sec            = "15"
  tcp_established_idle_timeout_sec = "600"
  tcp_transitory_idle_timeout_sec  = "15"
  udp_idle_timeout_sec             = "15"

  subnetworks = [
    {
      name                     = google_compute_subnetwork.nonprod_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    },
    {
      name                     = google_compute_subnetwork.shared_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    },
    {
      name                     = google_compute_subnetwork.vpc_connectory_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    },
    {
      name                     = google_compute_subnetwork.gke_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = [var.gke["secondary_ip_cidr1"], var.gke["secondary_ip_cidr2"]]
    },
    {
      name                     = google_compute_subnetwork.dev_internal_gke_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = [var.dev_internal_gke["secondary_ip_cidr1"], var.dev_internal_gke["secondary_ip_cidr2"]]
    },
    {
      name                     = module.dataproc.dataproc_subnetwork.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = []
    },
    {
      name                     = google_compute_subnetwork.staging_internal_gke_us_central1.self_link
      source_ip_ranges_to_nat  = ["ALL_IP_RANGES"]
      secondary_ip_range_names = [var.staging_internal_gke["secondary_ip_cidr1"], var.staging_internal_gke["secondary_ip_cidr2"]]
    }
  ]
  source_subnetwork_ip_ranges_to_nat = "LIST_OF_SUBNETWORKS"
  log_config_enable                  = true

}

resource "google_monitoring_alert_policy" "cloud_open_connection_alert_policy" {
  display_name = "Open connections approaching NAT limit"
  combiner     = "OR"

  conditions {
    display_name = "Critical - Cloud NAT Gateway - Open connections approaching limit."
    condition_threshold {
      filter = "resource.type = \"nat_gateway\" AND resource.labels.project_id = ${var.project_id} AND metric.type = \"router.googleapis.com/nat/open_connections\""
      aggregations {
        alignment_period     = "60s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_MEAN"
      }
      duration   = "0s"
      comparison = "COMPARISON_GT"
      trigger {
        count = 5
      }
      threshold_value = local.open_connection_alarm_threshold
    }
  }
  enabled = true
  notification_channels = [
    google_monitoring_notification_channel.team_platform_services_alerts_slack.id
  ]
}