# note: Must enable Compute engine API to get this working

module "vpc" {
  source  = "app.terraform.io/apella/vpc/google"
  version = "1.0.0"

  network_name            = var.network_name
  auto_create_subnetworks = var.auto_create_subnetworks
  routing_mode            = var.routing_mode
  description             = var.description
  shared_vpc_host         = var.shared_vpc_host
  # core input variables
  project_id  = var.project_id
  environment = var.environment

}



