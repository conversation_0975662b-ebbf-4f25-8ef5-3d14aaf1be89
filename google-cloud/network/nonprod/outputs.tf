/******************************************
	Core Terraform and Project Related Outputs
 *****************************************/

output "project_id" {
  value       = var.project_id
  description = "Project id"
}

/******************************************
	VPC Network Resource Related Outputs
 *****************************************/

# First output option outputs the entire resource instead of specific properties of the resource.  This was supported starting Terraform 12.
output "network" {
  value       = module.vpc.network
  description = "The VPC resource being created"
}

output "network_name" {
  value       = module.vpc.network.name
  description = "The name of the VPC being created"
}

output "network_self_link" {
  value       = module.vpc.network_self_link
  description = "The URI of the VPC being created"
}

/******************************************
	Cloud NAT Resource Related Variables
 *****************************************/
output "cloud_nat_name" {
  description = "The name of the created Cloud NAT instance"
  value       = module.cloud_nat.name
}


/******************************************
	Cloud Router Resource Related Variables
 *****************************************/
/* output "router_name" {
  value       = module.cloud_router.router.name
  description = "The name of the created router"
}

output "router_region" {
  value       = module.cloud_router.router.region
  description = "The region of the created router"
} */


/******************************************
	Shared compute subnetwork
 *****************************************/
output "shared_us_central1" {
  value       = google_compute_subnetwork.shared_us_central1
  description = "shared compute network"
}

/******************************************
	Internal GKE vars
 *****************************************/
output "dev_internal_gke" {
  value       = var.dev_internal_gke
  description = "nonprod internal dev gke network config"
}

output "nonprod_dev_internal_gke_master_range" {
  value       = var.nonprod_dev_internal_gke_master_range
  description = "nonprod dev internal gke master node range"
}

output "dev_internal_gke_us_central1" {
  value       = google_compute_subnetwork.dev_internal_gke_us_central1
  description = "nonprod dev internal gke subnet"
}

output "staging_internal_gke" {
  value       = var.staging_internal_gke
  description = "nonprod internal staging gke network config"
}

output "nonprod_staging_internal_gke_master_range" {
  value       = var.nonprod_staging_internal_gke_master_range
  description = "non prod staging internal gke master node range"
}

output "staging_internal_gke_us_central1" {
  value       = google_compute_subnetwork.staging_internal_gke_us_central1
  description = "nonprod staging internal gke subnet"
}

output "vpc_access_connectors" {
  description = "vpc access connectors, key'd by region"
  value = {
    us-central1 = google_vpc_access_connector.default-nonprod-us-central1
  }
}

# todo: remove this after everything is using the new env-specific output
output "datastream_vpc_subnet_ip_range" {
  description = "The IP range allocated by Datastream"
  value       = var.datastream_vpc_subnet_ip_range_dev
}

output "datastream_vpc_subnet_ip_range_dev" {
  description = "The IP range allocated by dev Datastream"
  value       = var.datastream_vpc_subnet_ip_range_dev
}

output "datastream_vpc_subnet_ip_range_staging" {
  description = "The IP range allocated by staging Datastream"
  value       = var.datastream_vpc_subnet_ip_range_staging
}

