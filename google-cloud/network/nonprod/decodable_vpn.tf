// Decodable VPN Secret
resource "google_secret_manager_secret" "decodable_vpn_secret" {
  secret_id = "decodable-vpn-secret"
  project   = var.project_id

  labels = {
    label = "pa"
  }

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}


// VPN
resource "google_compute_ha_vpn_gateway" "decodable_ha_gateway" {
  region  = var.region
  name    = "decodable-ha-vpn"
  project = var.project_id
  network = module.vpc.network.id
}


resource "google_compute_external_vpn_gateway" "decodable_ext_gwy" {
  name            = "decodable-ext-vpn-gwy"
  redundancy_type = "TWO_IPS_REDUNDANCY"
  interface {
    id         = 0
    ip_address = "************"
  }
  interface {
    id         = 1
    ip_address = "*************"
  }
}

data "google_secret_manager_secret_version" "decodable_secret" {
  secret = "decodable-vpn-secret"
}


resource "google_compute_vpn_tunnel" "tunnel_one" {
  name                            = "decodable-tunnel-0"
  shared_secret                   = data.google_secret_manager_secret_version.decodable_secret.secret_data
  peer_external_gateway           = google_compute_external_vpn_gateway.decodable_ext_gwy.name
  peer_external_gateway_interface = 0
  region                          = var.region
  router                          = module.cloud_nat.router_name
  ike_version                     = "2"
  vpn_gateway                     = google_compute_ha_vpn_gateway.decodable_ha_gateway.id
  vpn_gateway_interface           = 0
}

resource "google_compute_vpn_tunnel" "tunnel_two" {
  name                            = "decodable-tunnel-1"
  shared_secret                   = data.google_secret_manager_secret_version.decodable_secret.secret_data
  peer_external_gateway           = google_compute_external_vpn_gateway.decodable_ext_gwy.name
  peer_external_gateway_interface = 1
  region                          = var.region
  router                          = module.cloud_nat.router_name
  ike_version                     = "2"
  vpn_gateway                     = google_compute_ha_vpn_gateway.decodable_ha_gateway.id
  vpn_gateway_interface           = 0
}


// BGP
resource "google_compute_router_interface" "decodable_interface_one" {
  name       = "decodable-interface-0"
  router     = module.cloud_nat.router_name
  region     = var.region
  ip_range   = "**************/30"
  vpn_tunnel = google_compute_vpn_tunnel.tunnel_one.name
}

resource "google_compute_router_interface" "decodable_interface_two" {
  name       = "decodable-interface-1"
  router     = module.cloud_nat.router_name
  region     = var.region
  ip_range   = "*************/30"
  vpn_tunnel = google_compute_vpn_tunnel.tunnel_two.name
}

resource "google_compute_router_peer" "decodable_peer_one" {
  name            = "decodable-peer-one"
  interface       = "decodable-interface-one"
  peer_asn        = 64512
  ip_address      = "**************"
  peer_ip_address = "*************"
  router          = module.cloud_nat.router_name
  region          = var.region
}

resource "google_compute_router_peer" "decodable_peer_two" {
  name            = "decodable-peer-two"
  interface       = "decodable-interface-two"
  peer_asn        = 64512
  ip_address      = "*************"
  peer_ip_address = "*************"
  router          = module.cloud_nat.router_name
  region          = var.region
}
