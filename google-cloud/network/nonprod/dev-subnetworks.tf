# General Compute Network
resource "google_compute_subnetwork" "dev_general_compute_us_central1" {
  name                     = var.dev_general_compute["name"]
  description              = var.dev_general_compute["description"]
  project                  = var.project_id
  ip_cidr_range            = var.dev_general_compute["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.dev_general_compute["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

# GKE Nonprod Network
resource "google_compute_subnetwork" "dev_internal_gke_us_central1" {
  name                     = var.dev_internal_gke["name"]
  description              = var.dev_internal_gke["description"]
  project                  = var.project_id
  ip_cidr_range            = var.dev_internal_gke["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.dev_internal_gke["private_ip_google_access"]


  secondary_ip_range {
    range_name    = var.dev_internal_gke["secondary_ip_range_name1"]
    ip_cidr_range = var.dev_internal_gke["secondary_ip_cidr1"]
  }

  secondary_ip_range {
    range_name    = var.dev_internal_gke["secondary_ip_range_name2"]
    ip_cidr_range = var.dev_internal_gke["secondary_ip_cidr2"]
  }

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

module "dataproc" {
  source                   = "../modules/dataproc"
  network_environment      = var.environment
  environment              = "dev"
  project_id               = var.project_id
  ip_cidr_range            = var.dev_dataproc_network_range
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = "true"
  log_config               = var.log_config
  dataproc_sa_email        = data.terraform_remote_state.dev_data_platform.outputs.dataproc_sa.email
}
