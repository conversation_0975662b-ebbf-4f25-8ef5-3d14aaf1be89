#ICMP Allow for troubleshooting purposes
/*
resource "google_compute_firewall" "allow_icmp" {
  name    = "allow-icmp"
  project = var.project_id
  network = module.vpc.network_self_link

  allow {
    protocol = "icmp"
  }

  priority = "1000"

  log_config {
    metadata = var.log_config["metadata"]
  }
}
*/

#we will deny all traffic by default
resource "google_compute_firewall" "catch_all_deny_ingress" {
  name        = "catch-all-deny-ingress"
  project     = var.project_id
  network     = module.vpc.network_self_link
  description = "Catch-all deny all ingress"

  priority = 65534

  deny {
    protocol = "all"
  }

  direction     = "INGRESS"
  source_ranges = ["0.0.0.0/0"]

  log_config {
    metadata = var.log_config["metadata"]
  }
}

# IAP Allow from IAP Source Range. This is need to access private instances
resource "google_compute_firewall" "allow_iap" {
  name    = "allow-iap"
  project = var.project_id
  network = module.vpc.network_self_link

  source_ranges = var.iap_source_ranges
  allow {
    protocol = "tcp"
    ports    = ["22", "3389"]
  }

  # uncomment if we only want IAP to be allowd to instances with tag iap only
  # target_tags    = ["iap"]
  priority = "2000"

  log_config {
    metadata = var.log_config["metadata"]
  }
}


##################################################################################
#rules required by vpc connectory
##################################################################################
resource "google_compute_firewall" "nonprod_serverless_to_vpc_connector" {
  name        = "allow-nonprod-serverless-to-vpc-connector"
  description = "Allow ingress from nonprod serverless to vpc connector"
  project     = var.project_id
  network     = module.vpc.network_self_link
  priority    = 1000

  allow {
    protocol = "tcp"
    ports    = ["667"]
  }

  allow {
    protocol = "udp"
    ports    = ["665", "666"]
  }

  allow {
    protocol = "icmp"
    ports    = []
  }

  direction = "INGRESS"

  source_ranges = ["**************/26", "************/19"]
  target_tags   = ["vpc-connector"]

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "nonprod_vpc_connector_healthchecks" {
  name        = "allow-nonprod-vpc-connector-healthchecks"
  description = "Allow ingress from nonprod vpc connector healthchecks"
  project     = var.project_id
  network     = module.vpc.network_self_link
  priority    = 1000

  allow {
    protocol = "tcp"
    ports    = ["667"]
  }

  direction = "INGRESS"

  source_ranges = ["***********/22", "**********/16", "*************/23"]
  target_tags   = ["vpc-connector"]

  log_config {
    metadata = var.log_config["metadata"]
  }
}


##################################################################################
#rules required by vpc connectory
##################################################################################
resource "google_compute_firewall" "nonprod_vpc-connector-to-vpc" {
  name        = "allow-nonprod-vpc-connector-to-vpc"
  description = "Allow ingress from vpc connector to vpc resources"
  project     = var.project_id
  network     = module.vpc.network_self_link
  priority    = 1000

  allow {
    protocol = "tcp"
  }

  allow {
    protocol = "udp"
  }

  allow {
    protocol = "icmp"
  }

  direction   = "INGRESS"
  source_tags = ["vpc-connector"]

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "nonprod_vpc-connector-to-serverless" {
  name        = "allow-nonprod-vpc-connectrom-to-serverless"
  description = "Allow nonprod vpc connector to connect with serverless."
  project     = var.project_id
  network     = module.vpc.network_self_link
  priority    = 1000

  allow {
    protocol = "tcp"
    ports    = ["667"]
  }

  allow {
    protocol = "udp"
    ports    = ["665", "666"]
  }

  allow {
    protocol = "icmp"
    ports    = []
  }

  direction = "EGRESS"

  destination_ranges = ["***********/22", "**********/16", "*************/23"]
  target_tags        = ["vpc-connector"]


  log_config {
    metadata = var.log_config["metadata"]
  }
}



##################################################################################
#egress rules
##################################################################################

#allow vpc traffic to reach redis and databases
resource "google_compute_firewall" "allow_traffic_to_redis_sql" {
  name     = "allow-to-redis-sql"
  project  = var.project_id
  network  = module.vpc.network_self_link
  priority = 1000

  allow {
    protocol = "tcp"
    ports    = ["6379", "5432", "3306", "3307"]
  }

  direction          = "EGRESS"
  destination_ranges = ["${var.service_connection_address}/${var.service_connection_prefix_length}"]

  log_config {
    metadata = var.log_config["metadata"]
  }

}

##################################################################################
# Dataflow rules
# See https://cloud.google.com/dataflow/docs/guides/routes-firewall
##################################################################################

resource "google_compute_firewall" "dataflow-allow-ingress" {
  name      = "dataflow-allow-ingress"
  project   = var.project_id
  network   = module.vpc.network_self_link
  direction = "INGRESS"
  priority  = "0"

  allow {
    protocol = "tcp"
    ports    = ["12345-12346"]
  }

  source_tags = ["dataflow"]
  target_tags = ["dataflow"]

  log_config {
    metadata = var.log_config["metadata"]
  }

}


##################################################################################
# GKE Master Range Rules
##################################################################################

resource "google_compute_firewall" "allow_istio_service_mesh_sidecar_injection" {
  name          = "allow-nonprod-istio-service-mesh-sidecar-injection"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "2000"
  direction     = "INGRESS"
  source_ranges = [var.nonprod_dev_internal_gke_master_range, var.nonprod_staging_internal_gke_master_range]

  allow {
    protocol = "tcp"
    ports    = ["443", "10250", "15017"]
  }

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "allow_gke_keda_service_api_firewall" {
  name          = "allow-nonprod-gke-keda-service-api"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "2000"
  direction     = "INGRESS"
  source_ranges = [var.nonprod_dev_internal_gke_master_range, var.nonprod_staging_internal_gke_master_range]

  allow {
    protocol = "tcp"
    ports    = ["6443"]
  }

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "allow_gke_master_nodes_access_to_opa_gatekeeper" {
  name          = "allow-nonprod-gke-master-nodes-opa-gatekeeper"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "2000"
  direction     = "INGRESS"
  source_ranges = [var.nonprod_dev_internal_gke_master_range, var.nonprod_staging_internal_gke_master_range]

  allow {
    protocol = "tcp"
    ports    = ["8443"]
  }

  log_config {
    metadata = var.log_config["metadata"]
  }
}

##################################################################################
# GKE Node Rules
##################################################################################

resource "google_compute_firewall" "allow_github_runners_to_gke_subnet" {
  name        = "allow-github-runners-to-gke-subnet"
  project     = var.project_id
  network     = module.vpc.network_self_link
  priority    = "1000"
  direction   = "INGRESS"
  source_tags = ["gh-runner-vm"]
  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "allow_load_balancer_health_checks_to_gke_nodes" {
  name          = "allow-load-balancer-health-checks-to-gke-nodes"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "1000"
  direction     = "INGRESS"
  source_ranges = var.network_load_balancer_source_ranges
  allow {
    protocol = "tcp"
    ports    = ["10256"]
  }
  target_tags = var.gke_node_pool_network_tags

  log_config {
    metadata = var.log_config["metadata"]
  }
}

resource "google_compute_firewall" "allow_public_ingress_to_gke_nodes" {
  name          = "allow-public-ingress-to-gke-nodes"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "1000"
  direction     = "INGRESS"
  source_ranges = ["0.0.0.0/0"]
  allow {
    protocol = "tcp"
    ports    = ["15021", "443", "80", "22"]
  }
  target_tags = var.gke_node_pool_network_tags

  log_config {
    metadata = var.log_config["metadata"]
  }
}

##################################################################################
# Tailscale Rules
##################################################################################

resource "google_compute_firewall" "allow_tailscale_exit_node_ingress" {
  name          = "allow-tailscale-exit-node-ingress"
  project       = var.project_id
  network       = module.vpc.network_self_link
  priority      = "1000"
  direction     = "INGRESS"
  source_ranges = ["0.0.0.0/0"]

  allow {
    protocol = "udp"
    ports    = ["41641"]
  }
  target_tags = ["tailscale-exit-node"]

  log_config {
    metadata = var.log_config["metadata"]
  }
}
