/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}


variable "project_id" {
  description = "GCP Project ID"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

/******************************************
	VPC Network Resource Related Variables
 *****************************************/

variable "network_name" {
  description = "The name of the network being created"
}

variable "routing_mode" {
  type        = string
  default     = "GLOBAL"
  description = "The network routing mode (default 'GLOBAL')"
}

variable "shared_vpc_host" {
  type        = bool
  description = "Makes this project a Shared VPC host if 'true' "
}

variable "description" {
  type        = string
  description = "An optional description of this resource. The resource must be recreated to modify this field."
  default     = ""
}

variable "auto_create_subnetworks" {
  type        = bool
  description = "When set to true, the network is created in 'auto subnet mode' and it will create a subnet for each region automatically across the **********/9 address range. When set to false, the network is created in 'custom subnet mode' so the user can explicitly connect subnetwork resources."
  default     = false
}

/******************************************
	Log Settings common across all Subnetworks Related Variables
 *****************************************/

variable "log_config" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    aggregation_interval = ""
    flow_sampling        = ""
    metadata             = ""
  }
}

/******************************************
	SubNetwork Resource Related Variables
 *****************************************/

/******************************************
	Dev Sub Network Resource Related Variables
 *****************************************/
variable "dev_general_compute" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "dev_internal_gke" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
    secondary_ip_range_name1 = ""
    secondary_ip_cidr1       = ""
    secondary_ip_range_name2 = ""
    secondary_ip_cidr2       = ""
  }
}

variable "nonprod_dev_internal_gke_master_range" {
  type        = string
  description = "non prod dev internal gke master node range"
}

variable "dev_dataproc_network_range" {
  type        = string
  description = "dev dataproc network range"
}

/******************************************
	Non Prod Sub Network Resource Related Variables
 *****************************************/

variable "vpc_connector" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "general_compute" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "ai_notebook" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "gke" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
    secondary_ip_range_name1 = ""
    secondary_ip_cidr1       = ""
    secondary_ip_range_name2 = ""
    secondary_ip_cidr2       = ""
  }
}

/******************************************
	Shared Sub Network Resource Related Variables
 *****************************************/
variable "shared_compute" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

/******************************************
	Staging Sub Network Resource Related Variables
 *****************************************/
variable "staging_general_compute" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "staging_ai_notebook" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
  }
}

variable "staging_internal_gke" {
  type = map(any)
  # these defaults will be overrident via the terraform.tfvars file
  default = {
    name                     = ""
    description              = ""
    ip_cidr_range            = ""
    private_ip_google_access = ""
    secondary_ip_range_name1 = ""
    secondary_ip_cidr1       = ""
    secondary_ip_range_name2 = ""
    secondary_ip_cidr2       = ""
  }
}

variable "nonprod_staging_internal_gke_master_range" {
  type        = string
  description = "non prod staging gke master node range"
}

/******************************************
	Cloud NAT Resource Related Variables
 *****************************************/

variable "nat_gateway_name" {
  description = "The name of the GCP NAT Gateway"
}

variable "nat_ip_count" {
  type        = number
  description = "The number nat ips to provision"
  default     = 2
}

/******************************************
	Cloud Router Resource Related Variables
 *****************************************/
variable "router_name" {
  description = "The name of the GCP Router to associate the NAT to"
}

/******************************************
	Cloud Firewall Resource Related Variables
 *****************************************/
variable "iap_source_ranges" {
  type        = list(any)
  description = "The list of source Ranges for IAP for FW rule"

}

/******************************************
	private service connection Related Variables
 *****************************************/

variable "service_connection_address" {
  type        = string
  description = "Address for privte service connection"
}

variable "service_connection_prefix_length" {
  type        = string
  description = "prefix for privte service connection"
}

/******************************************
	serverless vpc connector
 *****************************************/
variable "serverless_vpc_service_project_nbrs" {
  type        = list(any)
  description = "The list of service project numbers that need access to serverless connector"
}

/******************************************
	gke ingress
 *****************************************/

variable "network_load_balancer_source_ranges" {
  type        = list(string)
  description = "The list of network load balancer source ranges documentation here https://cloud.google.com/load-balancing/docs/health-checks#fw-netlb"
  default     = ["***********/22", "************/22", "************/22", "**********/16"]
}

variable "gke_node_pool_network_tags" {
  type        = list(string)
  description = "The list of network tags for gke node pools that should have public ingress traffic allowed"
  default     = ["dev-internal-gke-node", "staging-internal-gke-node"]
}

variable "datastream_vpc_subnet_ip_range_dev" {
  type        = string
  description = "The IP range allocated by dev Datastream"
}

variable "datastream_vpc_subnet_ip_range_staging" {
  type        = string
  description = "The IP range allocated by staging Datastream"
}

variable "decodable_vpc_subnet_ip_range_dev" {
  type        = string
  description = "The IP range allocated by dev Decodable"
}

variable "decodable_vpc_subnet_ip_range_staging" {
  type        = string
  description = "The IP range allocated by staging Decodable"
}

variable "slack-api-token" {
  type        = string
  description = "The authentication token for sending alerts to Slack"
  sensitive   = true
}
