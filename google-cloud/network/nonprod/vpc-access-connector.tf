resource "google_vpc_access_connector" "default-nonprod-us-central1" {
  provider = google-beta
  name     = "svpc-nonprod-us-central1"
  region   = "us-central1"
  project  = var.project_id
  #ip_cidr_range = var.vpc_connector["ip_cidr_range"]
  #network       = module.vpc.network_name
  subnet {
    name = var.vpc_connector["name"]
  }

  depends_on = [
    google_compute_subnetwork.vpc_connectory_us_central1,
  ]
}


#grant service projects access to vpc serverless connector
#https://cloud.google.com/vpc/docs/configure-serverless-vpc-access?&_ga=2.5126195.-**********.**********&_gac=1.********.**********.Cj0KCQjw7pKFBhDUARIsAFUoMDYMdMHYwtB9gEMXhUQwFypApeVugFe7gx53AllODA1Y-tpJuKReAzMaAgh0EALw_wcB#granting_permissions_to_service_accounts_in_your_service_projects

resource "google_project_iam_member" "nonprod_network_serverless_vpcaccess" {
  for_each = toset(var.serverless_vpc_service_project_nbrs)
  project  = var.project_id
  role     = "roles/compute.networkUser"
  member   = "serviceAccount:service-${each.value}@gcp-sa-vpcaccess.iam.gserviceaccount.com"
}

#TODO create custom role to only allow vpcaccess.connector.use 
resource "google_project_iam_member" "nonprod_network_serverless_vpcaccessuser" {
  for_each = toset(var.serverless_vpc_service_project_nbrs)
  project  = var.project_id
  role     = "roles/vpcaccess.admin"
  member   = "serviceAccount:service-${each.value}@gcp-sa-vpcaccess.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "nonprod_network_serverless_cloudrun_vpcaccessuser" {
  for_each = toset(var.serverless_vpc_service_project_nbrs)
  project  = var.project_id
  role     = "roles/vpcaccess.admin"
  member   = "serviceAccount:service-${each.value}@serverless-robot-prod.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "nonprod_network_serverless_cloudservices" {
  for_each = toset(var.serverless_vpc_service_project_nbrs)
  project  = var.project_id
  role     = "roles/compute.networkUser"
  member   = "serviceAccount:${each.value}@cloudservices.gserviceaccount.com"
}