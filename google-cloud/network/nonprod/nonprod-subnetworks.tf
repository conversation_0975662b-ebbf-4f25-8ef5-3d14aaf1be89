##############################
#  subnets for central1 nonprod - 
#  THESE WILL BE REMOVED AS WE MIGRATE TO ENVIRONMENT SPECIFIC
##############################
# update to tfvars and variables

# nonprod central vpc connector Network
resource "google_compute_subnetwork" "vpc_connectory_us_central1" {
  name                     = var.vpc_connector["name"]
  description              = var.vpc_connector["description"]
  project                  = var.project_id
  ip_cidr_range            = var.vpc_connector["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.vpc_connector["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

# General Compute Network
resource "google_compute_subnetwork" "nonprod_us_central1" {
  name                     = var.general_compute["name"]
  description              = var.general_compute["description"]
  project                  = var.project_id
  ip_cidr_range            = var.general_compute["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.general_compute["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

# GKE Nonprod Network
resource "google_compute_subnetwork" "gke_us_central1" {
  name                     = var.gke["name"]
  description              = var.gke["description"]
  project                  = var.project_id
  ip_cidr_range            = var.gke["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.gke["private_ip_google_access"]


  secondary_ip_range {
    range_name    = var.gke["secondary_ip_range_name1"]
    ip_cidr_range = var.gke["secondary_ip_cidr1"]
  }

  secondary_ip_range {
    range_name    = var.gke["secondary_ip_range_name2"]
    ip_cidr_range = var.gke["secondary_ip_cidr2"]
  }

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}


