module "datastream" {
  source = "../modules/datastream"

  environment      = var.environment
  vpc_network_name = module.vpc.network_name
  cloudsqlproxy_datastream_ports = {
    dev = [
      data.terraform_remote_state.nonprod_platform.outputs.cloudsqlproxy_datastream_port_dev,
      data.terraform_remote_state.nonprod_platform.outputs.ehr_cloudsqlproxy_datastream_port_dev
    ]
    staging = [
      data.terraform_remote_state.nonprod_platform.outputs.cloudsqlproxy_datastream_port_staging,
      data.terraform_remote_state.nonprod_platform.outputs.ehr_cloudsqlproxy_datastream_port_staging
    ]
  }
  datastream_vpc_subnet_ip_ranges = {
    dev = [
      var.datastream_vpc_subnet_ip_range_dev,
      var.decodable_vpc_subnet_ip_range_dev,
    ]
    staging = [
      var.datastream_vpc_subnet_ip_range_staging,
      var.decodable_vpc_subnet_ip_range_staging,
    ]
  }
  data_platform_managed_service_accounts = [
    "serviceAccount:${data.terraform_remote_state.dev_data_platform.outputs.data_platform_sa_email}",
    "serviceAccount:${data.terraform_remote_state.staging_data_platform.outputs.data_platform_sa_email}"
  ]
  project_id = var.project_id
}
