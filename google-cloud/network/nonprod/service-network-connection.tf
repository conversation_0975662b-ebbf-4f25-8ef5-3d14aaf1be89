############################
# private Services Connection
############################
resource "google_compute_global_address" "nonprod_service_address" {
  project       = var.project_id
  name          = "${var.environment}-service-address"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  address       = var.service_connection_address
  prefix_length = var.service_connection_prefix_length
  network       = module.vpc.network_self_link
}

resource "google_service_networking_connection" "nonprod_service_connection" {
  network                 = module.vpc.network_self_link
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.nonprod_service_address.name]
}