

# General Compute Network
resource "google_compute_subnetwork" "staging_general_compute_us_central1" {
  name                     = var.staging_general_compute["name"]
  description              = var.staging_general_compute["description"]
  project                  = var.project_id
  ip_cidr_range            = var.staging_general_compute["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.staging_general_compute["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

# GKE Nonprod Network
resource "google_compute_subnetwork" "staging_internal_gke_us_central1" {
  name                     = var.staging_internal_gke["name"]
  description              = var.staging_internal_gke["description"]
  project                  = var.project_id
  ip_cidr_range            = var.staging_internal_gke["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.staging_internal_gke["private_ip_google_access"]


  secondary_ip_range {
    range_name    = var.staging_internal_gke["secondary_ip_range_name1"]
    ip_cidr_range = var.staging_internal_gke["secondary_ip_cidr1"]
  }

  secondary_ip_range {
    range_name    = var.staging_internal_gke["secondary_ip_range_name2"]
    ip_cidr_range = var.staging_internal_gke["secondary_ip_cidr2"]
  }

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}
