locals {
  hmh_remote_traffic_selector = ["*************", "*************"]
  hmh_peer_ip                 = "************"
}

# HMH VPN Secret
resource "google_secret_manager_secret" "hmh_secret" {
  secret_id = "hmh-vpn-secret"
  project   = var.project_id

  labels = {
    label = "hmh"
  }

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

# Static IP for VPN Tunnels
resource "google_compute_address" "vpn_static_ip_2" {
  name = "vpn-static-ip-2"
}
# VPN Gateway

resource "google_compute_vpn_gateway" "hmh_gateway" {
  region  = var.region
  project = var.project_id
  name    = "hmh-ehr-vpn"
  network = module.vpc.network.id
}

# Forwarding Rules
resource "google_compute_forwarding_rule" "fr_esp_2" {
  name        = "hmh-fr-esp"
  ip_protocol = "ESP"
  ip_address  = google_compute_address.vpn_static_ip_2.address
  target      = google_compute_vpn_gateway.hmh_gateway.id
}

resource "google_compute_forwarding_rule" "fr_udp500_2" {
  name        = "hmh-fr-udp500"
  ip_protocol = "UDP"
  port_range  = "500"
  ip_address  = google_compute_address.vpn_static_ip_2.address
  target      = google_compute_vpn_gateway.hmh_gateway.id
}

resource "google_compute_forwarding_rule" "fr_udp4500_2" {
  name        = "hmh-fr-udp4500"
  ip_protocol = "UDP"
  port_range  = "4500"
  ip_address  = google_compute_address.vpn_static_ip_2.address
  target      = google_compute_vpn_gateway.hmh_gateway.id
}

# Tunnels
resource "google_secret_manager_secret_version" "hmh_secret_version" {
  secret      = google_secret_manager_secret.hmh_secret.id
  secret_data = var.hmh_vpn_secret_value
}

resource "google_compute_vpn_tunnel" "hmh_tunnel1" {
  name                    = "hmh-vpn-tunnel1"
  region                  = var.region
  target_vpn_gateway      = google_compute_vpn_gateway.hmh_gateway.id
  shared_secret           = google_secret_manager_secret_version.hmh_secret_version.secret_data
  vpn_gateway_interface   = 0
  local_traffic_selector  = [var.hmh_connector.ip_cidr_range]
  remote_traffic_selector = local.hmh_remote_traffic_selector
  peer_ip                 = local.hmh_peer_ip

  depends_on = [
    google_compute_forwarding_rule.fr_esp_2,
    google_compute_forwarding_rule.fr_udp500_2,
    google_compute_forwarding_rule.fr_udp4500_2,
  ]
}

resource "google_compute_route" "hmh_vpn_route_1" {
  name       = "hmh-vpn-route-1"
  network    = module.vpc.network.id
  dest_range = local.hmh_remote_traffic_selector[0]
  priority   = 1000

  next_hop_vpn_tunnel = google_compute_vpn_tunnel.hmh_tunnel1.id
}

resource "google_compute_route" "hmh_vpn_route_2" {
  name       = "hmh-vpn-route-2"
  network    = module.vpc.network.id
  dest_range = local.hmh_remote_traffic_selector[1]
  priority   = 1000

  next_hop_vpn_tunnel = google_compute_vpn_tunnel.hmh_tunnel1.id
}

