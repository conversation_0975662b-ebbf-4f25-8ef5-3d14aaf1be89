/*  

No longer needed as the NAT ROUTER Is creating the Cloud ROUTER as well

module "cloud_router" {
  source  = "app.terraform.io/apella/cloud-router/google"
  version = "1.0.0"

  name   = var.router_name
  region = var.region

  bgp = {
    # The ASN (16550, 64512 - 65534, 4200000000 - 4294967294) can be any private ASN
    # not already used as a peer ASN in the same region and network or 16550 for Partner Interconnect.
    asn = "65001"
  }

  project = var.project_id
  network = module.vpc.network_name
}
*/