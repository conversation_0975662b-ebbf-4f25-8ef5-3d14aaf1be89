resource "google_compute_route" "default_igw" {
  name             = "default-igw-route-to-internet"
  description      = "Default route to IGW needed for outbound access.  Should go through NAT Gateway for Private instances."
  dest_range       = "0.0.0.0/0"
  network          = module.vpc.network_name
  next_hop_gateway = "projects/${var.project_id}/global/gateways/default-internet-gateway"
  priority         = 1000
}