locals {
  remote_traffic_selector = ["**************/32", "*************/32", "*************/32"]
  peer_ip                 = "*************"
}

# NYU VPN Secret
resource "google_secret_manager_secret" "nyu_secret" {
  secret_id = "nyu-vpn-secret"
  project   = var.project_id

  labels = {
    label = "nyu"
  }

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

# Static IP for VPN Tunnels
resource "google_compute_address" "vpn_static_ip_1" {
  name = "vpn-static-ip-1"
}

# VPN Gateway

resource "google_compute_vpn_gateway" "nyu_gateway" {
  region  = var.region
  project = var.project_id
  name    = "apella-cloud-vpn"
  network = module.vpc.network.id
}

# Forwarding Rules
resource "google_compute_forwarding_rule" "fr_esp_1" {
  name        = "fr-esp"
  ip_protocol = "ESP"
  ip_address  = google_compute_address.vpn_static_ip_1.address
  target      = google_compute_vpn_gateway.nyu_gateway.id
}

resource "google_compute_forwarding_rule" "fr_udp500_1" {
  name        = "fr-udp500"
  ip_protocol = "UDP"
  port_range  = "500"
  ip_address  = google_compute_address.vpn_static_ip_1.address
  target      = google_compute_vpn_gateway.nyu_gateway.id
}

resource "google_compute_forwarding_rule" "fr_udp4500_1" {
  name        = "fr-udp4500"
  ip_protocol = "UDP"
  port_range  = "4500"
  ip_address  = google_compute_address.vpn_static_ip_1.address
  target      = google_compute_vpn_gateway.nyu_gateway.id
}

# Tunnels
data "google_secret_manager_secret_version" "nyu_secret" {
  secret = "nyu-vpn-secret"
}

resource "google_compute_vpn_tunnel" "nyu_tunnel1" {
  name                    = "nyu-vpn-tunnel1"
  region                  = var.region
  target_vpn_gateway      = google_compute_vpn_gateway.nyu_gateway.id
  shared_secret           = data.google_secret_manager_secret_version.nyu_secret.secret_data
  vpn_gateway_interface   = 0
  local_traffic_selector  = [var.nyu_connector.ip_cidr_range]
  remote_traffic_selector = local.remote_traffic_selector
  peer_ip                 = local.peer_ip

  depends_on = [
    google_compute_forwarding_rule.fr_esp_1,
    google_compute_forwarding_rule.fr_udp500_1,
    google_compute_forwarding_rule.fr_udp4500_1,
  ]
}

resource "google_compute_route" "nyu_vpn_route_1" {
  name       = "nyu-vpn-route"
  network    = module.vpc.network.id
  dest_range = local.remote_traffic_selector[0]
  priority   = 1000

  next_hop_vpn_tunnel = google_compute_vpn_tunnel.nyu_tunnel1.id
}

resource "google_compute_route" "nyu_vpn_route_2" {
  name       = "nyu-vpn-route-2"
  network    = module.vpc.network.id
  dest_range = local.remote_traffic_selector[1]
  priority   = 1000

  next_hop_vpn_tunnel = google_compute_vpn_tunnel.nyu_tunnel1.id
}

resource "google_compute_route" "nyu_vpn_route_3" {
  name       = "nyu-vpn-route-3"
  network    = module.vpc.network.id
  dest_range = local.remote_traffic_selector[2]
  priority   = 1000

  next_hop_vpn_tunnel = google_compute_vpn_tunnel.nyu_tunnel1.id
}

