##############################
#  subnets for central1 prod
##############################
# update to tfvars and variables

# General Compute Network
resource "google_compute_subnetwork" "prod_us_central1" {
  name                     = var.general_compute["name"]
  description              = var.general_compute["description"]
  project                  = var.project_id
  ip_cidr_range            = var.general_compute["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.general_compute["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}


# prod central vpc connector Network
resource "google_compute_subnetwork" "vpc_connectory_us_central1" {
  name                     = var.vpc_connector["name"]
  description              = var.vpc_connector["description"]
  project                  = var.project_id
  ip_cidr_range            = var.vpc_connector["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.vpc_connector["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

# GKE prod Network
resource "google_compute_subnetwork" "internal_gke_us_central1" {
  name                     = var.internal_gke["name"]
  description              = var.internal_gke["description"]
  project                  = var.project_id
  ip_cidr_range            = var.internal_gke["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.internal_gke["private_ip_google_access"]


  secondary_ip_range {
    range_name    = var.internal_gke["secondary_ip_range_name1"]
    ip_cidr_range = var.internal_gke["secondary_ip_cidr1"]
  }

  secondary_ip_range {
    range_name    = var.internal_gke["secondary_ip_range_name2"]
    ip_cidr_range = var.internal_gke["secondary_ip_cidr2"]
  }


  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

resource "google_compute_subnetwork" "nyu_connector_us_central1" {
  name                     = var.nyu_connector["name"]
  description              = var.nyu_connector["description"]
  project                  = var.project_id
  ip_cidr_range            = var.nyu_connector["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.nyu_connector["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}

resource "google_compute_subnetwork" "hmh_connector_us_central1" {
  name                     = var.hmh_connector["name"]
  description              = var.hmh_connector["description"]
  project                  = var.project_id
  ip_cidr_range            = var.hmh_connector["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.hmh_connector["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}
