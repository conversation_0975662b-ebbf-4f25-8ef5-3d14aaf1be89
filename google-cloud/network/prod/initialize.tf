/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-network-prod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

data "terraform_remote_state" "prod_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-prod"
    }
  }
}

data "terraform_remote_state" "prod_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-platform-prod"
    }
  }
}
