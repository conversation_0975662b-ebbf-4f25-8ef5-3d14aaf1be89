locals {
  nat_ip_address             = "**************"
  nyu_connector_machine_type = "n2d-standard-2"
  nyu_nat_tags               = ["nyu-nat"]
  nyu_nat_zone               = "us-central1-a"
  nyu_vm_image               = "ubuntu-2204-lts"
  nyu_ip_addresses           = ["**************", "*************", "*************"]
}

resource "google_compute_address" "nyu_nat_private_ip_address" {
  name         = "nyu-nat-private-ip-address"
  subnetwork   = google_compute_subnetwork.nyu_connector_us_central1.id
  address_type = "INTERNAL"
  address      = local.nat_ip_address
}

resource "google_service_account" "nyu_nat_sa" {
  account_id   = "nyu-nat-sa"
  project      = var.project_id
  display_name = "NYU NAT Service Account"
}

resource "google_compute_instance" "nyu_nat" {
  name                      = "nyu-nat"
  machine_type              = local.nyu_connector_machine_type
  zone                      = local.nyu_nat_zone
  labels                    = merge(var.labels, { "datadog" = "monitored" })
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = local.nyu_vm_image
    }
  }

  network_interface {
    subnetwork_project = var.project_id
    subnetwork         = google_compute_subnetwork.nyu_connector_us_central1.id
    network_ip         = google_compute_address.nyu_nat_private_ip_address.address
  }

  can_ip_forward = true

  scheduling {
    on_host_maintenance = "MIGRATE"
    automatic_restart   = true
    preemptible         = false
  }

  service_account {
    email = google_service_account.nyu_nat_sa.email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }

  metadata = {
    // This script sets up networking for Tailscale
    startup-script = templatefile("${path.module}/templates/nyu_nat_startup_script.tpl", {
      envoy_port       = var.envoy_port,
      envoy_admin_port = var.envoy_admin_port
      envoy_version    = var.envoy_version
    })
  }

  tags = local.nyu_nat_tags
}
