module "datastream" {
  source = "../modules/datastream"

  environment      = var.environment
  vpc_network_name = module.vpc.network_name
  cloudsqlproxy_datastream_ports = {
    prod = [
      data.terraform_remote_state.prod_platform.outputs.cloudsqlproxy_datastream_port,
      data.terraform_remote_state.prod_platform.outputs.ehr_cloudsqlproxy_datastream_port_prod
    ]
  }
  datastream_vpc_subnet_ip_ranges = {
    prod = [
      var.datastream_vpc_subnet_ip_range,
      var.decodable_vpc_subnet_ip_range
    ]
  }
  data_platform_managed_service_accounts = [
    "serviceAccount:${data.terraform_remote_state.prod_data_platform.outputs.data_platform_sa_email}",
  ]
  project_id = var.project_id
}
