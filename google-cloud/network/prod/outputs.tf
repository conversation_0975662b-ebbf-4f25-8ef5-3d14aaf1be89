/******************************************
	Core Terraform and Project Related Outputs
 *****************************************/

output "project_id" {
  value       = var.project_id
  description = "Project id"
}

/******************************************
	VPC Network Resource Related Outputs
 *****************************************/

# First output option outputs the entire resource instead of specific properties of the resource.  This was supported starting Terraform 12.
output "network" {
  value       = module.vpc.network
  description = "The VPC resource being created"
}

output "network_name" {
  value       = module.vpc.network.name
  description = "The name of the VPC being created"
}

output "network_self_link" {
  value       = module.vpc.network_self_link
  description = "The URI of the VPC being created"
}

/******************************************
	Cloud NAT Resource Related Variables
 *****************************************/
output "cloud_nat_name" {
  description = "The name of the created Cloud NAT instance"
  value       = module.cloud_nat.name
}


/******************************************
	Cloud Router Resource Related Variables
 *****************************************/
/* output "router_name" {
  value       = module.cloud_router.router.name
  description = "The name of the created router"
}

output "router_region" {
  value       = module.cloud_router.router.region
  description = "The region of the created router"
} */

/******************************************
	Shared compute subnetwork
 *****************************************/
output "shared_us_central1" {
  value       = google_compute_subnetwork.shared_us_central1
  description = "shared compute network"
}

/******************************************
	Internal GKE vars
 *****************************************/
output "internal_gke" {
  value       = var.internal_gke
  description = "prod internal gke network config"
}

output "internal_gke_master_range" {
  value       = var.internal_gke_master_range
  description = "prod internal gke master node range"
}

output "internal_gke_us_central1" {
  value       = google_compute_subnetwork.internal_gke_us_central1
  description = "prod internal gke subnet"
}

output "vpc_access_connectors" {
  description = "vpc access connectors, key'd by region"
  value = {
    us-central1 = google_vpc_access_connector.default-prod-us-central1
  }
}

output "datastream_vpc_subnet_ip_range" {
  description = "The IP range allocated by Datastream"
  value       = var.datastream_vpc_subnet_ip_range
}

output "nyu_connector_us_central1" {
  value       = google_compute_subnetwork.nyu_connector_us_central1
  description = "NYU interconnect subnet range"
}

output "hmh_connector_us_central1" {
  value       = google_compute_subnetwork.hmh_connector_us_central1
  description = "HMH interconnect subnet range"
}
