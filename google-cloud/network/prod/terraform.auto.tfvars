/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "prod-network-bfb30f"
environment = "prod"


/******************************************
	VPC Network Resource Related Variables
 *****************************************/
network_name            = "apella-vpc"
shared_vpc_host         = true
description             = "This is used for the prod vpc shared host project network"
auto_create_subnetworks = false

/******************************************
	Sub Network Resource Related Variables
 *****************************************/
log_config = {
  aggregation_interval = "INTERVAL_5_MIN"
  flow_sampling        = 0.5
  metadata             = "INCLUDE_ALL_METADATA"
}

general_compute = {
  name                     = "prod-central1-compute"
  description              = "Prod General Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

shared_compute = {
  name                     = "prod-central1-shared-compute"
  description              = "Prod Shared Compute Resources"
  ip_cidr_range            = "************/22"
  private_ip_google_access = "true"
}

vpc_connector = {
  name                     = "prod-central1-vpc-connector"
  description              = "Prod VPC Connector subnet"
  ip_cidr_range            = "************/28"
  private_ip_google_access = "true"
}

internal_gke = {
  name                     = "prod-central1-internal-gke"
  description              = "Prod Internal GKE (Kubernetes) Container Network"
  ip_cidr_range            = "***********/22"
  private_ip_google_access = "true"
  secondary_ip_range_name1 = "prod-internal-gke-pod-range-1"
  secondary_ip_cidr1       = "*********/14"
  secondary_ip_range_name2 = "prod-internal-gke-svc-range-1"
  secondary_ip_cidr2       = "**********/14"
}

internal_gke_master_range = "***********/28"

nyu_connector = {
  name                     = "prod-central1-nyu-connector"
  description              = "A local subnet range dedicated to VPN traffic from NYU"
  ip_cidr_range            = "**************/29"
  private_ip_google_access = "true"
}

hmh_connector = {
  name                     = "prod-central1-hmh-connector"
  description              = "A local subnet range dedicated to VPN traffic from HMH"
  ip_cidr_range            = "************/24"
  private_ip_google_access = "true"
}


/******************************************
	Cloud NAT Resource Related Variables
 *****************************************/
nat_gateway_name = "prod-nat"
nat_ip_count     = 3

/******************************************
	Cloud Router Resource Related Variables
 *****************************************/
router_name = "prod-router"

/******************************************
	Cloud Firewall Resource Related Variables
 *****************************************/
# IAP IP Address source-ranges = ************/20
iap_source_ranges = ["************/20"]

/******************************************
	private service connection Related Variables
 *****************************************/
service_connection_address       = "************"
service_connection_prefix_length = "23"

/******************************************
	serverless vpc connector
 *****************************************/
serverless_vpc_service_project_nbrs = ["71556946473"]

datastream_vpc_subnet_ip_range = "************/29"
decodable_vpc_subnet_ip_range  = "**********/24"

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-network"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
}

envoy_version = "v1.30.4"
