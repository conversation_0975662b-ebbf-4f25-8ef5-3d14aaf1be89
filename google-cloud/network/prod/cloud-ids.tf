# Commenting out until we decide what to do with Cloud IDS
# resource "google_cloud_ids_endpoint" "central1_endpoint" {
#   name       = "central1-endpoint"
#   location   = "us-central1-c"
#   network    = module.vpc.network.id
#   severity   = "INFORMATIONAL"
#   depends_on = [google_service_networking_connection.prod_service_connection]
# }

# resource "google_compute_packet_mirroring" "ids_mirroring" {
#   name        = "ids-mirroring"
#   description = "Mirroring for IDS to run for all networks with public IP addresses"
#   network {
#     url = module.vpc.network.self_link
#   }
#   collector_ilb {
#     url = google_cloud_ids_endpoint.central1_endpoint.endpoint_forwarding_rule
#   }
#   mirrored_resources {
#     subnetworks {
#       url = google_compute_subnetwork.prod_us_central1.id
#     }

#     subnetworks {
#       url = google_compute_subnetwork.shared_us_central1.id
#     }

#     subnetworks {
#       url = google_compute_subnetwork.internal_gke_us_central1.id
#     }
#   }
# }

# resource "google_monitoring_notification_channel" "team_platform_services_alerts_slack" {
#   display_name = "Platform Services GCP Alerts"
#   type         = "slack"
#   labels = {
#     "channel_name" = "#team-platform-services-alert-prod"
#   }
#   sensitive_labels {
#     auth_token = var.slack_auth_token
#   }
# }

# resource "google_monitoring_alert_policy" "cloud_ids_alert_policy" {

#   display_name = "Intrusion Detection System Alert"
#   combiner     = "OR"

#   conditions {
#     display_name = "Critical or High"

#     condition_threshold {
#       filter     = "resource.type=\"ids.googleapis.com/Endpoint\" AND (jsonPayload.alert_severity=\"HIGH\" OR jsonPayload.alert_severity=\"CRITICAL\")"
#       duration   = "60s"
#       comparison = "COMPARISON_GT"
#       trigger {
#         count = 1
#       }
#     }
#   }
#   enabled = true

#   notification_channels = [
#     google_monitoring_notification_channel.team_platform_services_alerts_slack.id
#   ]
# }
