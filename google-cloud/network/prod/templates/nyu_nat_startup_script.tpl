# Script to install and configure Tailscale
# The OS is updated
sudo apt update -y && sudo apt upgrade -y

# Enable IP forwarding
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p /etc/sysctl.conf

############### ENVOY #####################
# Create Envoy Configuration File
ENVOY_CONFIGURATION_FOLDER=/opt/envoy
ENVOY_CONFIGURATION_FILE=envoy-proxy.yaml

mkdir -p $ENVOY_CONFIGURATION_FOLDER
cat >$ENVOY_CONFIGURATION_FOLDER/$ENVOY_CONFIGURATION_FILE <<EOL
static_resources:
  listeners:
    - name: dynamic_forward_proxy_upgrade
      address:
        socket_address:
          address: 0.0.0.0
          port_value: ${envoy_port}
      filter_chains:
        # Accept http proxy requests, perform a DNS lookup on the final destination, and forward to the end destination
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                '@type': type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                stat_prefix: forward_proxy
                route_config:
                  name: local_route
                  virtual_hosts:
                    - name: dynamic_forward_proxy
                      # Forward to any and all domains
                      domains:
                        - '*'
                      routes:
                        # Handle all http requests for all prefixes
                        - match:
                            prefix: /
                          route:
                            cluster: dynamic_forward_proxy_cluster
                        # This rule is necessary to handle https requests
                        - match:
                            connect_matcher: {}
                          route:
                            cluster: dynamic_forward_proxy_cluster
                            upgrade_configs:
                              - upgrade_type: CONNECT
                                connect_config: {}
                http_filters:
                  # Route requests to the proxy cluster doing DNS lookups
                  - name: envoy.filters.http.dynamic_forward_proxy
                    typed_config:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.dynamic_forward_proxy.v3.FilterConfig
                      dns_cache_config:
                        name: dynamic_forward_proxy_cache_config
                        dns_lookup_family: ALL
                  - name: envoy.filters.http.router
                    typed_config:
                      '@type': type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                http_protocol_options:
                  allow_absolute_url: true
                  accept_http_10: true
                http2_protocol_options:
                  allow_connect: true
                http3_protocol_options:
                  allow_extended_connect: true
                upgrade_configs:
                  - upgrade_type: CONNECT
  clusters:
    # Forward requests on to the original destination by http origin header
    - name: dynamic_forward_proxy_cluster
      dns_lookup_family: ALL
      lb_policy: CLUSTER_PROVIDED
      cluster_type:
        name: envoy.clusters.dynamic_forward_proxy
        typed_config:
          '@type': type.googleapis.com/envoy.extensions.clusters.dynamic_forward_proxy.v3.ClusterConfig
          allow_coalesced_connections: true
          dns_cache_config:
            name: dynamic_forward_proxy_cache_config
            dns_lookup_family: ALL
admin:
  address:
    socket_address:
      address: 0.0.0.0
      port_value: ${envoy_admin_port}
EOL

# Create Envoy Docker Systemd Service
cat >/lib/systemd/system/envoy.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Envoy Proxy Container
Requires=docker.service
After=docker.service

[Service]
ExecStartPre=-/usr/bin/docker stop envoy
ExecStartPre=-/usr/bin/docker rm envoy
ExecStartPre=/usr/bin/docker pull envoyproxy/envoy:${envoy_version}
ExecStart=/usr/bin/docker run --name envoy -v $ENVOY_CONFIGURATION_FOLDER/$ENVOY_CONFIGURATION_FILE:/envoy-proxy.yaml -p ${envoy_port}:${envoy_port} -p ${envoy_admin_port}:${envoy_admin_port} --restart always envoyproxy/envoy:${envoy_version} -c /envoy-proxy.yaml
Restart=always
EOL

# Start envoy service
systemctl daemon-reload
systemctl start envoy
