# Shared Compute Network
resource "google_compute_subnetwork" "shared_us_central1" {
  name                     = var.shared_compute["name"]
  description              = var.shared_compute["description"]
  project                  = var.project_id
  ip_cidr_range            = var.shared_compute["ip_cidr_range"]
  region                   = var.region
  network                  = module.vpc.network_self_link
  private_ip_google_access = var.shared_compute["private_ip_google_access"]

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}
