module "github_runner_firewall" {
  source = "../modules/gh-runner-firewall"

  project_id   = var.project_id
  network_name = module.vpc.network_self_link
  name_prefix  = "gh-runner"

  health_check_source_ranges = ["**********/16", "***********/22"]
  health_check_port          = 8080
  target_tags                = ["gh-runner-vm"]
}

output "github_runner_target_tags" {
  description = "Target tags for GitHub runners"
  value       = module.github_runner_firewall.target_tags
}
