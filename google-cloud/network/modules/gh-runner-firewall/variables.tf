variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "network_name" {
  description = "Name of the network to create the firewall rule in"
  type        = string
}

variable "health_check_source_ranges" {
  description = "Google Cloud's health check probe IP ranges"
  type        = list(string)
  default     = ["**********/16", "***********/22"]
}

variable "health_check_port" {
  description = "Port on which the health check server listens"
  type        = number
  default     = 8080
}

variable "target_tags" {
  description = "Network tags to apply the firewall rule to"
  type        = list(string)
  default     = ["gh-runner-vm"]
}

variable "name_prefix" {
  description = "Prefix to use for resource names"
  type        = string
  default     = "gh-runner"
}

variable "priority" {
  description = "Priority of the firewall rule"
  type        = number
  default     = 1000
}

variable "log_config_metadata" {
  description = "Metadata to include in logs"
  type        = string
  default     = "INCLUDE_ALL_METADATA"
}
