resource "google_compute_firewall" "allow_health_checks" {
  name      = "allow-health-checks-${var.name_prefix}"
  project   = var.project_id
  network   = var.network_name
  priority  = var.priority
  direction = "INGRESS"

  source_ranges = var.health_check_source_ranges

  allow {
    protocol = "tcp"
    ports    = [var.health_check_port]
  }

  target_tags = var.target_tags

  log_config {
    metadata = var.log_config_metadata
  }
}
