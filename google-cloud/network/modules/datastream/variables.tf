variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["nonprod", "prod"], var.environment)
    error_message = "Environment must be either nonprod or prod."
  }
}

variable "data_platform_managed_service_accounts" {
  description = "The managed service account email for the Data Platform project"
  type        = list(string)
}

variable "datastream_vpc_subnet_ip_ranges" {
  description = "Datstream's subnet IP range allocated in the VPC (by Datastream)"
  type        = map(list(string))
}

variable "cloudsqlproxy_datastream_ports" {
  description = "Datastream's SQL proxy port running on the bastion host"
  type        = map(list(string))
}

variable "vpc_network_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "project_id" {
  description = "GCP Project ID"
}
