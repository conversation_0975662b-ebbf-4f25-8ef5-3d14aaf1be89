variable "network_environment" {
  type        = string
  description = "GCP Network environment"
  validation {
    condition     = contains(["nonprod", "prod"], var.network_environment)
    error_message = "Environment must be either nonprod or prod."
  }
}

variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "region" {
  description = "GCP region"
}

variable "network" {
  description = "The VPC network for this subnet"
}

variable "private_ip_google_access" {
  description = "True to allow private ip google access"
}

variable "ip_cidr_range" {
  description = "The ip range for the subnet"
}

variable "log_config" {
  type = map(any)
  default = {
    aggregation_interval = ""
    flow_sampling        = ""
    metadata             = ""
  }
}

variable "dataproc_sa_email" {
  description = "The service account email for dataproc"
}
