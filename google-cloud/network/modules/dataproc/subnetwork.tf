resource "google_compute_subnetwork" "dataproc_subnetwork" {
  name                     = "${var.network_environment}-${var.region}-${var.environment}-dataproc"
  description              = "${var.network_environment} ${var.environment} DataProc subnet"
  project                  = var.project_id
  ip_cidr_range            = var.ip_cidr_range
  region                   = var.region
  network                  = var.network
  private_ip_google_access = var.private_ip_google_access

  log_config {
    aggregation_interval = var.log_config["aggregation_interval"]
    flow_sampling        = var.log_config["flow_sampling"]
    metadata             = var.log_config["metadata"]
  }
}
