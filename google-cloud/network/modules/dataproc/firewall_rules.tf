resource "google_compute_firewall" "allow_dataproc" {
  name      = "allow-${var.environment}-dataproc"
  project   = var.project_id
  network   = var.network
  priority  = 1000
  direction = "INGRESS"

  # From https://cloud.google.com/dataproc/docs/concepts/configuring-clusters/network#create_an_ingress_firewall_rule
  # This is a very permissive rule.  It allows TCP/UDP on all ports,
  # but only from and to the same dataproc service account.
  source_service_accounts = [var.dataproc_sa_email]
  target_service_accounts = [var.dataproc_sa_email]
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  log_config {
    metadata = var.log_config["metadata"]
  }
}
