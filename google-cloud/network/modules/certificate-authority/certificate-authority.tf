## Root CA pool
resource "google_privateca_ca_pool" "cloud_ca_pool" {
  name     = "apella_root_ca_pool"
  location = var.region
  tier     = var.ca_tier
  publishing_options {
    publish_ca_cert = true
    publish_crl     = false
  }
}
## Subordinate CA pool
resource "google_privateca_ca_pool" "edge_subca_pool" {
  name     = "apella_edge_sub_ca_pool"
  location = var.region
  tier     = var.ca_tier
  publishing_options {
    publish_ca_cert = true
    publish_crl     = false
  }
}

## rootCA
resource "google_privateca_certificate_authority" "root_ca" {
  location                 = var.region
  certificate_authority_id = "apella-root-ca"
  pool                     = google_privateca_ca_pool.cloud_ca_pool.name
  config {
    x509_config {
      ca_options {
        is_ca                  = true
        max_issuer_path_length = 10
      }
      key_usage {
        base_key_usage {
          crl_sign  = true
          cert_sign = true
        }
        extended_key_usage {
          server_auth      = true
          client_auth      = true
          code_signing     = true
          email_protection = false
        }
      }
    }
    subject_config {
      subject {
        organization = var.subjectOrg
        common_name  = "root_authority"
      }
    }
  }
  lifetime = "${10 * 365 * 24 * 3600}s"
  key_spec {
    cloud_kms_key_version = trimprefix(data.google_kms_crypto_key_version.ca-key-version.id, "//cloudkms.googleapis.com/v1/")
  }
}

## sub CA
resource "google_privateca_certificate_authority" "edge_sub_ca" {
  location                 = var.region
  certificate_authority_id = "apella-edge-nonprod-sub-ca"
  pool                     = google_privateca_ca_pool.edge_subca_pool.name
  type                     = var.sub_ca_type
  deletion_protection      = false
  config {
    x509_config {
      ca_options {
        is_ca                  = true
        max_issuer_path_length = 0
      }
      key_usage {
        base_key_usage {
          crl_sign  = true
          cert_sign = true
        }
        extended_key_usage {
          server_auth      = true
          client_auth      = true
          code_signing     = true
          email_protection = false
        }
      }
    }
    subject_config {
      subject {
        organization = var.subjectOrg
        common_name  = "sub_authority"
      }
    }
  }
  lifetime = "${10 * 365 * 24 * 3600}s"
  key_spec {
    algorithm = var.privateKeyAlgo
  }
}
