variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["nonprod", "prod"], var.environment)
    error_message = "Environment must be either nonprod or prod."
  }
}

variable "vpc_network_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
  type        = string
}

variable "ca_tier" {
  description = "Tier of the Certificate Authority (e.g., DEVOPS or ENTERPRISE)"
  type        = string
  default     = "ENTERPRISE"
}

variable "sub_ca_type" {
  description = "SUBORDINATE Type of the Certificate Authority"
  type        = string
  default     = "SUBORDINATE"
}

variable "kmsKeyRingName" {
  description = "Key management service name"
  default     = "apella_ca_kms"
  type        = string
}


variable "kmsKeyName" {
  description = "Key name"
  default     = "apella_ca_kms_key"
  type        = string
}

variable "kmsKeyAlgo" {
  description = "Key algorithm"
  default     = "RSA_SIGN_PKCS1_4096_SHA256"
  type        = string
}

variable "privateKeyAlgo" {
  description = "Key algorithm"
  default     = "RSA_PKCS1_4096_SHA256"
  type        = string
}


variable "subjectOrg" {
  description = "Org"
  default     = "Apella"
  type        = string
}
