#create a keyring
resource "google_kms_key_ring" "ca-keyring" {
  name     = var.kmsKeyRingName
  location = var.region
}
#create a key
resource "google_kms_crypto_key" "ca-key" {
  name     = var.kmsKeyName
  key_ring = google_kms_key_ring.ca-keyring.id
  purpose  = "ASYMMETRIC_SIGN"

  version_template {
    algorithm        = var.kmsKeyAlgo
    protection_level = "HSM"
  }

  lifecycle {
    prevent_destroy = false
  }
}
#KMS Data block for CAS key version input
data "google_kms_crypto_key_version" "ca-key-version" {
  crypto_key = google_kms_crypto_key.ca-key.id
}

resource "google_project_service_identity" "privateca_sa" {
  provider = google-beta
  service  = "privateca.googleapis.com"
  project  = var.project_id
}

resource "google_kms_crypto_key_iam_binding" "privateca_sa_keyuser_signerverifier" {
  crypto_key_id = google_kms_crypto_key.ca-key.id
  role          = "roles/cloudkms.signerVerifier"

  members = [
    "serviceAccount:${google_project_service_identity.privateca_sa.email}",
  ]
}

resource "google_kms_crypto_key_iam_binding" "privateca_sa_keyuser_viewer" {
  crypto_key_id = google_kms_crypto_key.ca-key.id
  role          = "roles/viewer"
  members = [
    "serviceAccount:${google_project_service_identity.privateca_sa.email}",
  ]
}