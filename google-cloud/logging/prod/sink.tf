#Raw Logs exports to GCS Dev
resource "google_logging_folder_sink" "raw_log_gcs_prod" {
  name             = "raw_log_gcs_${var.environment}_prod"
  folder           = data.google_active_folder.prod.name
  destination      = "storage.googleapis.com/${module.raw_log_bucket.name}"
  filter           = ""
  include_children = "true"
}

resource "google_logging_folder_sink" "raw_log_gcs_shared" {
  name             = "raw_log_gcs_${var.environment}_shared"
  folder           = data.google_active_folder.shared.name
  destination      = "storage.googleapis.com/${module.raw_log_bucket.name}"
  filter           = ""
  include_children = "true"
}

