module "raw_log_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.0.2"
  # insert required variables here
  bucket_name        = var.bucket_name
  environment        = var.environment
  labels             = var.labels
  location           = var.location
  project_id         = var.project_id
  retention_policy   = var.retention_policy
  storage_class      = var.storage_class
  bucket_policy_only = var.bucket_policy_only
  iam_members        = var.iam_members
  lifecycle_rules    = var.lifecycle_rules
  versioning         = var.versioning
}

resource "google_project_iam_member" "log-writer-unfiltered-shared" {
  project = var.project_id
  role    = "roles/storage.objectCreator"
  member  = google_logging_folder_sink.raw_log_gcs_shared.writer_identity
}

resource "google_project_iam_member" "log-writer-unfiltered-prod" {
  project = var.project_id
  role    = "roles/storage.objectCreator"
  member  = google_logging_folder_sink.raw_log_gcs_prod.writer_identity
}