/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "prod-log-f5365b"
environment = "prod"

#bucket vars
bucket_name   = "raw-log-f5365b"
location      = "us-central1"
storage_class = "COLDLINE"
labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "prod-logs"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-emails-and-phone-numbers"
  "vanta-contains-ephi"      = "true"
  "vanta-no-alert"           = "it-stores-raw-logs-from-prod-environment"
}
bucket_policy_only = true
versioning         = false
iam_members = [{
  role   = "roles/storage.objectAdmin"
  member = "group:<EMAIL>"
}]
lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 365
    with_state = "LIVE"
  }
}]
