/******************************************
  Audit Bucket
 *****************************************/
resource "google_service_account" "datadog_archive_sa" {
  account_id   = "datadaog-archive-sa"
  display_name = "Datadog Log Archiver Service Account"
}

module "datadog_audit_bucket" {
  source      = "app.terraform.io/apella/cloud-storage/google"
  version     = "1.2.8"
  bucket_name = "${var.environment}-datadog-archive-bucket"
  environment = var.environment
  labels      = var.labels
  location    = var.location
  project_id  = var.project_id
  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }

      condition = {
        age        = 365
        with_state = "ANY"
      }
    }
  ]
}

resource "google_storage_bucket_iam_member" "datadog_audit_bucket_memebers" {
  bucket = module.datadog_audit_bucket.bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.datadog_archive_sa.email}"
}

/******************************************
  Audit bucket monitor
 *****************************************/

module "audit_logs_exported_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "1.0.0"

  datadog_api_key = var.datadog_api_key
  datadog_app_key = var.datadog_app_key
  environment     = var.environment
  project_id      = var.project_id

  type               = "metric alert"
  name               = "${var.environment}: Exporting audit logs"
  query              = "sum(last_12h):sum:logs.audit.count{env:prod}.as_count() <= 1"
  threshold_critical = 1
  message            = "No audit logs being exported and retained.\n\n${var.slack_alert_channel}"
  tags               = ["team:platform-services"]
}


locals {
  roleList = [
    "roles/compute.viewer",
    "roles/monitoring.viewer",
    "roles/cloudasset.viewer",
    "roles/browser",
  ]
}

resource "google_project_iam_member" "datadog_service_account_iam_member" {
  for_each = toset(local.roleList)
  project  = google_service_account.datadog_archive_sa.project
  role     = each.value
  member   = "serviceAccount:${google_service_account.datadog_archive_sa.email}"
}

resource "datadog_integration_gcp_sts" "datadog_sts" {
  client_email    = google_service_account.datadog_archive_sa.email
  host_filters    = []
  automute        = true
  is_cspm_enabled = false
}

// Grant token creator role to the Datadog principal account.
resource "google_service_account_iam_member" "datadog_token_creator_iam" {
  service_account_id = google_service_account.datadog_archive_sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = format("serviceAccount:%s", datadog_integration_gcp_sts.datadog_sts.delegate_account_email)
}
