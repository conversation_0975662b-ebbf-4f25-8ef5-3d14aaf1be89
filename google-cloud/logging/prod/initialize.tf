/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-logging-prod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source = "hashicorp/google"
    }
    datadog = {
      source  = "DataDog/datadog"
      version = "3.31.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

locals {
  org_id = data.google_organization.org.org_id
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

data "google_active_folder" "prod" {
  display_name = "prod"
  parent       = "organizations/${data.google_organization.org.org_id}"
}
