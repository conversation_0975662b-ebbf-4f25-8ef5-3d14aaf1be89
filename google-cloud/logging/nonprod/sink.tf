#Raw Logs exports to GCS Dev
resource "google_logging_folder_sink" "raw_log_gcs_dev" {
  name             = "raw_log_gcs_${var.environment}_dev"
  folder           = data.google_active_folder.dev.name
  destination      = "storage.googleapis.com/${module.raw_log_bucket.name}"
  filter           = ""
  include_children = "true"
}

resource "google_logging_folder_sink" "raw_log_gcs_staging" {
  name             = "raw_log_gcs_${var.environment}_staging"
  folder           = data.google_active_folder.staging.name
  destination      = "storage.googleapis.com/${module.raw_log_bucket.name}"
  filter           = ""
  include_children = "true"
}

