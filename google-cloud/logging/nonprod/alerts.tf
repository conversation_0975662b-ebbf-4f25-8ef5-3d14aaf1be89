resource "google_monitoring_alert_policy" "vm_highcpu_alert_policy" {
  display_name = "Dev VM CPU Utilization High"
  combiner     = "OR"
  conditions {
    display_name = "Dev VM Instance - CPU utilization High"
    condition_threshold {
      filter          = "metric.type=\"compute.googleapis.com/instance/cpu/utilization\" AND resource.type=\"gce_instance\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.vm_highcpu_threshold
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }
}

resource "google_monitoring_alert_policy" "firestore_highwrites_alert_policy" {
  display_name = "Dev Firestore Writes Threshold Exceeded"
  combiner     = "OR"
  conditions {
    display_name = "Dev Firestore Writes Threshold Exceeded"
    condition_threshold {
      filter          = "metric.type=\"firestore.googleapis.com/document/write_count\" AND resource.type=\"firestore_instance\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.firestore_writes_threshold
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }
}

resource "google_monitoring_alert_policy" "firestore_highreads_alert_policy" {
  display_name = "Dev Firestore Reads Threshold Exceeded"
  combiner     = "OR"
  conditions {
    display_name = "Dev Firestore Reads Threshold Exceeded"
    condition_threshold {
      filter          = "metric.type=\"firestore.googleapis.com/document/read_count\" AND resource.type=\"firestore_instance\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.firestore_reads_threshold
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }
}

resource "google_monitoring_alert_policy" "firestore_highdeletes_alert_policy" {
  display_name = "Dev Firestore Deletes Threshold Exceeded"
  combiner     = "OR"
  conditions {
    display_name = "Dev Firestore Deletes Threshold Exceeded"
    condition_threshold {
      filter          = "metric.type=\"firestore.googleapis.com/document/delete_count\" AND resource.type=\"firestore_instance\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.firestore_deletes_threshold
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }
}

resource "google_monitoring_alert_policy" "firestore_highactiveconnections_alert_policy" {
  display_name = "Dev Firestore Active Connections Threshold Exceeded"
  combiner     = "OR"
  conditions {
    display_name = "Dev Firestore Active Conections Threshold Exceeded"
    condition_threshold {
      filter          = "metric.type=\"firestore.googleapis.com/network/active_connections\" AND resource.type=\"firestore_instance\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.firestore_activeconnections_threshold
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }
}