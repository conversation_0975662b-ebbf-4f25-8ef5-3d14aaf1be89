/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "nonprod-log-f302cc"
environment = "nonprod"


#bucket vars
bucket_name   = "raw-log-f302cc"
location      = "us-central1"
storage_class = "COLDLINE"
labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "nonprod-logs"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-emails-and-phone-numbers"
  "vanta-contains-ephi"      = "true"
  "vanta-no-alert"           = "it-stores-raw-logs-from-non-prod-environment"
}
bucket_policy_only = true
versioning         = false
iam_members = [{
  role   = "roles/storage.objectAdmin"
  member = "group:<EMAIL>"
}]
lifecycle_rules = [{
  action = {
    type = "Delete"
  }
  condition = {
    age        = 365
    with_state = "LIVE"
  }
}]

#VM alert thresholds
vm_highcpu_threshold = 0.80

#firestore alert thresholds
firestore_writes_threshold            = 10
firestore_reads_threshold             = 20
firestore_deletes_threshold           = 10
firestore_activeconnections_threshold = 20
