/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-logging-nonprod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source = "hashicorp/google"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_active_folder" "dev" {
  display_name = "dev"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

data "google_active_folder" "staging" {
  display_name = "staging"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

locals {
  org_id = data.google_organization.org.org_id
}
