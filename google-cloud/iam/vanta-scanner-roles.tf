locals {
  vanta_scanner_sa = data.terraform_remote_state.global_security.outputs.vanta-scanner-sa
}

###
# Custom Roles to allow <PERSON><PERSON> to scan our cloud
###

# Role for individual projects
resource "google_organization_iam_custom_role" "vanta_project_scanner_sa_role" {
  role_id     = "VantaProjectScanner"
  title       = "Vanta Project Scanner SA Role"
  description = "Gives access to Vanta service accounts to scan projects"
  org_id      = var.organization_id
  permissions = [
    "bigquery.datasets.get",
    "bigtable.instances.get",
    "compute.instances.get",
    "compute.subnetworks.get",
    "pubsub.topics.get",
    "resourcemanager.projects.get",
    "storage.buckets.get"
  ]
}

# Role for organization-wide visibility
resource "google_organization_iam_custom_role" "vanta_organization_scanner_sa_role" {
  role_id     = "VantaOrganizationScanner"
  title       = "Vanta Organization SA Scanner Role"
  description = "Gives Vanta access to scan aspects of the Apella organization"
  org_id      = var.organization_id
  permissions = [
    "iam.roles.list",
    "resourcemanager.organizations.getIamPolicy",
    "resourcemanager.folders.getIamPolicy"
  ]
}

# New Vanta SA (10/2023)

# Creates the VantaExtensiveOrganizationScanner role
resource "google_organization_iam_custom_role" "vanta_org_scanner_role" {
  org_id      = var.organization_id
  role_id     = "VantaExtensiveOrganizationScanner"
  title       = "Vanta Extensive Organization Scanner"
  description = "Role for listing inherited IAM policies"
  permissions = [
    "iam.roles.list",
    "resourcemanager.organizations.getIamPolicy",
    "resourcemanager.folders.getIamPolicy",
    "resourcemanager.projects.get",
    "resourcemanager.projects.list",
    "resourcemanager.folders.list",
    "bigquery.datasets.get",
    "compute.instances.get",
    "compute.instances.getEffectiveFirewalls",
    "compute.subnetworks.get",
    "pubsub.topics.get",
    "storage.buckets.get",
    "appengine.applications.get",
    "cloudasset.assets.searchAllResources",
  ]
}

# Grant VantaExtensiveOrganizationScanner role to the service account at the organization level
resource "google_organization_iam_binding" "vanta_org_binding" {
  org_id = var.organization_id
  role   = "organizations/${var.organization_id}/roles/VantaExtensiveOrganizationScanner"
  members = [
    "serviceAccount:${local.vanta_scanner_sa}"
  ]
}

# Grant iam.securityReviewer role to the service account at the organization level
resource "google_organization_iam_binding" "iam_security_reviewer" {
  org_id = var.organization_id
  role   = "roles/iam.securityReviewer"
  members = [
    "serviceAccount:${local.vanta_scanner_sa}"
  ]
}
