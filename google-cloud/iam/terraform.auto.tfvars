/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region          = "us-central1"
project_id      = "terraform-reference-app"
organization_id = "212613986540"
environment     = "prod"

nonprod_network_project_id = "nonprod-network-9523f0"
prod_network_project_id    = "prod-network-bfb30f"
prod_security_project_id   = "prod-security-ab8e3e"

/******************************************
	Group variables
 *****************************************/
group_prefix       = "GCP"
gcp_admin_name     = "ADMIN"
gcp_admin_email    = "<EMAIL>"
gcp_admin_owners   = ["<EMAIL>"]
gcp_admin_managers = ["<EMAIL>"]
gcp_admin_members  = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]


