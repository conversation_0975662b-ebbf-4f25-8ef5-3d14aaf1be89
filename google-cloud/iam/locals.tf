locals {
  organization_id               = data.google_organization.org.org_id
  permissionsSupportedInProject = [for perm in data.google_iam_testable_permissions.project_supported_permissions.permissions : perm.name]
  permissionsTestingInProject   = [for perm in data.google_iam_testable_permissions.project_testing_permissions.permissions : perm.name]
  permissionsValidInProject     = setunion(local.permissionsSupportedInProject, local.permissionsTestingInProject)

  permissionsSupportedInOrg = [for perm in data.google_iam_testable_permissions.org_supported_permissions.permissions : perm.name]
  permissionsTestingInOrg   = [for perm in data.google_iam_testable_permissions.org_testing_permissions.permissions : perm.name]
  permissionsValidInOrg     = setunion(local.permissionsSupportedInOrg, local.permissionsTestingInOrg)
}