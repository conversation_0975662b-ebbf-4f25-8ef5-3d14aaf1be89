/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "organization_id" {
  description = "GCP Organization ID"
  default     = ""
}

variable "project_id" {
  description = "GCP Project ID"
  default     = ""
}

variable "nonprod_network_project_id" {
  description = "GCP Project ID for non prod network"
}

variable "prod_network_project_id" {
  description = "GCP Project ID for prod network"
}

variable "prod_security_project_id" {
  description = "GCP Project ID for prod security"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

# Group Variables
variable "gcp_admin_name" {
  description = "Name of the GCP Admin Group"
  type        = string
}

variable "gcp_admin_email" {
  description = "Email of GCP Admin Group"
  type        = string
}

variable "group_prefix" {
  description = "Suffix of the groups to create"
  type        = string
  default     = "GCP"
}

variable "gcp_admin_owners" {
  description = "Owners for GCP Admin Group"
  type        = list(any)
}

variable "gcp_admin_members" {
  description = "Members for GCP Admin Group"
  type        = list(any)
}

variable "gcp_admin_managers" {
  description = "Managers for GCP Admin Group"
  type        = list(any)
}
