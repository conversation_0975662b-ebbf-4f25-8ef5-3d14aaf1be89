#RS commenting this out as this group was created in Google workspace
/*
module "gcp_admin_group" {
  source  = "terraform-google-modules/group/google"
  version = "0.2.0"

  id           = "${var.group_prefix}-${var.gcp_admin_name}@${var.domain_name}"
  display_name = "${var.group_prefix}-${var.gcp_admin_name}"
  description  = "Group of GCP Admins"
  domain       = var.domain_name
  owners       = var.gcp_admin_owners
  managers     = var.gcp_admin_managers
  members      = var.gcp_admin_members
}
*/





