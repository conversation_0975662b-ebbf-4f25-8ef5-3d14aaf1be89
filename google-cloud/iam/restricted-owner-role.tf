#Creates custom roles based on Owner role but takes away permisions to enable APIs

# list of roles to combine permissions for
locals {
  roleListOwner = [
    "roles/owner",
    "roles/storage.admin",
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner"
  ]
}

# query GCP for up to date role definitions
data "google_iam_role" "owner_roleinfo" {
  for_each = toset(local.roleListOwner)
  name     = each.value
}

locals {
  # flattens a set of sets of permissions from each role into a single set containing all permissions
  allPermissionsOwner = flatten([for role in data.google_iam_role.owner_roleinfo : role.included_permissions])
  # A temporary hack to get iam permission applies unblocked. There is 4k limit per role and currectly we are pushing past it.
  # This removes 1800 permissions that we do not use in order to get tf-iam to work
  permissionsToRemoveOwner = [
    "serviceusage.services.enable",
    "resourcemanager.projects.update",
  ]

  # creates a subset of permissions that excludes any permissions we want to disallow and those which are unsupported in custom roles
  permissionsListOwner = setintersection(setsubtract(local.allPermissionsOwner, local.permissionsToRemoveOwner), local.permissionsValidInProject)

  # creates a set of permission sets of size equal to half of the length of the overall permissions listing rounded down
  # this is due to a GCP API payload limitation of 64k. this may need to be tweaked if the overall list of permissions increases significantly.
  permissionsToApplyOwner = chunklist(local.permissionsListOwner, floor(length(local.permissionsListOwner) / 8))
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_1" {
  role_id     = "projectMaintainer_1"
  org_id      = local.organization_id
  title       = "project maintainer role (1 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[0]
}


resource "google_organization_iam_custom_role" "custom_role_project_maintainer_2" {
  role_id     = "projectMaintainer_2"
  org_id      = local.organization_id
  title       = "project maintainer role (2 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[1]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_3" {
  role_id     = "projectMaintainer_3"
  org_id      = local.organization_id
  title       = "project maintainer role (3 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[2]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_4" {
  role_id     = "projectMaintainer_4"
  org_id      = local.organization_id
  title       = "project maintainer role (4 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[3]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_5" {
  role_id     = "projectMaintainer_5"
  org_id      = local.organization_id
  title       = "project maintainer role (5 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[4]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_6" {
  role_id     = "projectMaintainer_6"
  org_id      = local.organization_id
  title       = "project maintainer role (6 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[5]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_7" {
  role_id     = "projectMaintainer_7"
  org_id      = local.organization_id
  title       = "project maintainer role (7 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[6]
}

resource "google_organization_iam_custom_role" "custom_role_project_maintainer_8" {
  role_id     = "projectMaintainer_8"
  org_id      = local.organization_id
  title       = "project maintainer role (8 of 8)"
  description = "clone of roles/owner without the ability for project maintainers to enable APIs"
  permissions = local.permissionsToApplyOwner[7]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_1" {
  role_id     = "consumerSA_1"
  org_id      = local.organization_id
  title       = "consumer SA role (1 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[0]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_2" {
  role_id     = "consumerSA_2"
  org_id      = local.organization_id
  title       = "consumer SA role (2 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[1]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_3" {
  role_id     = "consumerSA_3"
  org_id      = local.organization_id
  title       = "consumer SA role (3 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[2]
}


resource "google_organization_iam_custom_role" "custom_role_consumer_sa_4" {
  role_id     = "consumerSA_4"
  org_id      = local.organization_id
  title       = "consumer SA role (4 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[3]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_5" {
  role_id     = "consumerSA_5"
  org_id      = local.organization_id
  title       = "consumer SA role (5 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[4]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_6" {
  role_id     = "consumerSA_6"
  org_id      = local.organization_id
  title       = "consumer SA role (6 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[5]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_7" {
  role_id     = "consumerSA_7"
  org_id      = local.organization_id
  title       = "consumer SA role (7 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[6]
}

resource "google_organization_iam_custom_role" "custom_role_consumer_sa_8" {
  role_id     = "consumerSA_8"
  org_id      = local.organization_id
  title       = "consumer SA role (8 of 8)"
  description = "clone of roles/owner without the ability for consumer SA to enable APIs"
  permissions = local.permissionsToApplyOwner[7]
}

