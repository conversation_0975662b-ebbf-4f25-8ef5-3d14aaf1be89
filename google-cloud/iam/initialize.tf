/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-iam"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "google"
      version = "~> 5.0"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}

provider "google" {
  #RS 4/20/21 commented out project as IAM is global and does not require project
  #project     = var.project_id
  region = var.region
}

data "google_organization" "org" {
  domain = var.domain_name
}

#Query project permissions that are supported in custom roles
data "google_iam_testable_permissions" "project_supported_permissions" {
  full_resource_name   = "//cloudresourcemanager.googleapis.com/projects/${var.project_id}"
  stages               = ["GA", "BETA", "ALPHA", "DEPRECATED"]
  custom_support_level = "SUPPORTED"
}

data "google_iam_testable_permissions" "project_testing_permissions" {
  full_resource_name   = "//cloudresourcemanager.googleapis.com/projects/${var.project_id}"
  stages               = ["GA", "BETA", "ALPHA", "DEPRECATED"]
  custom_support_level = "TESTING"
}

#Query organization permissions that are supported in custom roles
data "google_iam_testable_permissions" "org_supported_permissions" {
  full_resource_name   = "//cloudresourcemanager.googleapis.com/organizations/${local.organization_id}"
  stages               = ["GA", "BETA", "ALPHA", "DEPRECATED"]
  custom_support_level = "SUPPORTED"
}

data "google_iam_testable_permissions" "org_testing_permissions" {
  full_resource_name   = "//cloudresourcemanager.googleapis.com/organizations/${local.organization_id}"
  stages               = ["GA", "BETA", "ALPHA", "DEPRECATED"]
  custom_support_level = "TESTING"
}
