/************************************************************
  define roles to be assigned to  GCP Admin
************************************************************/

locals {
  gcp_admin_group_org_roles = [
    "roles/securitycenter.admin",
    "roles/threatdetection.editor",
    "roles/iam.securityReviewer",
    "roles/orgpolicy.policyAdmin",
    "roles/billing.admin",
  ]
}

/*
resource "google_organization_iam_member" "gcp_admim_org_role_membership" {
  for_each = toset(local.gcp_admin_group_org_roles)
  org_id   = local.organization_id
  role     = each.value
  member   = "group:${var.gcp_admin_email}"
}
*/
