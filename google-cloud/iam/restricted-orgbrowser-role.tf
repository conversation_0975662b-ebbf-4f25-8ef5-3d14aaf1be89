# creates a orgBrowser role that takes away permissions to view IAM policies

# list of roles to combine permissions for
locals {
  roleListBrowser = [
    "roles/browser"
  ]
}

# query GCP for up to date role definitions
data "google_iam_role" "browser_roleinfo" {
  for_each = toset(local.roleListBrowser)
  name     = each.value
}

locals {
  # flattens a set of sets of permissions from each role into a single set containing all permissions
  allPermissionsBrowser = flatten([for role in data.google_iam_role.browser_roleinfo : role.included_permissions])

  permissionsToRemoveBrowser = ["resourcemanager.projects.getIamPolicy"]

  # creates a subset of permissions that excludes any permissions we want to disallow and those which are unsupported in custom roles
  permissionsListBrowser = setintersection(setsubtract(local.allPermissionsBrowser, local.permissionsToRemoveBrowser), local.permissionsValidInOrg)

  permissionsToApplyBrowser = local.permissionsListBrowser
}

resource "google_organization_iam_custom_role" "custom_role_org_browser" {
  role_id     = "orgBrowser"
  org_id      = local.organization_id
  title       = "org browser role"
  description = "clone of roles/browser without the ability to inspect IAM"
  permissions = local.permissionsToApplyBrowser
}