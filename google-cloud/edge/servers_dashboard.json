{"title": "Edge Servers", "description": "", "widgets": [{"id": 6144304356418150, "definition": {"title": "Normalized System Load by Host", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query3"}], "response_format": "timeseries", "queries": [{"query": "max:system.load.norm.1{$customer,$site,$role,$env} by {host}", "data_source": "metrics", "name": "query3"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "markers": [{"value": "y = 1", "display_type": "error dashed"}]}, "layout": {"x": 0, "y": 0, "width": 7, "height": 3}}, {"id": 6018549881038822, "definition": {"title": "CPU by Host", "title_size": "16", "title_align": "left", "type": "hostmap", "requests": {"fill": {"q": "avg:system.cpu.user{$customer,$role,$env,$site} by {host}"}}, "node_type": "host", "no_metric_hosts": true, "no_group_hosts": true, "group": ["customer", "site"], "scope": ["$customer", "$role", "$env", "$site"], "style": {"palette": "green_to_orange", "palette_flip": false, "fill_min": "0", "fill_max": "100"}}, "layout": {"x": 7, "y": 0, "width": 5, "height": 6}}, {"id": 1691985597757060, "definition": {"title": "Max CPU%", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"response_format": "scalar", "queries": [{"query": "max:system.cpu.user{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query1", "aggregator": "max"}], "formulas": [{"formula": "query1"}], "sort": {"count": 500, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}]}, "layout": {"x": 0, "y": 3, "width": 3, "height": 3}}, {"id": 6739825257871842, "definition": {"title": "CPU by Host", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "response_format": "timeseries", "queries": [{"query": "max:system.cpu.user{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query1"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 3, "y": 3, "width": 4, "height": 3}}, {"id": 6493770901230514, "definition": {"title": "Max RAM%", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"conditional_formats": [{"palette": "white_on_green", "value": 50, "comparator": "<"}, {"palette": "white_on_yellow", "value": 80, "comparator": "<"}, {"palette": "white_on_red", "value": 80, "comparator": ">"}], "response_format": "scalar", "queries": [{"query": "max:system.mem.total{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query2", "aggregator": "max"}, {"query": "max:system.mem.free{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query3", "aggregator": "max"}, {"query": "max:system.mem.cached{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query4", "aggregator": "max"}], "formulas": [{"formula": "(query2 - query3 - query4) / query2 * 100"}], "sort": {"count": 500, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}]}, "layout": {"x": 0, "y": 6, "width": 3, "height": 2}}, {"id": 9006817881874566, "definition": {"title": "Memory by Host", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1 - query2 - query3"}], "response_format": "timeseries", "queries": [{"query": "max:system.mem.total{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query1"}, {"query": "max:system.mem.free{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query2"}, {"query": "max:system.mem.cached{$customer,$role,$env,$site} by {host}", "data_source": "metrics", "name": "query3"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 3, "y": 6, "width": 4, "height": 3}}, {"id": 3995524931798670, "definition": {"title": "CPU Temperature", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "horizontal", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "response_format": "timeseries", "queries": [{"query": "max:custom.cpu.temperature{$customer,$site,$role,$env} by {host}", "data_source": "metrics", "name": "query1"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "markers": [{"value": "y = 95", "display_type": "error dashed"}]}, "layout": {"x": 7, "y": 6, "width": 5, "height": 2}}, {"id": 1275189994719920, "definition": {"title": "Top Percent Write Cycles Used", "title_size": "16", "title_align": "left", "type": "toplist", "requests": [{"response_format": "scalar", "queries": [{"name": "query1", "data_source": "metrics", "query": "max:system.disk.smart.percent_used{$customer,$site,$role,$env} by {host,device}", "aggregator": "avg"}], "formulas": [{"formula": "query1"}], "sort": {"count": 10, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}}]}, "layout": {"x": 0, "y": 8, "width": 3, "height": 3}}, {"id": 4063514050993461, "definition": {"title": "TCP Retransmits", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "retransmits"}], "response_format": "timeseries", "queries": [{"search": {"query": "client_$customer client_$role client_$env client_$site"}, "data_source": "network", "compute": {"metric": "network.retransmits", "interval": 300000, "aggregation": "sum"}, "name": "retransmits", "indexes": ["netflow-search-v2"], "group_by": []}], "style": {"palette": "dog_classic"}, "display_type": "bars"}], "yaxis": {"scale": "linear"}, "markers": [{"value": "y = 10000", "display_type": "error dashed"}]}, "layout": {"x": 7, "y": 8, "width": 5, "height": 2}}, {"id": 3217812126958949, "definition": {"title": "Network Volume (bytes)", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "volume_sent"}], "response_format": "timeseries", "queries": [{"search": {"query": "client_$customer client_$role client_$env client_$site"}, "data_source": "network", "compute": {"metric": "network.bytes_written", "interval": 300000, "aggregation": "sum"}, "name": "volume_sent", "indexes": ["netflow-search-v2"], "group_by": []}], "style": {"palette": "Purples"}, "display_type": "area"}], "yaxis": {"scale": "linear"}}, "layout": {"x": 3, "y": 9, "width": 4, "height": 2}}, {"id": 3225917767722346, "definition": {"title": "TCP Latency (ms)", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "rtt / rtt_count / 1000"}], "response_format": "timeseries", "queries": [{"search": {"query": "client_$customer client_$role client_$env client_$site"}, "data_source": "network", "compute": {"metric": "network.rtt", "interval": 300000, "aggregation": "sum"}, "name": "rtt", "indexes": ["netflow-search-v2"], "group_by": []}, {"search": {"query": "client_$customer client_$role client_$env client_$site"}, "data_source": "network", "compute": {"metric": "network.rtt_count", "interval": 300000, "aggregation": "sum"}, "name": "rtt_count", "indexes": ["netflow-search-v2"], "group_by": []}], "style": {"palette": "dog_classic"}, "display_type": "line"}], "yaxis": {"scale": "linear"}}, "layout": {"x": 7, "y": 10, "width": 5, "height": 2}}, {"id": 8473335695085340, "definition": {"title": "NVME Metrics", "title_size": "16", "title_align": "left", "type": "query_table", "requests": [{"queries": [{"data_source": "metrics", "name": "query1", "query": "avg:system.disk.smart.percent_used{$env, $customer, $site, $role} by {host}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query2", "query": "avg:system.disk.smart.avail_spare{$env, $customer, $site, $role} by {host}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query3", "query": "avg:system.disk.smart.unsafe_shutdowns{$env, $customer, $site, $role} by {host}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query4", "query": "avg:system.disk.smart.data_units_written{$env, $customer, $site, $role} by {host}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query5", "query": "avg:system.disk.smart.temperature{$env, $customer, $site, $role} by {host}", "aggregator": "avg"}], "response_format": "scalar", "sort": {"count": 500, "order_by": [{"type": "formula", "index": 0, "order": "desc"}]}, "formulas": [{"conditional_formats": [{"comparator": ">", "palette": "white_on_yellow", "value": 50}], "cell_display_mode": "number", "alias": "% used", "formula": "query1"}, {"conditional_formats": [{"comparator": "<", "palette": "white_on_red", "value": 50}], "cell_display_mode": "number", "alias": "Avail <PERSON>", "formula": "query2"}, {"cell_display_mode": "number", "alias": "Unsafe Shutdown", "formula": "query3"}, {"cell_display_mode": "number", "alias": "Written", "formula": "query4"}, {"alias": "Total Written", "formula": "512000 * query4"}, {"alias": "Temperature (C)", "conditional_formats": [{"comparator": ">", "palette": "white_on_red", "value": 40}], "formula": "query5 - 273"}]}], "has_search_bar": "auto"}, "layout": {"x": 0, "y": 11, "width": 6, "height": 4}}, {"id": 4173170226648360, "definition": {"title": "File System", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 4753770601532960, "definition": {"title": "sytem.fs.file_handles.allocated", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.fs.file_handles.allocated{$site, $env, $role, $customer} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 8536302511610242, "definition": {"title": "system.fs.file_handles.max", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.fs.file_handles.max{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 793047620052406, "definition": {"title": "system.fs.file_handles.allocated_unused", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.fs.file_handles.allocated_unused{$site, $env, $customer} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}, {"id": 8609639323108534, "definition": {"title": "system.fs.file_handles.used", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.fs.file_handles.used{$customer, $site, $env, $role} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 2, "width": 4, "height": 2}}, {"id": 1209006524603656, "definition": {"title": "process_open_fds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:process_open_fds{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 2, "width": 4, "height": 2}}, {"id": 7499860448829588, "definition": {"title": "system.fs.file_handles.in_use", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.fs.file_handles.in_use{$customer, $site, $env, $role} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 2, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 15, "width": 12, "height": 5}}, {"id": 8261657636052414, "definition": {"title": "Network", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 6083749261879186, "definition": {"title": "system.net.bytes_rcvd", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.net.bytes_rcvd{$customer, $role, $env, $site} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 779983681006952, "definition": {"title": "system.net.bytes_sent", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.net.bytes_sent{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 1089335342172012, "definition": {"title": "system.net.tcp.failed_retransmits", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:system.net.tcp.failed_retransmits{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 20, "width": 12, "height": 3}}, {"id": 4917380337372300, "definition": {"title": "Exit Nodes", "background_color": "vivid_purple", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 3542122306357024, "definition": {"title": "CPU utilization", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "max:gcp.gce.instance.cpu.utilization{tailscale-exit-node} by {name}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 2201790916464322, "definition": {"title": "Network Bytes Received ", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:gcp.gce.instance.network.received_bytes_count{tailscale-exit-node} by {name}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 5483891578483426, "definition": {"title": "Network Bytes Sent", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:gcp.gce.instance.network.sent_bytes_count{tailscale-exit-node} by {name}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 23, "width": 12, "height": 3}}], "template_variables": [{"name": "customer", "prefix": "customer", "available_values": [], "default": "*"}, {"name": "site", "prefix": "site", "available_values": [], "default": "*"}, {"name": "role", "prefix": "role", "available_values": ["edge-compute"], "default": "edge-compute"}, {"name": "env", "prefix": "env", "available_values": [], "default": "prod"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed"}