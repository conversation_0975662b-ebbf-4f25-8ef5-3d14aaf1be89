{"title": "Edge Transcoder", "description": "[[suggested_dashboards]]", "widgets": [{"id": 237553580856470, "definition": {"title": "ImageStreamer Instances by <PERSON>d", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "max:imagestreamer_running_threads{$customer,$site,$role,$env, kube_deployment:*-image} by {host,kube_deployment}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 3}}, {"id": 5555938322063042, "definition": {"title": "HlsStreamer Instances by Pod", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:hlsstreamer_running_threads{$customer,$site,$role,$env, kube_deployment:*-hls} by {host,kube_deployment}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 3}}, {"id": 2995950320795984, "definition": {"title": "ImageStreamer: Success after Fail by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:imagestreamer_successful_recovery.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 3}}, {"id": 2534594739961524, "definition": {"title": "Image: Frames per Minute", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "60 * query1"}], "queries": [{"query": "sum:imagestreamer_frames_processed.count{$customer,$site,$role,$env} by {camera_id}.as_rate().rollup(sum, 60)", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 3, "width": 4, "height": 3}}, {"id": 4163584655504704, "definition": {"title": "Image: % missing frames (by rate)", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"alias": "% missing frames", "formula": "(0.2 - frameRate) / 0.2 * 100"}], "queries": [{"query": "sum:imagestreamer_frames_processed.count{$customer,$site,$role,$env} by {camera_id}.as_rate().rollup(sum, 300)", "data_source": "metrics", "name": "frameRate"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"include_zero": false, "scale": "pow", "min": "0", "max": "100"}}, "layout": {"x": 4, "y": 3, "width": 4, "height": 3}}, {"id": 1179057322973934, "definition": {"title": "HLS: Segments per Minute", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "60 * query1"}], "queries": [{"query": "sum:hlsstreamer_segments_processed.count{$customer,$site,$role,$env} by {camera_id}.as_rate().rollup(sum, 60)", "data_source": "metrics", "name": "query1"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 3, "width": 4, "height": 3}}, {"id": 1835654007598344, "definition": {"title": "Imager: <PERSON><PERSON>rs by Type", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:imagestreamer_fatal_errors.count{$customer,$site,$role,$env} by {error_type}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 0, "y": 6, "width": 4, "height": 3}}, {"id": 2390712346534782, "definition": {"title": "Image: Fatal <PERSON>rs by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:imagestreamer_fatal_errors.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 4, "y": 6, "width": 4, "height": 3}}, {"id": 6908105769863100, "definition": {"title": "Image: Non-Fatal Errors by Type", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:imagestreamer_errors.count{$customer,$site,$role,$env} by {error_type}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 8, "y": 6, "width": 4, "height": 3}}, {"id": 6999713071940338, "definition": {"title": "HLS: Fatal Errors by Type", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:hlsstreamer_fatal_errors.count{$customer,$site,$role,$env} by {error_type}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 0, "y": 9, "width": 4, "height": 3}}, {"id": 5431802564577946, "definition": {"title": "HLS: <PERSON><PERSON>rs by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:hlsstreamer_fatal_errors.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 4, "y": 9, "width": 4, "height": 3}}, {"id": 3159975987557288, "definition": {"title": "HLS: Non-Fatal Errors by Type", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:hlsstreamer_errors.count{$customer,$site,$role,$env} by {error_type}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 8, "y": 9, "width": 4, "height": 3}}, {"id": 3459355411558082, "definition": {"title": "HLS: Non-Fatal Errors by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:hlsstreamer_errors.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 0, "y": 12, "width": 4, "height": 3}}, {"id": 4072292347611764, "definition": {"title": "HLS: Segments Missing Keyframes by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:hlsstreamer_files_missing_keyframe.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 4, "y": 12, "width": 4, "height": 3}}, {"id": 5727005967265686, "definition": {"title": "Image: Non-Fatal Errors by Camera ID", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:imagestreamer_errors.count{$customer,$site,$role,$env} by {camera_id}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 8, "y": 12, "width": 4, "height": 3}}, {"id": 6755522903674560, "definition": {"title": "Uploader: Image File Pending", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query9"}], "queries": [{"name": "query9", "data_source": "metrics", "query": "max:uploader_files_pending{$customer,$site,$role,$env,capture_mode:image} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 15, "width": 4, "height": 3}}, {"id": 6881068755316332, "definition": {"title": "Uploader: Images Uploaded per Minute", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "60 * query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:uploader_files_uploaded.count{$customer,$site,$role,$env,capture_mode:image} by {host}.as_rate().rollup(sum, 60)"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 15, "width": 4, "height": 3}}, {"id": 1717539048250654, "definition": {"title": "Uploader: HLS Segments Uploaded per Minute", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "60 * query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:uploader_files_uploaded.count{$customer,$site,$role,$env,capture_mode:hls} by {host}.as_rate().rollup(sum, 60)"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 15, "width": 4, "height": 3}}, {"id": 3730957033159362, "definition": {"title": "Uploader: p99 of Image Upload Duration Seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "p99:uploader_upload_file_duration_seconds{$customer,$site,$role,$env,capture_mode:image} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 18, "width": 4, "height": 3}}, {"id": 1218398822522952, "definition": {"title": "Uploader: HLS Segments Pending", "title_size": "16", "title_align": "left", "show_legend": false, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query9"}], "queries": [{"name": "query9", "data_source": "metrics", "query": "max:uploader_files_pending{$customer,$site,$role,$env,capture_mode:hls} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 18, "width": 4, "height": 3}}, {"id": 8519976852670520, "definition": {"title": "Uploader - HLS Segment Duration by Camera ID in Seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query2"}], "queries": [{"name": "query2", "data_source": "metrics", "query": "p99:uploader_hls_duration_seconds{$customer,$site,$role,$env} by {camera_id}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 18, "width": 4, "height": 3}}, {"id": 7635265277851886, "definition": {"title": "Uploader: Delay of Latest Uploaded HLS Segment in Seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "max:uploader_latest_file_upload_delay{$customer,$site,$role,$env,capture_mode:hls} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 21, "width": 4, "height": 3}}, {"id": 8582752137347982, "definition": {"title": "Uploader - p99 of HLS Segment Upload in Seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "p99:uploader_upload_file_duration_seconds{$customer,$site,$role,$env,capture_mode:hls} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 21, "width": 4, "height": 3}}, {"id": 4894453517860568, "definition": {"title": "Uploader - Errors", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:uploader_upload_file_errors.count{$customer,$site,$role,$env} by {host,capture_mode}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 8, "y": 21, "width": 4, "height": 3}}, {"id": 7767792012653246, "definition": {"title": "Uploader - Main Loop Errors", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:uploader_main_loop_exceptions.count{$customer,$site,$role,$env} by {host,capture_mode}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 0, "y": 24, "width": 4, "height": 3}}, {"id": 7628365548526232, "definition": {"title": "Uploader: Delay of Last Image File in Seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "max:uploader_latest_file_upload_delay{$customer,$site,$role,$env,capture_mode:image} by {host}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 24, "width": 4, "height": 3}}, {"id": 7498664006792390, "definition": {"legend_columns": ["avg", "min", "max", "value", "sum"], "legend_layout": "auto", "requests": [{"display_type": "line", "formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:system.disk.directory.files{env:$env.value, customer:$customer.value, name:/tmp/rtsp_transcoder/pending, site:$site.value} by {host}"}], "response_format": "timeseries", "style": {"line_type": "solid", "line_width": "normal", "order_by": "values", "palette": "dog_classic"}}], "show_legend": true, "title": "Transcoder pending file count", "title_align": "left", "title_size": "16", "type": "timeseries"}, "layout": {"height": 2, "width": 4, "x": 8, "y": 24}}, {"id": 285133966629678, "definition": {"title": "Python", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 891074884943666, "definition": {"title": "Process CPU seconds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:process_cpu_seconds.count{$customer,$site,$role,$env,$kube_app_name} by {kube_app_name,host,kube_deployment}.as_rate()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 3}}, {"id": 7236586134685208, "definition": {"title": "Process resident memory (bytes)", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:process_resident_memory_bytes{$customer,$site,$role,$env,$kube_app_name} by {kube_app_name,host,kube_deployment}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 3}}, {"id": 1014056380386144, "definition": {"title": "Python GC collections", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:python_gc_collections.count{$customer,$site,$role,$env,$kube_app_name} by {kube_app_name,host,kube_deployment}.as_rate()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 3}}]}, "layout": {"x": 0, "y": 27, "width": 12, "height": 4, "is_column_break": true}}, {"id": 7384621285732866, "definition": {"title": "Kubernetes", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 2659272926801224, "definition": {"title": "Number of Kubernetes container restarts", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "monotonic_diff(query1)"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:kubernetes.containers.restarts{$customer,$site,$role,$env,$kube_app_name} by {kube_app_name,host,kube_deployment}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 2}}, {"id": 8854098748581238, "definition": {"title": "container.io.write", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:container.io.write{$customer, $site, $role, $env, $kube_app_name} by {pod_name}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 0, "width": 4, "height": 2}}, {"id": 1218340518901940, "definition": {"title": "container.io.write.operations", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "avg:container.io.write.operations{$customer, $site, $role, $env, $kube_app_name} by {pod_name}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 0, "width": 4, "height": 2}}, {"id": 1355863590325172, "definition": {"title": "system.net.bytes_sent", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:system.net.bytes_sent{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 2, "width": 4, "height": 2}}, {"id": 2787700244303324, "definition": {"title": "container.pid.open_file", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:container.pid.open_files{$customer, $site, $role, $env, $kube_app_name} by {pod_name}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 2, "width": 4, "height": 2}}, {"id": 8469442579342576, "definition": {"title": "process_open_fds", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:process_open_fds{$customer, $env, $site, $role, $kube_app_name} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 2, "width": 4, "height": 2}}, {"id": 1363442090039820, "definition": {"title": "system.net.bytes_rcvd", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:system.net.bytes_rcvd{$customer, $site, $role, $env} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 4, "width": 4, "height": 2}}, {"id": 1594312303087574, "definition": {"title": "container.net.sent", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:container.net.sent{$customer, $site, $role, $env, $kube_app_name} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 4, "y": 4, "width": 4, "height": 2}}, {"id": 5874909715987022, "definition": {"title": "container.net.rcvd", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "time": {}, "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:container.net.rcvd{$customer, $site, $role, $env, $kube_app_name} by {kube_node}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 8, "y": 4, "width": 4, "height": 2}}]}, "layout": {"x": 0, "y": 31, "width": 12, "height": 7}}, {"id": 7390912145092290, "definition": {"title": "Latency and Drift", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 2726195405394374, "definition": {"title": "Transcoder - HLSStreamer Latency", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:hlsstreamer_latency_seconds{$customer, $site, $role, $env} by {camera_id}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"include_zero": true}, "markers": []}, "layout": {"x": 0, "y": 0, "width": 6, "height": 3}}, {"id": 6930448809187792, "definition": {"title": "Transcoder - HLSStreamer Accumulated Drift", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:hlsstreamer_end_time_to_actual_offset_seconds{$customer, $site, $role, $env} by {camera_id}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"include_zero": true}, "markers": []}, "layout": {"x": 6, "y": 0, "width": 6, "height": 3}}, {"id": 3357668547776414, "definition": {"title": "Transcoder - ImageStreamer Latency", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:imagestreamer_latency_seconds{$customer, $site, $role, $env} by {camera_id}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"include_zero": true}, "markers": []}, "layout": {"x": 0, "y": 3, "width": 6, "height": 3}}, {"id": 1938502495188880, "definition": {"title": "Transcoder - ImageStreamer Accumulated Drift", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "max:imagestreamer_drift_seconds{$customer, $site, $role, $env} by {camera_id}"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"include_zero": true}, "markers": []}, "layout": {"x": 6, "y": 3, "width": 6, "height": 3}}]}, "layout": {"x": 0, "y": 38, "width": 12, "height": 7}}, {"id": 2994134632701218, "definition": {"title": "GCP", "show_title": true, "type": "group", "layout_type": "ordered", "widgets": [{"id": 1232022836517062, "definition": {"title": "GCP Bucket RPS", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "query1"}], "queries": [{"name": "query1", "data_source": "metrics", "query": "sum:gcp.storage.api.request_count{*} by {bucket_name}.as_rate().rollup(sum, 60)"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 4, "height": 3}}]}, "layout": {"x": 0, "y": 45, "width": 12, "height": 4}}, {"id": 3865348845793394, "definition": {"title": "Upload Size GB", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "autosmooth(query1) / 1024"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:uploader_upload_file_size_mega_bytes{$customer, $site, $role, $env} by {customer}.as_count()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 0, "width": 6, "height": 4}}, {"id": 54129804920530, "definition": {"title": "Upload Rate mbps", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"formula": "autosmooth(query1) * 8"}], "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:uploader_upload_file_size_mega_bytes{$customer, $site, $role, $env} by {customer}.as_rate()"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 6, "y": 0, "width": 6, "height": 4}}, {"id": 8423008902293278, "definition": {"title": "Cameras not getting images or videos", "title_size": "16", "title_align": "left", "type": "query_table", "requests": [{"queries": [{"data_source": "metrics", "name": "query1", "query": "sum:imagestreamer_frames_processed.count{$customer, $site, $role, $env} by {camera_id}.as_count()", "aggregator": "last"}, {"data_source": "metrics", "name": "query2", "query": "sum:hlsstreamer_segments_processed.count{$customer, $site, $role, $env} by {camera_id}.as_count()", "aggregator": "last"}], "response_format": "scalar", "sort": {"count": 25, "order_by": [{"type": "formula", "index": 0, "order": "asc"}]}, "formulas": [{"cell_display_mode": "bar", "alias": "Images Captured", "formula": "query1"}, {"cell_display_mode": "bar", "alias": "Segments Captured", "formula": "query2"}]}], "has_search_bar": "auto"}, "layout": {"x": 0, "y": 4, "width": 12, "height": 10}}], "template_variables": [{"name": "customer", "prefix": "customer", "available_values": [], "default": "*"}, {"name": "site", "prefix": "site", "available_values": [], "default": "*"}, {"name": "role", "prefix": "role", "available_values": [], "default": "edge-compute"}, {"name": "env", "prefix": "env", "available_values": [], "default": "prod"}, {"name": "kube_app_name", "prefix": "kube_app_name", "available_values": ["edge-uploader", "rtsp-transcoder"], "default": "*"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed"}