# Creating ORG ID Stream Passwords
# TODO: Remove Secrets after deployments have migrated to the site specific stream passwords
resource "google_secret_manager_secret" "customer_stream_password_secrets" {
  for_each  = toset(var.org_ids)
  project   = var.project_id
  secret_id = "${each.value}_stream_password"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_binding" "edge_uploader_secrets_binding_secret_accessor_role" {
  for_each  = google_secret_manager_secret.customer_stream_password_secrets
  project   = var.project_id
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members = [
    "serviceAccount:${data.terraform_remote_state.data_platform.outputs.edge_customer_uploader_sa.email}"
  ]
}

resource "google_secret_manager_secret_iam_binding" "edge_uploader_secrets_binding_secret_viewer_role" {
  for_each  = google_secret_manager_secret.customer_stream_password_secrets
  project   = var.project_id
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.viewer"
  members = [
    "serviceAccount:${data.terraform_remote_state.data_platform.outputs.edge_customer_uploader_sa.email}"
  ]
}


resource "google_secret_manager_secret" "customer_site_stream_password_secrets" {
  for_each  = toset(var.site_ids)
  project   = var.project_id
  secret_id = "${each.value}_stream_password"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_binding" "edge_uploader_site_secrets_binding_secret_accessor_role" {
  for_each  = google_secret_manager_secret.customer_site_stream_password_secrets
  project   = var.project_id
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members = [
    "serviceAccount:${data.terraform_remote_state.data_platform.outputs.edge_customer_uploader_sa.email}"
  ]
}

resource "google_secret_manager_secret_iam_binding" "edge_uploader_site_secrets_binding_secret_viewer_role" {
  for_each  = google_secret_manager_secret.customer_site_stream_password_secrets
  project   = var.project_id
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.viewer"
  members = [
    "serviceAccount:${data.terraform_remote_state.data_platform.outputs.edge_customer_uploader_sa.email}"
  ]
}
