# Identity for the ArgoCD image updater
resource "google_service_account" "argocd_image_updater_sa" {
  account_id   = "${var.environment_prefix}-argocd-image-updater"
  project      = var.project_id
  display_name = "ArgoCD Image Updater Service Account"
}

# An account key that is used by ArgoCD image updater to authenticate from the edge Kubernetes cluster
# 
# SECURITY NOTE: Managing a Service Account key in Terraform results in the private key being stored
# in the Terraform state store in addition to the Google Secret Manager (see below). This presents a
# risk versus providing the private key as a write-only sensitive Terraform variable because it
# creates a second readable copy of the key which has its own security modeling. In other words,
# Apella users who have access to read Terraform state will now be able to obtain the private key
# even if we think we've restricted their access to the secret via Google Secret Manager.
#
# All that being said, the risk associated with the leaking of this particular key is pretty low.
# The only role which will be assigned to the ArgoCD image updater will be to a container registry
# reader role. If a malicious actor got access to our container registry, it wouldn't pose a threat
# to our sensitive data. So, we'll err on the side of the convenience of automation at the expense
# of some security controls.
resource "google_service_account_key" "argocd_image_updater_sa_key" {
  service_account_id = google_service_account.argocd_image_updater_sa.id
}

# Google Secret Manager secret which stores the base64 encoded key. The cluster_sa will has access
# to read this secret and injects it as a Kubernetes Docker pull secret to which the ArgoCD image
# updater has access.
resource "google_secret_manager_secret" "argocd_image_updater_sa_key" {
  project   = var.project_id
  secret_id = "argocd_image_updater_sa_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "argocd_image_updater_sa_key_version" {
  secret      = google_secret_manager_secret.argocd_image_updater_sa_key.id
  secret_data = google_service_account_key.argocd_image_updater_sa_key.private_key
}