resource "google_secret_manager_secret" "tailscale_oauth_client_secret" {
  project   = var.project_id
  secret_id = "tailscale_oauth_client_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret" "tailscale_oauth_client_id" {
  project   = var.project_id
  secret_id = "tailscale_oauth_client_id"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}
