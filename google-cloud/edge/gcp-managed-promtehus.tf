resource "google_service_account" "edge_metrics_sa" {
  account_id   = "edge-metrics-sa"
  project      = var.project_id
  display_name = "Edge Metrics SA"
}

resource "google_project_iam_member" "edge_metric_roles" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.edge_metrics_sa.email}"
}

resource "google_service_account_key" "edge_metrics_sa_key" {
  service_account_id = google_service_account.edge_metrics_sa.id
}

resource "google_secret_manager_secret" "edge_metrics_service_account_key_secret" {
  project   = var.project_id
  secret_id = "edge_metric_sa_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "edge_metrics_service_account_key_secret_version" {
  secret      = google_secret_manager_secret.edge_metrics_service_account_key_secret.id
  secret_data = base64decode(google_service_account_key.edge_metrics_sa_key.private_key)
}
