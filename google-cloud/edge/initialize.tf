terraform {
  backend "remote" {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      prefix = "tf-edge-"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    datadog = {
      source  = "DataDog/datadog"
      version = "~>3.19"
    }
  }
}

data "terraform_remote_state" "data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-${var.environment_prefix}"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}
