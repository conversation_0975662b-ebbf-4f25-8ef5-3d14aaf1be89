####################################################
# Global Variables
####################################################

variable "project_id" {
  description = "GCP Project ID"
}

variable "environment_prefix" {
  description = "The environment prefix to append to external variable names"
}

variable "region" {
  description = "The GCP Region to create Google resources inside of"
  default     = "us-central1-c"
}

####################################################
# Datadog
####################################################

variable "datadog_api_key" {
  description = "The datadog api key for Terra<PERSON> to use"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "The datadog app key for Terraform to use"
  type        = string
  sensitive   = true
}

variable "datadog_key_name_postfix" {
  description = "The prefix to use for the datadog key name"
  default     = "edge-cluster"
}

variable "exclude_host_files_pending" {
  description = "Exclude hosts from the files pending monitor query. Used to filter out known hosts that are degraded"
  default     = ""
}

####################################################
# Customers
####################################################

variable "org_ids" {
  description = "A list of the organization ids that we should generate secrets for i.e. houston_methodist"
  type        = list(string)
}

variable "site_ids" {
  description = "A list of the site ids that we should generate secrets for i.e. houston_methodist"
  type        = list(string)
}
