/***********************************************************************
Creating the Datadog api and app keys
***********************************************************************/

resource "datadog_api_key" "cluster_api_key" {
  name = "${var.environment_prefix}-${var.datadog_key_name_postfix}"
}

resource "datadog_application_key" "cluster_app_key" {
  name = "${var.environment_prefix}-${var.datadog_key_name_postfix}"
}

/***********************************************************************
Pushing the api and app key to GCP secrets for k8s to read from
***********************************************************************/

resource "google_secret_manager_secret" "datadog_api_key" {
  project   = var.project_id
  secret_id = "datadog_api_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "datadog_api_key_version" {
  secret = google_secret_manager_secret.datadog_api_key.id

  secret_data = datadog_api_key.cluster_api_key.key
}

resource "google_secret_manager_secret" "datadog_app_key" {
  project   = var.project_id
  secret_id = "datadog_app_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "datadog_app_key_version" {
  secret = google_secret_manager_secret.datadog_app_key.id

  secret_data = datadog_application_key.cluster_app_key.key
}
