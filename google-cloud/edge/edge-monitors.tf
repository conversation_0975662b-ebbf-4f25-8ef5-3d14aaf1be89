locals {
  datadog_tags = [
    "environment:${var.environment_prefix}", "project-id:${var.project_id}", "team:field-eng"
  ]
  slack_alert_channel_name_base = "@slack-team-field-eng-alert"
  critical_alert_team_name_base = "@webhook-incident_io"

  exclude_host_files_pending = var.exclude_host_files_pending != "" ? " , !host:${var.exclude_host_files_pending}*" : ""
}


resource "datadog_monitor" "imagestreamer_frame_capture_monitor" {
  count = var.environment_prefix == "prod" || var.environment_prefix == "dev" ? 1 : 0

  name    = "Camera image capture rate too low for camera {{camera_id.name}} (${var.environment_prefix})"
  message = var.environment_prefix == "prod" ? local.critical_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "max(last_15m):sum:imagestreamer_frames_processed.count{env:${var.environment_prefix}} by {camera_id}.as_rate().rollup(sum, 60) * 60 <= 11"
  type    = "query alert"

  monitor_thresholds {
    critical = 11
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 10
  evaluation_delay    = 300

  renotify_statuses = ["alert", "no data"]
  priority          = 3

  tags = local.datadog_tags
}


resource "datadog_monitor" "nuc_up_monitor" {
  count = var.environment_prefix == "staging" ? 1 : 0

  name    = "NUC {{host}} not available (${var.environment_prefix})"
  message = local.critical_alert_team_name_base
  query   = "avg(last_5m):avg:system.cpu.user{env:${var.environment_prefix}} by {host}.rollup(avg, 300) <= 0"
  type    = "query alert"

  monitor_thresholds {
    critical = 0
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 10
  evaluation_delay    = 300

  renotify_statuses = ["alert", "no data"]
  priority          = 2

  tags = local.datadog_tags
}


resource "datadog_monitor" "uploader_files_pending_monitor" {
  count = var.environment_prefix == "prod" || var.environment_prefix == "dev" ? 1 : 0

  name    = "Too many files pending to be uploaded for filetype {{capture_mode.name}} on host {{host.name}} (${var.environment_prefix})"
  message = var.environment_prefix == "prod" ? local.critical_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "min(last_5m):avg:uploader_files_pending{env:${var.environment_prefix}${local.exclude_host_files_pending}} by {host,capture_mode} >= 50"
  type    = "query alert"

  monitor_thresholds {
    critical = 50
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 10
  evaluation_delay    = 15

  renotify_statuses = ["alert", "no data"]
  priority          = 3

  tags = local.datadog_tags
}

resource "datadog_monitor" "files_pending_monitor" {
  count = var.environment_prefix == "prod" || var.environment_prefix == "dev" ? 1 : 0

  name    = "Too many files in /tmp/rtsp_transcoder/pending on host {{host.name}} (${var.environment_prefix})"
  message = var.environment_prefix == "prod" ? local.critical_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "min(last_5m):system.disk.directory.files{name:/tmp/rtsp_transcoder/pending, env:${var.environment_prefix}${local.exclude_host_files_pending}} by {host} >= 150"
  type    = "query alert"

  monitor_thresholds {
    critical = 150
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 10
  evaluation_delay    = 15

  renotify_statuses = ["alert", "no data"]
  priority          = 3

  tags = local.datadog_tags
}


resource "datadog_monitor" "camera_drift" {
  count   = var.environment_prefix == "prod" || var.environment_prefix == "dev" ? 1 : 0
  name    = "Camera is drifting {{camera_id.name}} (${var.environment_prefix})"
  message = "${local.slack_alert_channel_name_base}-${var.environment_prefix} Check rtsp-transcoder."
  query   = "avg(last_5m):1000 * avg:hlsstreamer_end_time_to_actual_offset_seconds{env:${var.environment_prefix}} by {camera_id} >= 9750"
  type    = "query alert"

  monitor_thresholds {
    warning  = 4750
    critical = 9750
  }

  notify_audit         = false
  require_full_window  = false
  notify_no_data       = false
  renotify_occurrences = 3
  renotify_interval    = 30
  include_tags         = true
  new_group_delay      = 60
  no_data_timeframe    = 10
  evaluation_delay     = 5

  priority = 5

  tags = local.datadog_tags

}

resource "datadog_monitor" "keyframe_missing_monitor" {
  count = var.environment_prefix == "prod" || var.environment_prefix == "dev" ? 1 : 0

  name    = "Segment that doesn't start with keyframe detected for camera {{camera_id.name}} (${var.environment_prefix})"
  message = "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "sum(last_5m):sum:hlsstreamer_files_missing_keyframe.count{${var.environment_prefix}} by {camera_id}.as_count() > 0"
  type    = "query alert"

  monitor_thresholds {
    critical = 0
  }

  notify_audit         = false
  require_full_window  = false
  notify_no_data       = true
  renotify_occurrences = 3
  renotify_interval    = 30
  include_tags         = true
  new_group_delay      = 60
  no_data_timeframe    = 10
  evaluation_delay     = 5

  renotify_statuses = ["alert", "no data"]
  priority          = 3

  tags = local.datadog_tags
}

resource "datadog_monitor" "disk_above_50_percent_utilization" {
  name    = "Disk utilization above 50% detected for {{host}} (${var.environment_prefix})"
  message = "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "avg(last_5m):avg:system.disk.smart.percent_used{env:${var.environment_prefix}} by {host} >= 50"
  type    = "query alert"

  monitor_thresholds {
    critical = 50
  }

  notify_audit         = false
  require_full_window  = false
  notify_no_data       = true
  renotify_occurrences = 3
  renotify_interval    = 30
  include_tags         = true
  new_group_delay      = 60
  no_data_timeframe    = 10
  evaluation_delay     = 300

  renotify_statuses = ["alert", "no data"]
  priority          = 4

  tags = local.datadog_tags
}

resource "datadog_monitor" "disk_below_90_percent_spare_capacity" {
  name    = "Disk spare capacity below 90% detected for {{host}} (${var.environment_prefix})"
  message = "${local.slack_alert_channel_name_base}-${var.environment_prefix}"
  query   = "avg(last_5m):avg:system.disk.smart.avail_spare{env:${var.environment_prefix}} by {host} < 90"
  type    = "query alert"

  monitor_thresholds {
    critical = 90
  }

  notify_audit         = false
  require_full_window  = false
  notify_no_data       = true
  renotify_occurrences = 3
  renotify_interval    = 30
  include_tags         = true
  new_group_delay      = 60
  no_data_timeframe    = 10
  evaluation_delay     = 300

  renotify_statuses = ["alert", "no data"]
  priority          = 3

  tags = local.datadog_tags
}

resource "datadog_monitor" "exit_node_cpu" {
  count = var.environment_prefix == "prod" ? 1 : 0

  name    = "Exit Node CPU {{name}} (${var.environment_prefix})"
  message = local.critical_alert_team_name_base
  query   = "avg(last_5m):avg:gcp.gce.instance.cpu.utilization{tailscale-exit-node} by {name} >= 75"
  type    = "query alert"

  monitor_thresholds {
    warning  = 70
    critical = 75
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 10
  evaluation_delay    = 300

  renotify_statuses = ["alert", "no data"]
  priority          = 2

  tags = local.datadog_tags
}
