variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "db_tier" {
  description = "The tier for the master instance."
  type        = string
  default     = "db-f1-micro"
}

variable "db_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_15"
}

variable "db_availability_type" {
  description = "The availability type for the master instance.This is only used to set up high availability for the PostgreSQL instance. Can be either `ZONAL` or `REGIONAL`."
  type        = string
  default     = "REGIONAL"
}

variable "db_backup_location" {
  description = "The region to which backups for this db will be stored. By default stored in the US multiregion"
  type        = string
  default     = "us"
}


#TODO: consider making this two configs, network project id / network name,
#seems like a smell that we query so many workspace outputs
variable "network_terraform_workspace" {
  type        = string
  description = "terraform workspace that houses network configuration, used for data.terraform_remote_state.network.outputs.network.id"
}

variable "security_terraform_workspace" {
  type        = string
  description = "terraform workspace that houses security configuration, used for data.terraform_remote_state.security.outputs.bastion-host-sa"
}

variable "project_factory_terraform_workspace" {
  type        = string
  description = "terraform workspace that houses project factory configuration, used for data.terraform_remote_state.project_factory.outputs.data-platform-project.tf_sa_email"
}

variable "env" {
  type = string
}

variable "labels" {
  description = "The key/value labels "
  type        = map(string)
  default     = {}
}

variable "keda_sa_email" {
  description = "The keda service account"
  type        = string
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "cloud_sql_version_password_date" {
  description = "version of generated password, change this value to rotate the password"
  type        = string
  default     = "2024-04-29T16:00:25+00:00"
}

variable "cloud_sql_disk_size" {
  description = "The disk size (GB) for the master instance."
  type        = number
  default     = 10
}

variable "cloud_sql_replica_active" {
  description = "Whether to activate the db replica"
  type        = bool
  default     = false
}

variable "cloud_sql_replica_tier" {
  description = "The tier for the replica instance. Must be same or larger than the master instance"
  type        = string
  default     = "db-f1-micro"
}

variable "cloud_sql_authorized_networks" {
  description = "A list of authorized networks that are allowed to access cloudsql"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "data_warehouse_version_password_date" {
  description = "version of generated password, change this value to rotate the password"
  type        = string
  default     = "2024-12-20T17:04:19+00:00"
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "enable_db_alerts" {
  description = "Enable alerts for database"
  type        = bool
  default     = false
}

/******************************************
Block Release Data Variables
*****************************************/

variable "block_release_web_api_url" {
  description = "Url of web api used for block release"
  type        = string
}

variable "web_api_service_account_id" {
  description = "Service account id for web api"
  type        = string
}

variable "block_release_iam_members" {
  description = "The list of IAM members to grant permissions on the bucket."
  type = list(object({
    role   = string
    member = string
  }))
  default = []
}

variable "block_release_data_lifecycle_rules" {
  description = "The bucket's Lifecycle Rules configuration."
  type = list(object({
    action    = any
    condition = any
  }))
  default = []
}

variable "block_release_message_retention_duration" {
  type    = string
  default = "604800s"
}

variable "block_release_retain_acked_messages" {
  type    = bool
  default = false
}

variable "block_release_ack_deadline_seconds" {
  type    = number
  default = 60
}

variable "block_release_max_delivery_attempts" {
  type    = number
  default = 5
}

variable "block_release_minimum_backoff" {
  type    = string
  default = "10s"
}

variable "block_release_maximum_backoff" {
  type    = string
  default = "600s"
}

variable "block_release_enable_message_ordering" {
  type    = bool
  default = false
}

variable "generator_enabled" {
  type    = bool
  default = false
}
