locals {
  redox_pubsub_name       = "${var.env}-redox-events"
  generator_service_count = var.generator_enabled ? 1 : 0
}

data "google_pubsub_topic" "redox_events" {
  project = local.web_api_project_id
  name    = local.redox_pubsub_name
}

module "generator_service" {
  count          = local.generator_service_count
  source         = "./modules/service"
  service_name   = "ehr-data-generator"
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  publishes_to_topics = {
    redox_events = data.google_pubsub_topic.redox_events.id
  }
  labels = var.labels
}
