locals {
  pubsub_primary_agent = "serviceAccount:service-${data.google_project.ehr.number}@gcp-sa-pubsub.iam.gserviceaccount.com"

  org_id_dirs = [
    "hmh", "tgh", "nyu", "hackensack_meridian"
  ]

  org_id_mapping = {
    "hmh" = "houston_methodist"
    "tgh" = "tampa_general"
  }

  path_prefixes = {
    for pair in setproduct(local.org_id_dirs, local.upload_types) :
    "${pair[0]}/uploads/${pair[1]}" => {
      org_id_dir  = pair[0]
      upload_type = pair[1]
    }
  }
}

/******************************************
GCS Bucket for Block Release Data
*****************************************/

module "customer_block_release_data_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.env}-customer-block-release-data"
  project_id                  = data.google_project.ehr.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.env
  iam_members                 = var.block_release_iam_members
  labels                      = var.labels
  lifecycle_rules             = var.block_release_data_lifecycle_rules

  cors = {
    origin          = []
    method          = []
    response_header = ["*"]
    max_age_seconds = 3000
  }
}

# Allow access to Bucket to download block release data
resource "google_project_iam_member" "api_server_block_release_bucket_roles" {
  for_each = toset([
    "roles/storage.objectViewer"
  ])
  project = data.google_project.ehr.project_id
  role    = each.value
  member  = "serviceAccount:api-server@${var.web_api_service_account_id}.iam.gserviceaccount.com"
}


resource "google_pubsub_topic" "block_release_data" {
  name   = "block-release-data"
  labels = var.labels
}

resource "google_storage_notification" "block_release_data_notification" {
  for_each = local.org_id_mapping

  bucket         = module.customer_block_release_data_bucket.name
  payload_format = "JSON_API_V1"
  topic          = google_pubsub_topic.block_release_data.id
  event_types    = ["OBJECT_FINALIZE"]

  object_name_prefix = "${each.key}/block_releases"
  custom_attributes = {
    orgId = each.value
  }

  depends_on = [google_pubsub_topic_iam_binding.binding]
}

// ****************************************
// Make clients uploads topic.
// ****************************************
resource "google_pubsub_topic" "client_uploads" {
  name   = "client-uploads"
  labels = var.labels
}

resource "google_storage_notification" "client_uploads_notification" {
  for_each = local.path_prefixes

  bucket         = module.customer_block_release_data_bucket.name
  payload_format = "JSON_API_V1"
  topic          = google_pubsub_topic.client_uploads.id
  event_types    = ["OBJECT_FINALIZE"]

  object_name_prefix = "${each.key}/"
  custom_attributes = {
    orgId      = lookup(local.org_id_mapping, each.value.org_id_dir, each.value.org_id_dir)
    uploadType = each.value.upload_type
  }

  depends_on = [google_pubsub_topic_iam_binding.client_uploads_gcs_publisher_binding]
}


/******************************************
 Service account for uploading to buckets
******************************************/

module "block_release_data_sftp_service" {
  source              = "./modules/service"
  service_name        = "block-release-sftp"
  k8s_namespace       = "block-release-data"
  k8s_service_account = "block-release-data-block-release-sftp"
  gcp_project_id      = data.google_project.ehr.project_id
  gke_project_id      = local.gke_project_id
  iam_roles = [
    "roles/secretmanager.secretAccessor"
  ]

  labels = var.labels
}


resource "google_storage_bucket_iam_member" "block_release_data_iam_member" {
  bucket = module.customer_block_release_data_bucket.name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${module.block_release_data_sftp_service.google_service_account.email}"
  depends_on = [
    module.customer_block_release_data_bucket,
    module.block_release_data_sftp_service
  ]
}

# secrets
resource "google_secret_manager_secret" "sftp-secret" {
  secret_id = "block-data-sftp-users"

  # We should consider adding a pubsub topic to trigger a restart
  # of the service when a secret is updated
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
    }
  }
}

resource "google_secret_manager_secret" "sftp-host-ed25519-key" {
  secret_id = "block-data-sftp-ssh-host-ed25519-key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
    }
  }
}

resource "google_secret_manager_secret" "sftp-host-rsa-key" {
  secret_id = "block-data-sftp-ssh-host-rsa-key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_binding" "binding" {
  project   = google_secret_manager_secret.sftp-secret.project
  secret_id = google_secret_manager_secret.sftp-secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members = [
    "serviceAccount:${module.block_release_data_sftp_service.google_service_account.email}"
  ]
  depends_on = [
    google_secret_manager_secret.sftp-secret
  ]
}

resource "google_secret_manager_secret_iam_binding" "ed25519_binding" {
  project   = google_secret_manager_secret.sftp-host-ed25519-key.project
  secret_id = google_secret_manager_secret.sftp-host-ed25519-key.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members = [
    "serviceAccount:${module.block_release_data_sftp_service.google_service_account.email}"
  ]
  depends_on = [
    google_secret_manager_secret.sftp-host-ed25519-key
  ]
}

resource "google_secret_manager_secret_iam_binding" "rsa_binding" {
  project   = google_secret_manager_secret.sftp-host-rsa-key.project
  secret_id = google_secret_manager_secret.sftp-host-rsa-key.secret_id
  role      = "roles/secretmanager.secretAccessor"
  members = [
    "serviceAccount:${module.block_release_data_sftp_service.google_service_account.email}"
  ]
  depends_on = [
    google_secret_manager_secret.sftp-host-rsa-key
  ]
}

// Enable notifications by giving the correct IAM permission to the unique service account.

data "google_storage_project_service_account" "gcs_account" {
}

resource "google_pubsub_topic_iam_binding" "binding" {
  topic   = google_pubsub_topic.block_release_data.id
  role    = "roles/pubsub.publisher"
  members = ["serviceAccount:${data.google_storage_project_service_account.gcs_account.email_address}"]
}

resource "google_pubsub_topic_iam_binding" "client_uploads_gcs_publisher_binding" {
  topic   = google_pubsub_topic.client_uploads.id
  role    = "roles/pubsub.publisher"
  members = ["serviceAccount:${data.google_storage_project_service_account.gcs_account.email_address}"]
}

// End enabling notifications


/*
  Push subscription for block release data & dead lettering
*/

resource "google_pubsub_topic" "dead_letter" {
  name = "${google_pubsub_topic.block_release_data.name}-dead-letter-topic"
}

resource "google_pubsub_subscription" "block_release_dead_letter_subscription" {
  name                       = "${google_pubsub_topic.block_release_data.name}-dead-letter-subscription"
  topic                      = google_pubsub_topic.dead_letter.id
  message_retention_duration = var.block_release_message_retention_duration
  retain_acked_messages      = var.block_release_retain_acked_messages
  ack_deadline_seconds       = var.block_release_ack_deadline_seconds
  expiration_policy {
    ttl = "" // never expire this subscription
  }
}

resource "google_pubsub_subscription" "block_release_push_subscription" {
  name    = "${google_pubsub_topic.block_release_data.name}-subscription"
  topic   = google_pubsub_topic.block_release_data.name
  project = data.google_project.ehr.project_id

  push_config {
    push_endpoint = "${var.block_release_web_api_url}/v1/blocks/releases"
    oidc_token {
      service_account_email = google_service_account.block_release_subscriber_sa.email
      audience              = var.block_release_web_api_url
    }
  }

  expiration_policy {
    ttl = "" // never expire this subscription
  }

  ack_deadline_seconds = var.block_release_ack_deadline_seconds

  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter.id
    max_delivery_attempts = var.block_release_max_delivery_attempts
  }

  retry_policy {
    minimum_backoff = var.block_release_minimum_backoff
    maximum_backoff = var.block_release_maximum_backoff
  }

  # messages do not need to be ordered
  enable_message_ordering = var.block_release_enable_message_ordering

  retain_acked_messages = var.block_release_retain_acked_messages
}

resource "google_pubsub_subscription" "block_release_client_uploads_push_subscription" {
  name    = "${google_pubsub_topic.block_release_data.name}-client-uploads-push-subscription"
  topic   = google_pubsub_topic.client_uploads.name
  project = data.google_project.ehr.project_id

  push_config {
    push_endpoint = "${var.block_release_web_api_url}/v1/blocks/releases"
    oidc_token {
      service_account_email = google_service_account.block_release_subscriber_sa.email
      audience              = var.block_release_web_api_url
    }
  }

  expiration_policy {
    ttl = "" // never expire this subscription
  }

  ack_deadline_seconds = var.block_release_ack_deadline_seconds

  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter.id
    max_delivery_attempts = var.block_release_max_delivery_attempts
  }

  retry_policy {
    minimum_backoff = var.block_release_minimum_backoff
    maximum_backoff = var.block_release_maximum_backoff
  }

  # messages do not need to be ordered
  enable_message_ordering = var.block_release_enable_message_ordering

  retain_acked_messages = var.block_release_retain_acked_messages

  filter = <<-EOF
    attributes.uploadType = "block_releases"
  EOF
}

resource "google_service_account" "block_release_subscriber_sa" {
  account_id   = "block-release-service-account"
  display_name = "Service account to block-release-data topic and assoc. deadletter"
}

resource "google_pubsub_subscription_iam_member" "block_release_subscriber_sa_permissions" {
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.block_release_push_subscription.id
  member       = "serviceAccount:${google_service_account.block_release_subscriber_sa.email}"
}

resource "google_pubsub_subscription_iam_member" "block_release_primary_pub_sub_sa_permissions" {
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.block_release_push_subscription.id
  member       = local.pubsub_primary_agent
}

resource "google_pubsub_topic_iam_member" "block_release_publisher_sa_permissions" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.dead_letter.id
  member = "serviceAccount:${google_service_account.block_release_subscriber_sa.email}"
}

resource "google_pubsub_topic_iam_member" "block_release_primary_pub_sub_sa_permissions" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.dead_letter.id
  member = local.pubsub_primary_agent
}

resource "google_pubsub_subscription_iam_member" "block_release_client_uploads_subscriber_sa" {
  subscription = google_pubsub_subscription.block_release_client_uploads_push_subscription.id
  role         = "roles/pubsub.subscriber"
  member       = "serviceAccount:${google_service_account.block_release_subscriber_sa.email}"
}

resource "google_pubsub_subscription_iam_member" "block_release_client_uploads_primary_agent" {
  subscription = google_pubsub_subscription.block_release_client_uploads_push_subscription.id
  role         = "roles/pubsub.subscriber"
  member       = local.pubsub_primary_agent
}
