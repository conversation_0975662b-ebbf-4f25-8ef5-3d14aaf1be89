locals {
  k8s_namespace       = var.k8s_namespace == null ? var.service_name : var.k8s_namespace
  k8s_service_account = var.k8s_service_account == null ? google_service_account.this.account_id : var.k8s_service_account
  k8s_sa_member       = "serviceAccount:${data.google_project.gke.project_id}.svc.id.goog[${local.k8s_namespace}/${local.k8s_service_account}]"
}

data "google_project" "gke" {
  project_id = var.gke_project_id
}

data "google_project" "this" {
  project_id = var.gcp_project_id
}

resource "google_service_account" "this" {
  account_id   = var.service_name
  display_name = "${var.service_name} service account"
}

resource "google_project_iam_member" "additional_roles" {
  for_each = var.iam_roles
  project  = data.google_project.this.project_id
  role     = each.value
  member   = google_service_account.this.member

}

# Allow the kubernetes service account to impersonate the gcp service account with iam permissions
resource "google_service_account_iam_member" "this" {
  service_account_id = google_service_account.this.name

  role   = "roles/iam.workloadIdentityUser"
  member = local.k8s_sa_member

}

resource "google_pubsub_topic_iam_member" "subscribes_to_topics" {
  for_each = var.subscribes_to_topics
  role     = "roles/pubsub.subscriber"
  topic    = each.value
  member   = google_service_account.this.member
}

resource "google_pubsub_topic_iam_member" "publishes_to_topics" {
  for_each = var.publishes_to_topics
  role     = "roles/pubsub.publisher"
  topic    = each.value
  member   = google_service_account.this.member

}

resource "google_pubsub_subscription_iam_member" "subscribes_to_subscriptions" {
  for_each     = var.subscribes_to_subscriptions
  role         = "roles/pubsub.subscriber"
  subscription = each.value
  member       = google_service_account.this.member
}

### This may not be needed (though its present in other terraform)
# resource "google_project_iam_member" "subscriber" {
#   count = length(var.subscribes_to_topics) > 0 ? 1 : 0

#   project = data.google_project.this.project_id
#   role = "roles/pubsub.subscriber"
#   member = google_service_account.this.member
# }

### Only subscriber role seems to be added in previous modules, but you'd expect both needed if any
# resource "google_project_iam_member" "publisher" {
#   count = length(var.publishes_to_topics) > 0 ? 1 : 0

#   project = data.google_project.this.project_id
#   role = "roles/pubsub.publisher"
#   member = google_service_account.this.member
# }
