variable "iam_roles" {
  description = "iam roles to add to the service's service account"
  type        = set(string)
  default     = []
}

variable "k8s_namespace" {
  description = "k8s namespace for service"
  type        = string
  default     = null
  nullable    = true
}

variable "k8s_service_account" {
  description = "k8s service account name used by service"
  type        = string
  default     = null
  nullable    = true
}

variable "gke_project_id" {
  description = "gcp project for kubernetes cluster"
  type        = string
  nullable    = false
}

variable "gcp_project_id" {
  description = "gcp project for service"
  type        = string
  nullable    = false
}

variable "service_name" {
  description = "name of service"
  type        = string
  nullable    = false
}
variable "publishes_to_topics" {
  description = "topics the service can publish to by name"
  type        = map(string)
  default     = {}
}

variable "subscribes_to_topics" {
  description = "topics the service can subscribe to by name"
  type        = map(string)
  default     = {}
}

variable "subscribes_to_subscriptions" {
  description = "subscriptions the service can subscribe to by name"
  type        = map(string)
  default     = {}
}

variable "labels" {
  description = "The key/value labels "
  type        = map(string)
  default     = {}
}
