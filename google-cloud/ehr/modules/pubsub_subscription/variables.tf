variable "name" {
  description = "name of subscription"
  type        = string
  nullable    = false
}

variable "deadletter_name" {
  description = "name of deadletter resources"
  type        = string
  default     = "errors"
}

variable "deadletter_enabled" {
  description = "whether to build deadlettering topics/subscriptions/policies"
  type        = bool
  default     = true
}

variable "topic_id" {
  description = "topic id"
  type        = string
  nullable    = false
}

variable "labels" {
  description = "The key/value labels "
  type        = map(string)
  default     = {}
}

variable "message_retention_duration" {
  description = "message_retention_duration of both the subscription and deadletter"
  type        = string
  default     = "604800s"

}

variable "retain_acked_messages" {
  description = "Whether to retain acked messages"
  type        = bool
  default     = true
}

variable "retry_minimum_backoff" {
  type    = string
  default = "10s"
}

variable "retry_maximum_backoff" {
  type    = string
  default = "600s"
}

variable "enable_message_ordering" {
  type    = bool
  default = false
}

variable "max_delivery_attempts" {
  type    = number
  default = 5
}

variable "ack_deadline_seconds" {
  type    = number
  default = 90

}
variable "google_project_id" {
  type = string
}

variable "filter" {
  description = "Optional filter expression for Pub/Sub subscription."
  type        = string
  default     = null
}
