locals {
  pubsub_primary_agent                    = "serviceAccount:service-${data.google_project.this.number}@gcp-sa-pubsub.iam.gserviceaccount.com"
  dead_letter_topic_and_subscription_name = "${var.name}-${var.deadletter_name}"


  dead_letter_policies = var.deadletter_enabled ? [
    {
      dead_letter_topic     = google_pubsub_topic.dead_letter[0].id
      max_delivery_attempts = var.max_delivery_attempts
    }
  ] : []
}


data "google_project" "this" {
  project_id = var.google_project_id
}

resource "google_pubsub_topic" "dead_letter" {
  count  = var.deadletter_enabled ? 1 : 0
  name   = local.dead_letter_topic_and_subscription_name
  labels = var.labels
}

resource "google_pubsub_subscription" "this" {
  name                       = var.name
  topic                      = var.topic_id
  labels                     = var.labels
  message_retention_duration = var.message_retention_duration
  retain_acked_messages      = var.retain_acked_messages
  expiration_policy {
    ttl = "" // never expire this subscription
  }

  filter = var.filter

  ack_deadline_seconds = var.ack_deadline_seconds

  retry_policy {
    minimum_backoff = var.retry_minimum_backoff
    maximum_backoff = var.retry_maximum_backoff
  }

  enable_message_ordering = false

  dynamic "dead_letter_policy" {
    for_each = local.dead_letter_policies
    iterator = config
    content {
      dead_letter_topic     = config.value["dead_letter_topic"]
      max_delivery_attempts = config.value["max_delivery_attempts"]
    }
  }
}


# In addition to the retries built in above, we also need a way to pull
# the "dead" messages from the dead letter queue.  So we create a pull
# subscription here.
resource "google_pubsub_subscription" "dead_letter_subscription_for_pulling" {
  count = var.deadletter_enabled ? 1 : 0

  name                       = local.dead_letter_topic_and_subscription_name
  topic                      = google_pubsub_topic.dead_letter[0].id
  labels                     = var.labels
  message_retention_duration = var.message_retention_duration
  retain_acked_messages      = false
  ack_deadline_seconds       = var.ack_deadline_seconds
  expiration_policy {
    ttl = "" // never expire this subscription
  }
}

//For dead lettering to work the pubsub agent needs to be able to subscribe to the subscription, and publish to the dead leter topic
resource "google_pubsub_subscription_iam_member" "pubsub_service_ehr_events_subscriber" {
  count        = var.deadletter_enabled ? 1 : 0
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.this.id
  member       = local.pubsub_primary_agent
}

resource "google_pubsub_topic_iam_member" "pubsub_service_ehr_ingestion_errors_publisher" {
  count  = var.deadletter_enabled ? 1 : 0
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.dead_letter[0].id
  member = local.pubsub_primary_agent
}
