locals {
  # Replace underscores with dashes in the upload types to maintain convention for subscription names.
  upload_types_mapped = { for upload_type in local.upload_types : replace(upload_type, "_", "-") => upload_type }
}

module "report_processor_service" {
  source         = "./modules/service"
  service_name   = "report-processor"
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  iam_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/storage.objectViewer"
  ]
  subscribes_to_subscriptions = {
    case_enricher_events = module.report_processor_subscription.subscription_id
  }
  labels = var.labels
}

# Legacy
module "report_processor_subscription" {
  source            = "./modules/pubsub_subscription"
  name              = "report-processor"
  google_project_id = data.google_project.ehr.id
  topic_id          = google_pubsub_topic.block_release_data.name
}

module "client_upload_subscriptions" {
  for_each = local.upload_types_mapped

  source            = "./modules/pubsub_subscription"
  name              = each.key
  google_project_id = data.google_project.ehr.id
  topic_id          = google_pubsub_topic.client_uploads.id

  # The `value` refers to the value in the `upload_types_mapped` map, which is the
  # upload type with underscores preserved.
  filter = <<-EOF
     attributes.uploadType = "${each.value}"
  EOF
}
