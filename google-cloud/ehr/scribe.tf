locals {
  scribe_service_name      = "ehr-scribe"
  scribe_subscription_name = "scribe"
  iam_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser"
  ]
}

module "ingestor_published_ehr_events_subscription" {
  source             = "./modules/pubsub_subscription"
  name               = local.scribe_subscription_name
  google_project_id  = data.google_project.ehr.id
  topic_id           = google_pubsub_topic.ingestor_published_ehr_events.id
  labels             = var.labels
  deadletter_enabled = false
}

module "scribe_service" {
  source         = "./modules/service"
  service_name   = local.scribe_service_name
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  iam_roles      = local.iam_roles
  subscribes_to_subscriptions = {
    ingestor_published_ehr_events = module.ingestor_published_ehr_events_subscription.subscription_id
  }

  labels = var.labels
}

module "scribe_backfill_service" {
  source         = "./modules/service"
  service_name   = "${local.scribe_service_name}-backfill"
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  iam_roles      = local.iam_roles
  k8s_namespace  = "ehr-scribe"
  subscribes_to_subscriptions = {
    ingestor_published_ehr_events = module.ingestor_published_ehr_events_backfill_subscription.subscription_id
  }

  labels = var.labels
}

module "keda_sa_permissions" {
  source               = "./modules/keda_sa_permissions"
  project_id           = data.google_project.ehr.project_id
  keda_service_account = var.keda_sa_email
}

module "ingestor_published_ehr_events_backfill_subscription" {
  source             = "./modules/pubsub_subscription"
  name               = "${local.scribe_subscription_name}-backfill"
  google_project_id  = data.google_project.ehr.id
  topic_id           = google_pubsub_topic.ingestor_published_ehr_events_backfill.id
  labels             = var.labels
  deadletter_enabled = false
}
