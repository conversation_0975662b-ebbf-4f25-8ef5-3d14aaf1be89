env                                 = "staging"
network_terraform_workspace         = "gcp-network-nonprod"
security_terraform_workspace        = "gcp-security-nonprod"
project_factory_terraform_workspace = "gcp-project-factory-nonprod"
block_release_web_api_url           = "https://api.staging.apella.io"
web_api_service_account_id          = "staging-web-api-3efef9"
keda_sa_email                       = "<EMAIL>"
cloud_sql_replica_active            = true
