locals {
  vpc_network_id        = data.terraform_remote_state.network.outputs.network.id
  bastion_host_sa_email = data.terraform_remote_state.security.outputs.bastion-host-sa
  gh_runner_sa          = data.terraform_remote_state.security.outputs.github-runner-sa

  cloud_sql_username_secret_id = "ehr-db-user"
  cloud_sql_password_secret_id = "ehr-db-password"

  data_warehouse_username_secret_id = "ehr-data-warehouse-user"
  data_warehouse_password_secret_id = "ehr-data-warehouse-password"

  data_platform_tf_sa_email = data.terraform_remote_state.project_factory.outputs["${var.env}-data-platform-project"].tf_sa_email
}


resource "google_secret_manager_secret" "ehr_cloud_sql_username" {
  secret_id = local.cloud_sql_username_secret_id
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret" "ehr_cloud_sql_password" {
  secret_id = local.cloud_sql_password_secret_id
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "ehr_cloud_sql_user" {
  secret      = google_secret_manager_secret.ehr_cloud_sql_username.id
  secret_data = "ehr_admin"
}

resource "google_secret_manager_secret_version" "ehr_cloud_sql_password" {
  secret      = google_secret_manager_secret.ehr_cloud_sql_password.id
  secret_data = random_password.cloud_sql_password.result
}

resource "random_password" "cloud_sql_password" {
  length  = 14
  special = false

  keepers = {
    date = var.cloud_sql_version_password_date
  }
}

resource "google_secret_manager_secret" "ehr_data_warehouse_username" {
  secret_id = local.data_warehouse_username_secret_id
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret" "ehr_data_warehouse_password" {
  secret_id = local.data_warehouse_password_secret_id
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "ehr_data_warehouse_user" {
  secret      = google_secret_manager_secret.ehr_data_warehouse_username.id
  secret_data = "ehr_data_warehouse"
}

resource "google_secret_manager_secret_version" "ehr_data_warehouse_password" {
  secret      = google_secret_manager_secret.ehr_data_warehouse_password.id
  secret_data = random_password.data_warehouse_password.result
}

resource "random_password" "data_warehouse_password" {
  length  = 14
  special = false

  keepers = {
    date = var.data_warehouse_version_password_date
  }
}

resource "google_project_iam_member" "bastion_host_uses_postgres" {
  for_each = toset([
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client"
  ])
  project = data.google_project.ehr.id
  role    = each.value
  member  = "serviceAccount:${local.bastion_host_sa_email}"
}

resource "google_project_iam_member" "gh_runner_uses_postgres" {
  for_each = toset([
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor",
  ])
  project = data.google_project.ehr.id
  role    = each.value
  member  = "serviceAccount:${local.gh_runner_sa}"
}

data "terraform_remote_state" "vpc" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = var.network_terraform_workspace
    }
  }
}

module "cloud_sql" {
  source  = "app.terraform.io/apella/cloud-sql/google"
  version = "2.0.1"

  name                     = "ehr"
  random_instance_name     = false
  availability_type        = var.db_availability_type
  database_version         = var.db_version
  project_id               = data.google_project.ehr.project_id
  zone                     = var.zone
  region                   = var.region
  tier                     = var.db_tier
  deletion_protection      = true
  disk_size                = var.cloud_sql_disk_size
  secret_project_id        = data.google_project.ehr.project_id
  secretname_user_name     = local.cloud_sql_username_secret_id
  secretname_user_password = local.cloud_sql_password_secret_id
  additional_users = [
    {
      name     = local.data_warehouse_username_secret_id
      password = local.data_warehouse_password_secret_id
    }
  ]

  database_flags = [
    {
      name  = "cloudsql.logical_decoding"
      value = "on"
    }
  ]

  max_connections = "200"
  read_replicas = var.cloud_sql_replica_active == false ? [] : [{
    name = "-read-api"
    tier = var.cloud_sql_replica_tier
    ip_configuration = {
      ipv4_enabled        = false
      private_network     = data.terraform_remote_state.vpc.outputs.network.id
      require_ssl         = true
      authorized_networks = var.cloud_sql_authorized_networks
    }
    database_flags = [
      {
        name  = "max_connections",
        value = "200"
      },
      {
        name  = "cloudsql.logical_decoding"
        value = "on"
      }
    ]
    user_labels = var.labels
    zone        = var.zone
    # Following are default values, but must be provided for the replica module to work
    disk_autoresize = true
    disk_size       = 200
    disk_type       = "PD_SSD"

    insights_config = {
      query_string_length     = 4500
      record_application_tags = true
      record_client_address   = true
    }
  }]

  monitor_enabled         = true
  monitor_tags            = ["env:${var.env}", "project-id:${data.google_project.ehr.project_id}", "team:clinical-data"]
  monitor_warning_channel = var.enable_db_alerts ? "@webhook-incident_io" : "@slack-team-ehr-interfaces-alert-${var.env}"

  ip_configuration = {
    ipv4_enabled        = false
    private_network     = local.vpc_network_id
    require_ssl         = true
    authorized_networks = []
  }

  backup_configuration = {
    enabled                        = true
    start_time                     = "00:00"
    location                       = var.db_backup_location
    point_in_time_recovery_enabled = true
  }

  user_labels = var.labels

  insights_config = {
    query_string_length     = 1024
    record_application_tags = true
    record_client_address   = true
  }

}

# Giving the Data Platform TF SA access to the EHR data warehouse db username and password
resource "google_secret_manager_secret_iam_member" "data_platform_fs_sa_username_secret_access" {
  project   = data.google_project.ehr.id
  secret_id = google_secret_manager_secret.ehr_data_warehouse_username.id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.data_platform_tf_sa_email}"
}

resource "google_secret_manager_secret_iam_member" "data_platform_fs_sa_password_secret_access" {
  project   = data.google_project.ehr.id
  secret_id = google_secret_manager_secret.ehr_data_warehouse_password.id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${local.data_platform_tf_sa_email}"
}
