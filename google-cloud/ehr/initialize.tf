locals {
  project_id = "${var.env}-ehr"

  web_api_project_id = length(data.google_projects.web_api.projects) == 1 ? data.google_projects.web_api.projects[0].project_id : error("Expected exactly one web-api project.")
  gke_project_id     = length(data.google_projects.gke.projects) == 1 ? data.google_projects.gke.projects[0].project_id : error("Expected exactly one gke project.")
}

terraform {
  backend "remote" {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      prefix = "ehr-"
    }
  }

  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.25"
    }
  }
}

provider "google" {
  project = local.project_id
  region  = var.region
}

# Assumed web-api naming convention
data "google_projects" "web_api" {
  filter = "NAME:${var.env}-web-api"
}

# Assumed EHR project id convention
data "google_project" "ehr" {
  project_id = local.project_id
}

# Assumed internal gke naming convention
data "google_projects" "gke" {
  filter = "NAME:${var.env}-internal"
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "terraform_remote_state" "network" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = var.network_terraform_workspace
    }
  }
}

data "terraform_remote_state" "security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = var.security_terraform_workspace
    }
  }
}

data "terraform_remote_state" "project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = var.project_factory_terraform_workspace
    }
  }
}
