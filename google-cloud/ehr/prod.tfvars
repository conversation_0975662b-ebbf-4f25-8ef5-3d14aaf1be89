env                                 = "prod"
network_terraform_workspace         = "gcp-network-prod"
security_terraform_workspace        = "gcp-security-prod"
project_factory_terraform_workspace = "gcp-project-factory-prod"
block_release_web_api_url           = "https://api.apella.io"
web_api_service_account_id          = "prod-web-api-7f60bf"
db_tier                             = "db-custom-2-7680"
cloud_sql_replica_tier              = "db-custom-2-7680"
cloud_sql_replica_active            = true
enable_db_alerts                    = true
keda_sa_email                       = "<EMAIL>"
cloud_sql_disk_size                 = 200

