module "outbound_message_processor_service" {
  source         = "./modules/service"
  service_name   = "outbound-message-processor"
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  iam_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser"
  ]
  subscribes_to_subscriptions = {
    outbound_message_processor = module.outbound_message_processor_subscription.subscription_id
  }
  labels = var.labels
}

resource "google_pubsub_topic" "outbound_messages" {
  name   = "outbound-messages"
  labels = var.labels
}

resource "google_pubsub_topic" "outbound_messages_dead_letter" {
  name = "${google_pubsub_topic.outbound_messages.name}-dead-letter"
}

module "outbound_message_processor_subscription" {
  source            = "./modules/pubsub_subscription"
  name              = "outbound-message-processor"
  google_project_id = data.google_project.ehr.project_id
  topic_id          = google_pubsub_topic.outbound_messages.id
  labels            = var.labels
}

resource "google_pubsub_subscription" "outbound_messages_dead_letter_subscription" {
  name                       = "${google_pubsub_topic.outbound_messages_dead_letter.name}-subscription"
  topic                      = google_pubsub_topic.outbound_messages_dead_letter.id
  message_retention_duration = "604800s" # 7 days.
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  expiration_policy {
    ttl = "" // Never expire this subscription.
  }
}