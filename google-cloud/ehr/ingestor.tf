locals {
  web_api_ehr_events_id = "projects/${local.web_api_project_id}/topics/${var.env}-redox-events"
}

resource "google_pubsub_topic" "ingestion_errors" {
  name   = "ingestor-errors"
  labels = var.labels
}

resource "google_pubsub_topic" "ingestor_published_ehr_events" {
  name   = "ehr-events"
  labels = var.labels
}

resource "google_pubsub_topic" "ingestor_published_ehr_events_backfill" {
  name   = "ehr-events-backfill"
  labels = var.labels
}

module "ingestor_ehr_events_subscription" {
  source            = "./modules/pubsub_subscription"
  name              = "ingestor-ehr-events" # This is temporary until the `ehr-ingestor` becomes customer facing.
  google_project_id = data.google_project.ehr.id
  topic_id          = local.web_api_ehr_events_id
  labels            = var.labels
}

module "ingestor_service" {
  source         = "./modules/service"
  service_name   = "ehr-ingestor"
  gcp_project_id = data.google_project.ehr.project_id
  gke_project_id = local.gke_project_id
  iam_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser"
  ]
  publishes_to_topics = {
    ingestion_errors                       = google_pubsub_topic.ingestion_errors.id
    ingestor_published_ehr_events          = google_pubsub_topic.ingestor_published_ehr_events.id
    ingestor_published_ehr_events_backfill = google_pubsub_topic.ingestor_published_ehr_events_backfill.id
  }
  subscribes_to_subscriptions = {
    ingestor_ehr_events = module.ingestor_ehr_events_subscription.subscription_id
  }
  labels = var.labels
}
