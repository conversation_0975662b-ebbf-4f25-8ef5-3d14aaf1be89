# Overview

This module holds EHR related infrastructure.
There is a terraform workspace and GCP project per Apella environment.

# Related docs

- https://miro.com/app/board/uXjVNNK-AqQ=/

# Dev

-   [Terraform Cloud](https://app.terraform.io/app/apella/workspaces/ehr-dev)
-   [GCP console](https://console.cloud.google.com/?project=dev-ehr)


# Staging

-   [Terraform Cloud](https://app.terraform.io/app/apella/workspaces/ehr-staging)
-   [GCP console](https://console.cloud.google.com/?project=staging-ehr)


# Prod

-   [Terraform Cloud](https://app.terraform.io/app/apella/workspaces/ehr-prod)
-   [GCP console](https://console.cloud.google.com/?project=prod-ehr)


# What’s Next

-   Adding additional services
-   Adding Postgres
-   Incrementally moving existing EHR interface infrastructure from web api modules into this module

## Service Infrastructure

### Block Release Data

Resources for ingesting block release data from customers is located in the [block release data](block_release_data.tf) file.

[SFTP Service Playbook](https://www.notion.so/apella/Block-Release-SFTP-Service-319249a2d63e43aab24729bbd860e686?pvs=4)