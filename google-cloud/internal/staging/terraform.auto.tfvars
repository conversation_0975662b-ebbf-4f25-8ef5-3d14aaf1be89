/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "staging-internal-e11112"
environment = "staging"

labels = {
  "environment" : "staging"
  "vanta-owner"              = "oren"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "staging-internal"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-dev-environment"
}


/*****************************************************************************
  GKE
******************************************************************************/
cluster_name                       = "staging-internal-gke"
gke_node_pool_network_tags         = ["staging-internal-gke-node"]
gke_primary_node_pool_machine_type = "e2-medium"
gke_resource_limits = [
  {
    resource_type = "cpu"
    minimum       = 1
    maximum       = 100
  },
  {
    resource_type = "memory"
    minimum       = 1
    maximum       = 400
  },
  {
    resource_type = "nvidia-tesla-a100"
    minimum       = 0
    maximum       = 2
  },
  {
    resource_type = "nvidia-tesla-p100"
    minimum       = 0
    maximum       = 4
  },
  {
    resource_type = "nvidia-tesla-t4"
    minimum       = 0
    maximum       = 10
  },
  {
    resource_type = "nvidia-tesla-v100"
    minimum       = 0
    maximum       = 4
  }
]
/******************************************
  Metabase
 *****************************************/
metabase_db_secretname_admin_user_name     = "staging-metabase-db-pgadmin-username"
metabase_db_secretname_admin_user_password = "staging-metabase-db-pgadmin-password"

metabase_db_backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

metabase_db_additional_users = [
  {
    name     = "staging-metabase-db-user-username"
    password = "staging-metabase-db-user-password"
  }
]

/******************************************
  Cloudflare
 *****************************************/

cloudflare_zone_id = "d2c08f8dec7ab768698724bb71098f9b"

/******************************************
  Tailscale
 *****************************************/

tailscale_bastion_tags = [
  "tag:gcp-subnet-router",
]

/******************************************
  Dagster
 *****************************************/
dagster_db_tier                           = "db-custom-1-3840"
dagster_db_secretname_admin_user_name     = "staging-dagster-db-pgadmin-username"
dagster_db_secretname_admin_user_password = "staging-dagster-db-pgadmin-password"

dagster_db_additional_users = [
  {
    name     = "staging-dagster-db-user-username"
    password = "staging-dagster-db-user-password"
  }
]
