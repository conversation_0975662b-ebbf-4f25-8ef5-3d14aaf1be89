locals {
  org_id                               = data.google_organization.org.org_id
  network_id                           = data.terraform_remote_state.nonprod_vpc.outputs.network.id
  internal_gke_network_id              = data.terraform_remote_state.nonprod_vpc.outputs.dev_internal_gke_us_central1.id
  shared_network_cidr_block            = data.terraform_remote_state.nonprod_vpc.outputs.shared_us_central1.ip_cidr_range
  internal_gke_network_name            = data.terraform_remote_state.nonprod_vpc.outputs.dev_internal_gke_us_central1.name
  internal_gke_master_ipv4_cidr_block  = data.terraform_remote_state.nonprod_vpc.outputs.nonprod_dev_internal_gke_master_range
  internal_gke_cluster_range           = data.terraform_remote_state.nonprod_vpc.outputs.dev_internal_gke["secondary_ip_range_name1"]
  internal_gke_services_range          = data.terraform_remote_state.nonprod_vpc.outputs.dev_internal_gke["secondary_ip_range_name2"]
  internal_gke_ip_cidr_range           = data.terraform_remote_state.nonprod_vpc.outputs.dev_internal_gke["ip_cidr_range"]
  network_project_id                   = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-network-project.project_id
  nonprod_gh_runner_sa                 = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  project_number                       = data.terraform_remote_state.nonprod_project_factory.outputs.dev-internal-project.project_number
  mlops_dags_sa_email                  = data.terraform_remote_state.ml_project.outputs.mlops_dags_sa.email
  realtime_dags_sa_email               = data.terraform_remote_state.data_platform_project.outputs.dagster_realtime_dags_sa.email
  dagster_analytics_pipelines_sa_email = data.terraform_remote_state.web_api_project.outputs.dagster_analytics_pipelines_sa.email
}

# BEGIN Internal Dev GKE cluster.
module "gke-cluster" {
  source         = "../modules/gke-standard-cluster"
  cluster_name   = var.cluster_name
  region         = var.region
  project_id     = var.project_id
  project_number = local.project_number
  labels         = var.labels
  enable_fuse    = true

  network    = local.network_id
  subnetwork = local.internal_gke_network_id

  master_ipv4_cidr_block = local.internal_gke_master_ipv4_cidr_block

  master_authorized_networks_config = [{
    cidr_blocks = [
      {
        cidr_block   = local.shared_network_cidr_block
        display_name = "Shared compute (bastion)"
      }
    ],
  }]
  cluster_secondary_range_name  = local.internal_gke_cluster_range
  services_secondary_range_name = local.internal_gke_services_range
  gke_node_pool_network_tags    = var.gke_node_pool_network_tags

  primary_node_pool_machine_type = var.gke_primary_node_pool_machine_type
  resource_limits                = var.gke_resource_limits

  external_dns_cloudflare_token = var.external_dns_cloudflare_token
  argocd_oauth_client_secret    = var.argocd_oauth_client_secret
  argocd_jfrog_helm_password    = var.argocd_jfrog_helm_password
}
# END Internal Dev GKE cluster.

module "metabase" {
  source          = "../modules/metabase"
  region          = var.region
  zone            = var.zone
  project_id      = var.project_id
  network_id      = local.network_id
  environment     = var.environment
  labels          = var.labels
  datadog_team_id = "data-platform"

  db_backup_configuration           = var.metabase_db_backup_configuration
  db_secretname_admin_user_name     = var.metabase_db_secretname_admin_user_name
  db_secretname_admin_user_password = var.metabase_db_secretname_admin_user_password
  db_additional_users               = var.metabase_db_additional_users
}

module "bastion" {
  source             = "../modules/bastion-host"
  environment        = var.environment
  project_id         = var.project_id
  subnetwork_project = local.network_project_id
  subnet_name        = local.internal_gke_network_name
  labels             = var.labels
  bastion_zone       = var.zone
  ip_routes = [
    local.internal_gke_ip_cidr_range,          # gke subnet
    local.internal_gke_master_ipv4_cidr_block, # control plane
  ]
  tailscale_tags = var.tailscale_bastion_tags
}

module "crossplane" {
  source     = "../modules/crossplane"
  project_id = var.project_id
}

/*****************************************************************************
  external secrets service accounts
******************************************************************************/

module "external_secrets" {
  source     = "../modules/external-secrets"
  project_id = var.project_id
}

module "dagster" {
  source     = "../modules/dagster"
  project_id = var.project_id

  region      = var.region
  zone        = var.zone
  network_id  = local.network_id
  environment = var.environment
  labels      = var.labels

  db_tier                           = var.dagster_db_tier
  db_backup_configuration           = var.dagster_db_backup_configuration
  db_secretname_admin_user_name     = var.dagster_db_secretname_admin_user_name
  db_secretname_admin_user_password = var.dagster_db_secretname_admin_user_password
  db_additional_users               = var.dagster_db_additional_users

  datadog_api_key_secret_id = module.gke-cluster.datadog_api_key_secret_id
  datadog_app_key_secret_id = module.gke-cluster.datadog_app_key_secret_id
  team_metadata = {
    forecasting = {
      warning_handle  = null
      critical_handle = "@slack-bot-ops-forecasting-dev"
      datadog_team_id = "forecasting"
    }
    computer_vision = {
      warning_handle  = "@slack-bot-ops-computer-vision-dev"
      critical_handle = "@slack-bot-ops-computer-vision-dev"
      datadog_team_id = "computer-vision"
    }
    catch_all = {
      warning_handle  = "@slack-bot-ops-computer-vision-dev"
      critical_handle = "@slack-bot-ops-computer-vision-dev"
      datadog_team_id = "computer-vision" # The DSML team is no longer a valid team in Datadog. Setting to CV as default.
    }
  }

  user_deployment_sa_emails = [local.mlops_dags_sa_email, local.realtime_dags_sa_email, local.dagster_analytics_pipelines_sa_email]
}

module "external_dns" {
  source     = "../modules/external-dns"
  project_id = var.project_id
}

module "argocd" {
  source     = "../modules/argocd"
  project_id = var.project_id
}

module "grafana" {
  source      = "../modules/grafana"
  project_id  = var.project_id
  environment = var.environment
}

module "tailscale" {
  source     = "../modules/tailscale"
  project_id = var.project_id
}
