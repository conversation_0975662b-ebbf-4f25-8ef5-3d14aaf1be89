/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-internal-dev"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 3.0"
    }
    google = {
      source  = "hashicorp/google"
      version = ">= 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 5.0"
    }
    random = {
      source = "hashicorp/random"
    }
    tailscale = {
      source  = "tailscale/tailscale"
      version = "~> 0.13.5"
    }
    datadog = {
      source = "DataDog/datadog"
    }
  }
}

data "terraform_remote_state" "nonprod_vpc" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "ml_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-machine-learning-dev"
    }
  }
}

data "terraform_remote_state" "data_platform_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-dev"
    }
  }
}

data "terraform_remote_state" "web_api_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-web-api-dev"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

provider "cloudflare" {
  account_id = var.cloudflare_account_id
  api_token  = var.cloudflare_token
}

provider "tailscale" {
  api_key = var.tailscale_api_key
  tailnet = var.tailscale_tailnet
}

data "google_organization" "org" {
  domain = var.domain_name
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}
