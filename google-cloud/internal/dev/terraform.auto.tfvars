/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "dev-internal-b2aa9f"
environment = "dev"

labels = {
  "environment" : "dev"
  "vanta-owner"              = "oren"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "dev-internal"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-dev-environment"
}

/******************************************
  GKE
 *****************************************/
cluster_name                       = "dev-internal-gke"
gke_node_pool_network_tags         = ["dev-internal-gke-node"]
gke_primary_node_pool_machine_type = "e2-medium"
gke_resource_limits = [
  {
    resource_type = "cpu"
    minimum       = 1
    maximum       = 250
  },
  {
    resource_type = "memory"
    minimum       = 1
    maximum       = 800
  },
  {
    resource_type = "nvidia-tesla-a100"
    minimum       = 0
    maximum       = 2
  },
  {
    resource_type = "nvidia-tesla-p100"
    minimum       = 0
    maximum       = 4
  },
  {
    resource_type = "nvidia-tesla-t4"
    minimum       = 0
    maximum       = 40
  },
  {
    resource_type = "nvidia-tesla-v100"
    minimum       = 0
    maximum       = 5
  }
]
/******************************************
  Metabase
 *****************************************/
metabase_db_secretname_admin_user_name     = "dev-metabase-db-pgadmin-username"
metabase_db_secretname_admin_user_password = "dev-metabase-db-pgadmin-password"

metabase_db_backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

metabase_db_additional_users = [
  {
    name     = "dev-metabase-db-user-username"
    password = "dev-metabase-db-user-password"
  }
]

/******************************************
  Cloudflare
 *****************************************/

cloudflare_zone_id = "d2c08f8dec7ab768698724bb71098f9b"

/******************************************
  Tailscale
 *****************************************/

tailscale_bastion_tags = [
  "tag:gcp-subnet-router",
]

/******************************************
  Dagster
 *****************************************/
dagster_db_tier                           = "db-custom-2-7680"
dagster_db_secretname_admin_user_name     = "dev-dagster-db-pgadmin-username"
dagster_db_secretname_admin_user_password = "dev-dagster-db-pgadmin-password"

dagster_db_additional_users = [
  {
    name     = "dev-dagster-db-user-username"
    password = "dev-dagster-db-user-password"
  }
]
