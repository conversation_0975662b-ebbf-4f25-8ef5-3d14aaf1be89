locals {
  bigquery_roles = ["roles/bigquery.dataViewer", "roles/bigquery.readSessionUser", "roles/bigquery.jobUser"]
}

# Allow prod GitHub Runner service account to manage GKE clusters in this project.
resource "google_project_iam_member" "gh_runner_container_admin_membership" {
  project = var.project_id
  role    = "roles/container.admin"
  member  = "serviceAccount:${local.prod_gh_runner_sa}"
}

# Allow the Data Warehouse to read from our prod-internal bigquery billing information
resource "google_project_iam_member" "dagster_bigquery_permissions" {
  for_each = toset(local.bigquery_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${module.dagster.dagster_self_hosted_sa.email}"
}

resource "google_project_iam_member" "prod_gh_runner_bigquery_permissions" {
  for_each = toset(local.bigquery_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.prod_gh_runner_sa}"
}
