/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "prod-internal-c5ac6b"
environment = "prod"

labels = {
  "environment" : "prod"
  "vanta-owner"              = "oren"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "prod-internal"
  "vanta-contains-user-data" = "true"
  "vanta-contains-ephi"      = "true"
}


/*****************************************************************************
  GKE
******************************************************************************/
cluster_name                       = "prod-internal-gke"
gke_node_pool_network_tags         = ["prod-internal-gke-node"]
gke_primary_node_pool_machine_type = "e2-standard-2"
gke_resource_limits = [
  {
    resource_type = "cpu"
    minimum       = 1
    maximum       = 1500
  },
  {
    resource_type = "memory"
    minimum       = 1
    maximum       = 6000
  },
  {
    resource_type = "nvidia-tesla-a100"
    minimum       = 0
    maximum       = 100
  },
  {
    resource_type = "nvidia-tesla-p100"
    minimum       = 0
    maximum       = 32
  },
  {
    resource_type = "nvidia-tesla-t4"
    minimum       = 0
    maximum       = 160
  },
  {
    resource_type = "nvidia-tesla-v100"
    minimum       = 0
    maximum       = 32
  }
]
/******************************************
  Metabase
 *****************************************/
metabase_db_tier = "db-custom-1-3840"

metabase_db_secretname_admin_user_name     = "prod-metabase-db-pgadmin-username"
metabase_db_secretname_admin_user_password = "prod-metabase-db-pgadmin-password"

metabase_db_backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

metabase_db_additional_users = [
  {
    name     = "prod-metabase-db-user-username"
    password = "prod-metabase-db-user-password"
  }
]

/******************************************
  Cloudflare
 *****************************************/

cloudflare_zone_id = "d2c08f8dec7ab768698724bb71098f9b"

/******************************************
  Tailscale
 *****************************************/

tailscale_bastion_tags = [
  "tag:gcp-subnet-router",
]

/******************************************
  Dagster
 *****************************************/
dagster_db_tier                           = "db-custom-8-30720"
dagster_db_secretname_admin_user_name     = "prod-dagster-db-pgadmin-username"
dagster_db_secretname_admin_user_password = "prod-dagster-db-pgadmin-password"

dagster_db_availability_type = "REGIONAL"

dagster_db_additional_users = [
  {
    name     = "prod-dagster-db-user-username"
    password = "prod-dagster-db-user-password"
  }
]

dagster_db_backup_configuration = {
  enabled                        = true
  start_time                     = "01:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}
