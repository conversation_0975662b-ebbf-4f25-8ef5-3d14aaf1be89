variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

/*****************************************************************************
  gke cluster
******************************************************************************/
variable "cluster_name" {
  description = "Name of the gke cluster"
  type        = string
}

variable "gke_primary_node_pool_machine_type" {
  description = "Machine type for the primary node pool"
  type        = string
}

variable "gke_resource_limits" {
  description = "Global constraints for machine resources in the cluster."
  type        = list(any)
  default     = []
}

variable "gke_node_pool_network_tags" {
  description = "The network tags to apply to every node in the cluster for more specific networking rules"
  type        = list(string)
}

/*****************************************************************************
  metabase
******************************************************************************/
variable "metabase_db_tier" {
  description = "The tier for the sql instance."
  type        = string
  default     = "db-f1-micro"
}

variable "metabase_db_secretname_admin_user_name" {
  description = "SecretManager Secret name where metabase's pg admin user name is stored."
  type        = string
}

variable "metabase_db_secretname_admin_user_password" {
  description = "SecretManager Secret name where metabase's pg admin user password is stored."
  type        = string
}

variable "metabase_db_additional_users" {
  description = "A list of users to be created in your cluster"
  type = list(object({
    name     = string
    password = string
  }))
  default = []
}

variable "metabase_db_backup_configuration" {
  description = "The backup_configuration settings subblock for the database settings"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = string
    point_in_time_recovery_enabled = bool
  })
  default = {
    enabled                        = false
    start_time                     = null
    location                       = null
    point_in_time_recovery_enabled = false
  }
}

/*****************************************************************************
  cloudflare
******************************************************************************/

variable "cloudflare_zone_id" {
  description = "The Cloudflare Zone ID to use (from Cloudflare dashboard)"
  type        = string
}

variable "cloudflare_account_id" {
  description = "The Cloudflare UUID for the Account the Zone lives in."
  type        = string
  sensitive   = true
}

variable "cloudflare_token" {
  description = "The Cloudflare API token."
  type        = string
}

variable "cloudflare_origincert" {
  description = "The Cloudflare origin certificate for Argo tunnels. See https://dash.cloudflare.com/argotunnel"
  type        = string
  sensitive   = true
}

variable "external_dns_cloudflare_token" {
  description = "The Cloudflare API token for external DNS."
  type        = string
  sensitive   = true
}

/*****************************************************************************
  tailscale
******************************************************************************/

variable "tailscale_api_key" {
  description = "The Tailscale API key."
  type        = string
  sensitive   = true
}

variable "tailscale_tailnet" {
  description = "Tailscale network"
  type        = string
  default     = "apella.io"
}

variable "tailscale_bastion_tags" {
  description = "Tags to be applied to the tailscale connection"
  type        = list(string)
  default     = []
}


/*****************************************************************************
  datadog
******************************************************************************/

variable "datadog_api_key" {
  description = "The datadog api key for Terraform to use"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "The datadog app key for Terraform to use"
  type        = string
  sensitive   = true
}
/*****************************************************************************
  argocd
******************************************************************************/
variable "argocd_oauth_client_secret" {
  description = "The Google oauth client secret for ArgoCD"
  type        = string
  sensitive   = true
}

variable "argocd_jfrog_helm_password" {
  description = "The JFrog Helm User Password"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Dagster
******************************************************************************/

variable "dagster_db_secretname_admin_user_name" {
  description = "SecretManager Secret name where dagster's pg admin user name is stored."
  type        = string
}

variable "dagster_db_secretname_admin_user_password" {
  description = "SecretManager Secret name where dagster's pg admin user password is stored."
  type        = string
}

variable "dagster_db_additional_users" {
  description = "A list of users to be created in your cluster"
  type = list(object({
    name     = string
    password = string
  }))
  default = []
}

variable "dagster_db_tier" {
  description = "The sql instance tier to use for the Dagster metabase postgres db"
  type        = string
}

variable "dagster_db_backup_configuration" {
  description = "The backup_configuration settings subblock for the database settings"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = string
    point_in_time_recovery_enabled = bool
  })
  default = {
    enabled                        = false
    start_time                     = null
    location                       = null
    point_in_time_recovery_enabled = false
  }
}

variable "dagster_db_availability_type" {
  description = "The availability type for the master instance. This is only used to set up high availability for the PostgreSQL instance. Can be either `ZONAL` or `REGIONAL`."
  type        = string
}