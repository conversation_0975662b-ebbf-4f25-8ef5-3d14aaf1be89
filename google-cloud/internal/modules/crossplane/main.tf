/******************************************
  Crossplane SA
 *****************************************/
resource "google_service_account" "crossplane_sa" {
  account_id   = "crossplane-sa"
  project      = var.project_id
  display_name = "Crossplane Service Account"
}

// Crossplane will both use secrets and create secrets
resource "google_project_iam_member" "crossplane_roles" {
  project = var.project_id
  role    = "roles/secretmanager.admin"
  member  = "serviceAccount:${google_service_account.crossplane_sa.email}"
}

resource "google_service_account_iam_member" "crossplane_wi_iam_member" {
  service_account_id = google_service_account.crossplane_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.crossplane_namespace}/${var.crossplane_ksa}]"
}

resource "google_service_account_iam_member" "crossplane_provider_wi_iam_member" {
  service_account_id = google_service_account.crossplane_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.crossplane_namespace}/${var.crossplane_provider_ksa}]"
}
