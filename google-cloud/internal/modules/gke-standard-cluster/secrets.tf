/*****************************************************************************
  Cloudflare Secret
******************************************************************************/
resource "google_secret_manager_secret" "cloudflare_secret" {
  project   = var.project_id
  secret_id = "cloudflare_token"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "cloudflare_secret_version" {
  secret = google_secret_manager_secret.cloudflare_secret.id

  secret_data = var.external_dns_cloudflare_token
}

/*****************************************************************************
  ArgoCD Oauth Secret
******************************************************************************/
resource "google_secret_manager_secret" "argocd_oauth_client_secret" {
  project   = var.project_id
  secret_id = "argocd_oauth_client_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "argocd_oauth_client_secret_version" {
  secret = google_secret_manager_secret.argocd_oauth_client_secret.id

  secret_data = var.argocd_oauth_client_secret
}

/*****************************************************************************
  JFrog Helm Password
******************************************************************************/
resource "google_secret_manager_secret" "argocd_jfrog_helm_password" {
  project   = var.project_id
  secret_id = "argocd_jfrog_helm_password"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "argocd_jfrog_helm_password_version" {
  secret = google_secret_manager_secret.argocd_jfrog_helm_password.id

  secret_data = var.argocd_jfrog_helm_password
}
