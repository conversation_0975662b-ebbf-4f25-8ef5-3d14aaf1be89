/***********************************************************************
Creating and mapping the k8s service account to the GCP service account
***********************************************************************/

resource "google_service_account" "datadog_sa" {
  account_id   = "datadog-sa"
  project      = var.project_id
  display_name = "Datadog Service Account"
}

resource "google_project_iam_member" "datadog_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.datadog_sa.email}"
}

resource "google_service_account_iam_member" "datadog_wi_iam_member" {
  service_account_id = google_service_account.datadog_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.datadog_namespace}/${var.datadog_ksa}]"
}

/***********************************************************************
Creating the Datadog api and app keys
***********************************************************************/

resource "datadog_api_key" "cluster_api_key" {
  name = var.cluster_name
}

resource "datadog_application_key" "cluster_app_key" {
  name = var.cluster_name
}

/***********************************************************************
Pushing the api and app key to GCP secrets for k8s to read from
***********************************************************************/

resource "google_secret_manager_secret" "datadog_api_key" {
  project   = var.project_id
  secret_id = "datadog_api_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "datadog_api_key_version" {
  secret = google_secret_manager_secret.datadog_api_key.id

  secret_data = datadog_api_key.cluster_api_key.key
}

resource "google_secret_manager_secret" "datadog_app_key" {
  project   = var.project_id
  secret_id = "datadog_app_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "datadog_application_key_version" {
  secret = google_secret_manager_secret.datadog_app_key.id

  secret_data = datadog_application_key.cluster_app_key.key
}
