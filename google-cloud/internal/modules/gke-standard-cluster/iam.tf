locals {
  gke_nodes_sa_roles = [
    "roles/container.serviceAgent",
  ]
}

resource "google_service_account" "gke_nodes_sa" {
  account_id   = "gke-nodes-sa"
  project      = var.project_id
  display_name = "GKE nodes Service Account"
}

// These roles are needed for the gke-nodes SA
resource "google_project_iam_member" "gke_nodes_roles" {
  for_each = toset(local.gke_nodes_sa_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.gke_nodes_sa.email}"
}
