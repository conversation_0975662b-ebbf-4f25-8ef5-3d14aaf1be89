locals {
  keda_ksas = ["keda", "keda-metrics-server", "keda-operator", "keda-webhook"]
}

resource "google_service_account" "keda_sa" {
  account_id   = "keda-sa"
  project      = var.project_id
  display_name = "KEDA Service Account"
}

resource "google_service_account_iam_member" "keda_wi_iam_member" {
  for_each           = toset(local.keda_ksas)
  service_account_id = google_service_account.keda_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.keda_namespace}/${each.value}]"
}
