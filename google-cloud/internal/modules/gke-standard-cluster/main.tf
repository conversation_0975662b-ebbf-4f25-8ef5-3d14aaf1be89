resource "google_container_cluster" "default" {
  provider = google-beta
  name     = var.cluster_name
  location = var.region
  project  = var.project_id
  resource_labels = merge(var.labels, {
    "mesh_id" = "proj-${var.project_number}"
  })

  network    = var.network
  subnetwork = var.subnetwork

  addons_config {
    gcs_fuse_csi_driver_config {
      enabled = var.enable_fuse
    }
  }

  private_cluster_config {
    enable_private_endpoint = true
    enable_private_nodes    = true
    master_ipv4_cidr_block  = var.master_ipv4_cidr_block
  }

  dynamic "master_authorized_networks_config" {
    for_each = var.master_authorized_networks_config
    content {
      dynamic "cidr_blocks" {
        for_each = lookup(master_authorized_networks_config.value, "cidr_blocks", [])
        content {
          cidr_block   = cidr_blocks.value.cidr_block
          display_name = lookup(cidr_blocks.value, "display_name", null)
        }
      }
    }
  }

  maintenance_policy {
    recurring_window {
      # 10pm to 2am PST every day
      start_time = "2025-01-28T06:00:00Z"
      end_time   = "2025-01-28T10:00:00Z"
      recurrence = "FREQ=DAILY"
    }
  }


  # Configuration of cluster IP allocation for VPC-native clusters
  ip_allocation_policy {
    cluster_secondary_range_name  = var.cluster_secondary_range_name
    services_secondary_range_name = var.services_secondary_range_name
  }

  # Configuration options for the Release channel feature, which provide more control over automatic upgrades of your GKE clusters.
  release_channel {
    channel = "REGULAR"
  }
  timeouts {
    create = "30m"
    update = "40m"
  }

  lifecycle {
    ignore_changes = [
    ]
  }

  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  # see - https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster#example-usage---with-a-separately-managed-node-pool-recommended
  remove_default_node_pool = true
  initial_node_count       = 1

  # Auto-provisioning configuration
  cluster_autoscaling {
    enabled             = true
    autoscaling_profile = "BALANCED"
    dynamic "resource_limits" {
      for_each = var.resource_limits
      content {
        resource_type = resource_limits.value["resource_type"]
        minimum       = resource_limits.value["minimum"]
        maximum       = resource_limits.value["maximum"]
      }
    }
    auto_provisioning_defaults {
      oauth_scopes = [
        "https://www.googleapis.com/auth/cloud-platform",
        "https://www.googleapis.com/auth/monitoring",
        "https://www.googleapis.com/auth/trace.append",
        "https://www.googleapis.com/auth/logging.write",
        "https://www.googleapis.com/auth/devstorage.read_only",
        "https://www.googleapis.com/auth/compute"
      ]
      service_account = google_service_account.gke_nodes_sa.email
    }

  }

  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }

  node_pool_auto_config {
    network_tags {
      tags = var.gke_node_pool_network_tags
    }
  }

  # Overriding the GKE default of 110. We typically don't have many pods in a single node, and it
  # is wasteful to pre-allocate 110 IPs
  default_max_pods_per_node = var.default_max_pods_per_node
}

resource "google_container_node_pool" "primary_node_pool" {
  name     = "primary-node-pool"
  location = var.region
  cluster  = google_container_cluster.default.name
  # Note that for a multi-zone cluster, it creates one per region. So the value 1 means 3 nodes
  node_count = var.primary_node_pool_node_count

  node_config {
    preemptible     = false
    machine_type    = var.primary_node_pool_machine_type
    service_account = google_service_account.gke_nodes_sa.email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/monitoring",
      "https://www.googleapis.com/auth/trace.append",
    ]
    tags = var.gke_node_pool_network_tags
  }
}
