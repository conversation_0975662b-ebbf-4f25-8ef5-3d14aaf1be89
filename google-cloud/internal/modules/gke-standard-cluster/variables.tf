variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "cluster_name" {
  type = string
}

variable "enable_fuse" {
  type        = bool
  default     = false
  description = "Enable the FUSE CSI driver in the project cluster"
}

variable "region" {
  type = string
}

variable "project_id" {
  type = string
}

variable "project_number" {
  type = string
}

variable "network" {
  type = string
}

variable "subnetwork" {
  type = string
}

variable "master_ipv4_cidr_block" {
  description = "This range must not overlap with any other ranges in use within the cluster's network, and it must be a /28 subnet"
  type        = string
}

variable "cluster_secondary_range_name" {
  type = string
}

variable "services_secondary_range_name" {
  type = string
}

variable "master_authorized_networks_config" {
  description = <<EOF
  The desired configuration options for master authorized networks. Omit the nested cidr_blocks attribute to disallow external access (except the cluster node IPs, which GKE automatically whitelists)
  ### example format ###
  master_authorized_networks_config = [{
    cidr_blocks = [{
      cidr_block   = "10.0.0.0/8"
      display_name = "example_network"
    }],
  }]
EOF
  type        = list(any)
  default     = []
}

variable "gke_node_pool_network_tags" {
  description = "The network tags to apply to every node in the cluster for more specific networking rules"
  type        = list(string)
}

variable "default_max_pods_per_node" {
  description = "Number of pods allowed per node"
  type        = number
  default     = 32
}

variable "primary_node_pool_node_count" {
  description = "Number of nodes (per zone) in the primary node pool. Should be 1, unless you have a very busy cluster"
  type        = number
  default     = 1
}

variable "primary_node_pool_machine_type" {
  description = "Machine type for the primary node pool"
  type        = string
}

variable "resource_limits" {
  description = "Global constraints for machine resources in the cluster."
  type        = list(any)
  default     = []
}

/*****************************************************************************
  cloudflare
******************************************************************************/
variable "external_dns_cloudflare_token" {
  description = "The Cloudflare API token for external DNS."
  type        = string
  sensitive   = true
}

/*****************************************************************************
  argocd
******************************************************************************/
variable "argocd_oauth_client_secret" {
  description = "The Google oauth client secret for ArgoCD"
  type        = string
  sensitive   = true
}

variable "argocd_jfrog_helm_password" {
  description = "The JFrog Helm User Password"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  Datadog
******************************************************************************/
variable "datadog_namespace" {
  description = "Datadog namespace"
  default     = "datadog"
}

variable "datadog_ksa" {
  description = "Datadog ksa"
  default     = "datadog"
}

/*****************************************************************************
  Keda
******************************************************************************/
variable "keda_namespace" {
  description = "The namespace where KEDA is deployed in the k8s cluster"
  default     = "keda"
}
