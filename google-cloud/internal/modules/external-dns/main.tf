resource "google_service_account" "external_dns_cloud_agent_sa" {
  account_id   = "external-dns-cloud-agent-sa"
  project      = var.project_id
  display_name = "external-dns Cloud Agent Service Account"
}

resource "google_project_iam_member" "external_dns_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.external_dns_cloud_agent_sa.email}"
}

resource "google_service_account_iam_member" "external_dns_wi_iam_member" {
  service_account_id = google_service_account.external_dns_cloud_agent_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.external_dns_namespace}/${var.external_dns_ksa}]"
}
