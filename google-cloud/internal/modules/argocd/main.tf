/******************************************
  ArgoCD SA
 *****************************************/
resource "google_service_account" "argocd_sa" {
  account_id   = "argocd-sa"
  project      = var.project_id
  display_name = "ArgoCD Service Account"
}

resource "google_project_iam_member" "argocd_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.argocd_sa.email}"
}

resource "google_service_account_iam_member" "argocd_wi_iam_member" {
  service_account_id = google_service_account.argocd_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.argocd_namespace}/${var.argocd_ksa}]"
}

resource "google_service_account_key" "argocd_sa_key" {
  service_account_id = google_service_account.argocd_sa.id
}

# Google Secret Manager secret which stores the base64 encoded key. The cluster_sa has access
# to read this secret and injects it as a Kubernetes Docker pull secret to which the ArgoCD image
# updater has access.
resource "google_secret_manager_secret" "argocd_sa_key" {
  project   = var.project_id
  secret_id = "argocd_sa_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "argocd_sa_key_version" {
  secret      = google_secret_manager_secret.argocd_sa_key.id
  secret_data = google_service_account_key.argocd_sa_key.private_key
}

/******************************************
  ArgoCD Image Updater SA
 *****************************************/
resource "google_service_account" "argocd_image_updater_sa" {
  account_id   = "argocd-image-updater"
  project      = var.project_id
  display_name = "ArgoCD Image Updater Service Account"
}

resource "google_service_account_iam_member" "argocd_image_updater_wi_iam_member" {
  service_account_id = google_service_account.argocd_image_updater_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.argocd_namespace}/${var.argocd_image_updater_ksa}]"
}

# SECURITY NOTE: Refer to: https://github.com/Apella-Technology/tf-edge/blob/main/terraform/argocd-image-updater.tf#L10
# This key is being created through terraform, and is stored in terraform state. So having access to this key will mean
# people can access our internal account. Blast radius in this case will be access to our Artifact registry.
resource "google_service_account_key" "argocd_image_updater_sa_key" {
  service_account_id = google_service_account.argocd_image_updater_sa.id
}

# Google Secret Manager secret which stores the base64 encoded key. The cluster_sa has access
# to read this secret and injects it as a Kubernetes Docker pull secret to which the ArgoCD image
# updater has access.
resource "google_secret_manager_secret" "argocd_image_updater_sa_key" {
  project   = var.project_id
  secret_id = "argocd_image_updater_sa_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "argocd_image_updater_sa_key_version" {
  secret      = google_secret_manager_secret.argocd_image_updater_sa_key.id
  secret_data = google_service_account_key.argocd_image_updater_sa_key.private_key
}

/******************************************
  Argo Rollouts SA
 *****************************************/

resource "google_service_account" "argo_rollouts_sa" {
  account_id   = "argo-rollouts-sa"
  project      = var.project_id
  display_name = "Argo Rollouts Service Account"
}

resource "google_project_iam_member" "argo_rollouts_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.argo_rollouts_sa.email}"
}

resource "google_service_account_iam_member" "argo_rollouts_wi_iam_member" {
  service_account_id = google_service_account.argo_rollouts_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.argo_rollouts_namespace}/${var.argo_rollouts_ksa}]"
}

/******************************************
  ArgoCD Notifications
 *****************************************/

resource "google_secret_manager_secret" "slack_app_token" {
  project   = var.project_id
  secret_id = "argo_notification_slack_app_token"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret" "github_app_argo_notification_key" {
  project   = var.project_id
  secret_id = "github_app_argo_notification_key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}
