variable "project_id" {
  description = "GCP Project ID"
}

variable "argocd_namespace" {
  description = "External DNS namespace"
  default     = "argocd"
}

variable "argocd_ksa" {
  description = "ArgoCD ksa"
  default     = "argocd-apella-sa"
}

variable "argocd_image_updater_ksa" {
  description = "ArgoCD Image Updater ksa"
  default     = "argocd-image-updater"
}

variable "argo_rollouts_namespace" {
  description = "The Argo Rollouts Kubernetes namespace"
  default     = "argo-rollouts"
}

variable "argo_rollouts_ksa" {
  description = "The Argo Rollouts Kubernetes service account name"
  default     = "argo-rollouts"
}
