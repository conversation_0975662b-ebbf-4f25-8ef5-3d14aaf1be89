resource "google_service_account" "external_secret_sa" {
  account_id   = "external-secrets-sa"
  project      = var.project_id
  display_name = "External-Secrets Service Account"
}

resource "google_project_iam_member" "external_secrets_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.external_secret_sa.email}"
}

resource "google_service_account_iam_member" "external_secrets_wi_iam_member" {
  service_account_id = google_service_account.external_secret_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.external_secrets_namespace}/${var.external_secrets_ksa}]"
}
