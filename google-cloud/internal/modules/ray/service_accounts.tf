# SA and permissions for Ray
resource "google_service_account" "ray_sa" {
  account_id   = "ray-sa"
  display_name = "Service account for <PERSON>"
}

resource "google_service_account_iam_member" "internal_gke_ray_gsa_workload_identity" {
  service_account_id = google_service_account.ray_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.gke_project_id}.svc.id.goog[${var.namespace}/${var.k8s_service_account}]"
}
