resource "tailscale_tailnet_key" "bastion_tailnet_key" {
  reusable  = true
  ephemeral = true
  tags      = var.tailscale_tags
}

resource "google_secret_manager_secret" "tailscale_bastion_state" {
  secret_id = "internal-gke-bastion-tailscale-state"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_member" "tailscale_bastion_secret_iam_member" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.tailscale_bastion_state.id
  role      = "roles/secretmanager.secretVersionManager"
  member    = "serviceAccount:${google_service_account.gke_bastion_sa.email}"
}

resource "google_compute_instance" "gke_bastion_host" {
  name                      = "${var.environment}-internal-gke-bastion"
  machine_type              = var.bastion_instance_type
  zone                      = var.bastion_zone
  labels                    = var.labels
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = "ubuntu-2204-lts"
    }
  }

  network_interface {
    subnetwork_project = var.subnetwork_project
    subnetwork         = var.subnet_name
  }

  scheduling {
    on_host_maintenance = "MIGRATE"
    automatic_restart   = true
    preemptible         = false
  }

  service_account {
    email = google_service_account.gke_bastion_sa.email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }

  // This is where we configure the server (aka instance). Variables like web_zone take a terraform variable and provide it to the server so that it can use them as a local variable
  metadata = {
    startup-script = templatefile("${path.module}/templates/bastion.tpl",
      {
        tailscale_authkey     = tailscale_tailnet_key.bastion_tailnet_key.key
        tailscale_secret_name = google_secret_manager_secret.tailscale_bastion_state.name
        tailscale_routes      = join(",", var.ip_routes)
    })
  }
}
