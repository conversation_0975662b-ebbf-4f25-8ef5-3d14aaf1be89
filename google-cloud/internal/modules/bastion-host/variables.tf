variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "project_id" {
  type = string
}

variable "subnetwork_project" {
  type = string
}

variable "subnet_name" {
  type = string
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "bastion_instance_type" {
  description = "Compute instance to use for bastion"
  type        = string
  default     = "n1-standard-1"
}

variable "bastion_zone" {
  description = "Zone to put bastion host in"
  type        = string
}

variable "ip_routes" {
  description = "The CIDR to routes over tunnels"
  type        = list(string)
}

variable "tailscale_tags" {
  description = "Tags to apply to tailscale hosts"
  type        = list(string)
}

