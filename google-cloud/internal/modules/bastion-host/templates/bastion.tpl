# The OS is updated
sudo apt update -y && sudo apt upgrade -y

#
# Tailscale Setup
#

# Enable IP forwarding
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p /etc/sysctl.conf

# Pull the previous state that exists
sudo gcloud secrets versions access "latest" --secret ${tailscale_secret_name} --out-file="/var/lib/tailscale/tailscaled.state"

# Install tailscale
sudo curl -fsSL https://tailscale.com/install.sh | sh
sudo tailscale up --authkey ${tailscale_authkey} --advertise-routes=${tailscale_routes}

# Save the current state after connected so that future node recreations will be registered as the same Tailscale machine
sudo gcloud secrets versions add ${tailscale_secret_name} --data-file="/var/lib/tailscale/tailscaled.state"
