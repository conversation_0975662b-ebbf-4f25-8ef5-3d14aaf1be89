locals {
  storageRoles = toset([
    "roles/storage.admin",
    "roles/storage.objectAdmin"
  ])
}


/******************************************
  Grafana
 *****************************************/
resource "google_service_account" "grafana_sa" {
  account_id   = "grafana-sa"
  project      = var.project_id
  display_name = "Grafana Service Account"
}

resource "google_service_account_iam_member" "grafana_wi_iam_member" {
  service_account_id = google_service_account.grafana_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.grafana_namespace}/${var.grafana_ksa}]"
}

resource "google_secret_manager_secret" "grafana_oauth_client_secret" {
  project   = var.project_id
  secret_id = "grafana_oauth_client_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_member" "grafana_oauth_client_secret_member" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.grafana_oauth_client_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.grafana_sa.email}"
}

/******************************************
  Grafana Mimir
 *****************************************/
resource "google_service_account" "grafana_mimir_sa" {
  account_id   = "grafana-mimir-sa"
  project      = var.project_id
  display_name = "Grafana Mimir Service Account"
}

resource "google_service_account_iam_member" "grafana_mimir_wi_iam_member" {
  service_account_id = google_service_account.grafana_mimir_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.project_id}.svc.id.goog[${var.grafana_namespace}/${var.grafana_mimir_ksa}]"
}

/******************************************
  Grafana Tempo
 *****************************************/
resource "google_service_account" "grafana_tempo_sa" {
  account_id   = "grafana-tempo-sa"
  project      = var.project_id
  display_name = "Grafana Tempo Service Account"
}

resource "google_service_account_iam_member" "grafana_tempo_wi_iam_member" {
  service_account_id = google_service_account.grafana_tempo_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.project_id}.svc.id.goog[${var.grafana_namespace}/${var.grafana_tempo_ksa}]"
}


/******************************************
  Grafana Data Bucket
 *****************************************/
resource "google_storage_bucket" "grafana" {
  name                        = "${var.environment}-internal-grafana"
  project                     = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
}

resource "google_storage_bucket_iam_member" "grafana_bucket_permissions" {
  for_each = local.storageRoles
  bucket   = google_storage_bucket.grafana.name
  role     = each.value
  member   = "serviceAccount:${google_service_account.grafana_sa.email}"
}

resource "google_storage_bucket_iam_member" "grafana_mimir_bucket_permissions" {
  for_each = local.storageRoles
  bucket   = google_storage_bucket.grafana.name
  role     = each.value
  member   = "serviceAccount:${google_service_account.grafana_mimir_sa.email}"
}

resource "google_storage_bucket_iam_member" "grafana_tempo_bucket_permissions" {
  for_each = local.storageRoles
  bucket   = google_storage_bucket.grafana.name
  role     = each.value
  member   = "serviceAccount:${google_service_account.grafana_tempo_sa.email}"
}
