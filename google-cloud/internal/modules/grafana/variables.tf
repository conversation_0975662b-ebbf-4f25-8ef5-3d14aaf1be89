variable "project_id" {
  description = "GCP Project ID"
}

variable "grafana_namespace" {
  description = "External DNS namespace"
  default     = "grafana"
}

variable "grafana_ksa" {
  description = "Grafana ksa"
  default     = "grafana"
}

variable "grafana_mimir_ksa" {
  description = "Grafana ksa"
  default     = "grafana-mimir"
}

variable "grafana_tempo_ksa" {
  description = "Grafana ksa"
  default     = "grafana-tempo"
}

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}
