/******************************************
  Tailscale SA
 *****************************************/
resource "google_service_account" "tailscale_sa" {
  account_id   = "tailscale-sa"
  project      = var.project_id
  display_name = "Tailscale Service Account"
}

resource "google_service_account_iam_member" "tailscale_wi_iam_member" {
  service_account_id = google_service_account.tailscale_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.tailscale_namespace}/${var.tailscale_ksa}]"
}

/******************************************
  Tailscale oauth secrets
 *****************************************/

resource "google_secret_manager_secret" "tailscale_oauth_client_secret" {
  project   = var.project_id
  secret_id = "tailscale_oauth_client_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret" "tailscale_oauth_client_id" {
  project   = var.project_id
  secret_id = "tailscale_oauth_client_id"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_iam_member" "tailscale_oauth_client_secret_access" {
  secret_id = google_secret_manager_secret.tailscale_oauth_client_secret.id
  project   = var.project_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.tailscale_sa.email}"
}

resource "google_secret_manager_secret_iam_member" "id" {
  secret_id = google_secret_manager_secret.tailscale_oauth_client_id.id
  project   = var.project_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.tailscale_sa.email}"
}
