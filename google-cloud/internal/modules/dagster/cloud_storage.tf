resource "google_storage_bucket" "internal_dagster" {
  name                        = "${var.environment}-internal-dagster"
  project                     = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age            = 30
      matches_prefix = ["compute_logs/"]
    }
    action {
      type = "Delete"
    }
  }
}
