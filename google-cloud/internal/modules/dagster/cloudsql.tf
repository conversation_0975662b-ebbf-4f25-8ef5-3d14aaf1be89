module "cloud_sql" {
  source  = "app.terraform.io/apella/cloud-sql/google"
  version = "1.4.4"

  name                     = "${var.environment}-dagster"
  random_instance_name     = false
  availability_type        = var.db_availability_type
  database_version         = var.db_version
  project_id               = var.project_id
  zone                     = var.zone
  region                   = var.region
  tier                     = var.db_tier
  deletion_protection      = false
  secret_project_id        = var.project_id
  secretname_user_name     = var.db_secretname_admin_user_name
  secretname_user_password = var.db_secretname_admin_user_password


  ip_configuration = {
    ipv4_enabled        = false
    private_network     = var.network_id
    require_ssl         = true
    authorized_networks = var.db_authorized_networks
  }

  backup_configuration = var.db_backup_configuration

  additional_users = var.db_additional_users
  user_labels      = var.labels

  monitor_enabled         = true
  monitor_tags            = ["env:${var.environment}", "project-id:${var.project_id}", "team:${var.team_metadata.catch_all.datadog_team_id}"]
  monitor_warning_channel = "@slack-bot-ops-computer-vision-${var.environment}"

  insights_config = {
    query_string_length     = 4096
    record_application_tags = true
    record_client_address   = true
  }
}
