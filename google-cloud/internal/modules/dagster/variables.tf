variable "project_id" {
  description = "GCP Project ID"
}

variable "dagster_namespace" {
  description = "Dagster Self Hosted Namespace"
  default     = "dagster"
}

variable "dagster_ksa" {
  description = "Dagster ksa"
  default     = "dagster"
}

/******************************************
  Dagster SQL Values
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "network_id" {
  description = "Network id for the cloud SQL to use"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "db_tier" {
  description = "The tier for the sql instance."
  type        = string
  default     = "db-f1-micro"
}

// required
variable "db_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_13"
}

variable "db_authorized_networks" {
  description = "A list of authorized networks that are allowed to access cloudsql"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "db_backup_configuration" {
  description = "The backup_configuration settings subblock for the database settings"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = string
    point_in_time_recovery_enabled = bool
  })
  default = {
    enabled                        = false
    start_time                     = null
    location                       = null
    point_in_time_recovery_enabled = false
  }
}

variable "db_additional_users" {
  description = "A list of users to be created in your cluster"
  type = list(object({
    name     = string
    password = string
  }))
  default = []
}

variable "db_secretname_admin_user_name" {
  description = "SecretManager Secret name where admin user name is stored"
  type        = string
}

variable "db_secretname_admin_user_password" {
  description = "SecretManager Secret name where admin users password is stored."
  type        = string
}

variable "db_availability_type" {
  description = "The availability type for the master instance. This is only used to set up high availability for the PostgreSQL instance. Can be either `ZONAL` or `REGIONAL`."
  type        = string
  default     = "ZONAL"
}

variable "user_deployment_sa_emails" {
  description = "List of user deployment service account emails"
  type        = list(string)
  default     = []
}

variable "datadog_api_key_secret_id" {
  description = "ID of the Secret Manager secret for the Datadog API key"
  type        = string
}

variable "datadog_app_key_secret_id" {
  description = "ID of the Secret Manager secret for the Datadog app key"
  type        = string
}

variable "team_metadata" {
  type = object({
    forecasting = object({
      warning_handle  = optional(string)
      critical_handle = string
      datadog_team_id = string
    })
    computer_vision = object({
      warning_handle  = optional(string)
      critical_handle = string
      datadog_team_id = string
    })
    catch_all = object({
      warning_handle  = string
      critical_handle = string
      datadog_team_id = string
    })
  })
  description = "Metadata for teams. The handles to which warnings or critical alerts should be sent, prefixed with '@'."
}
