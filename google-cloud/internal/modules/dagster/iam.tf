locals {
  dagster_self_hosted_sa_roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
  ]
  user_deployment_sa_roles = [
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
  ]
  user_deployments_roles_combinations = [
    for pair in setproduct(var.user_deployment_sa_emails, local.user_deployment_sa_roles) : {
      email = pair[0]
      role  = pair[1]
    }
  ]
  all_dagster_sa_emails = toset(
    concat(
      var.user_deployment_sa_emails,
      [google_service_account.dagster_self_hosted_sa.email]
    )
  )
}

/******************************************
  Dagster Hosted Service Account
 *****************************************/

resource "google_service_account" "dagster_self_hosted_sa" {
  account_id   = "dagster-sa"
  project      = var.project_id
  display_name = "Dagster Service Account"
}

resource "google_project_iam_member" "dagster_self_hosted_roles" {
  for_each = toset(local.dagster_self_hosted_sa_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.dagster_self_hosted_sa.email}"
}

resource "google_service_account_iam_member" "dagster_self_hosted_wi_iam_member" {
  service_account_id = google_service_account.dagster_self_hosted_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.project_id}.svc.id.goog[${var.dagster_namespace}/${var.dagster_ksa}]"
}

/******************************************
  Give User Deployment Service Accounts access to the DB
 *****************************************/
resource "google_project_iam_member" "dagster_user_deployment_sa_roles" {
  for_each = {
    for combo in local.user_deployments_roles_combinations : "${combo.email}.${combo.role}" => combo
  }
  project = var.project_id
  role    = each.value.role
  member  = "serviceAccount:${each.value.email}"
}

/******************************************
  Dagster User Deployment permissions
 *****************************************/

// Read/write access to the objects in the internal Dagster storage bucket
resource "google_storage_bucket_iam_member" "internal_dagster_object_user" {
  for_each = local.all_dagster_sa_emails
  bucket   = google_storage_bucket.internal_dagster.name
  role     = "roles/storage.objectUser"
  member   = "serviceAccount:${each.value}"
}

// Read access to the internal Dagster storage bucket
resource "google_storage_bucket_iam_member" "internal_dagster_bucket_reader" {
  for_each = local.all_dagster_sa_emails
  bucket   = google_storage_bucket.internal_dagster.name
  role     = "roles/storage.legacyBucketReader"
  member   = "serviceAccount:${each.value}"
}

// Datadog API key secret access
resource "google_secret_manager_secret_iam_member" "user_deployment_datadog_api_key_secret_access" {
  secret_id = var.datadog_api_key_secret_id
  project   = var.project_id
  role      = "roles/secretmanager.secretAccessor"
  for_each  = local.all_dagster_sa_emails
  member    = "serviceAccount:${each.value}"
}

// Datadog app key secret access
resource "google_secret_manager_secret_iam_member" "user_deployment_datadog_app_key_secret_access" {
  secret_id = var.datadog_app_key_secret_id
  project   = var.project_id
  role      = "roles/secretmanager.secretAccessor"
  for_each  = local.all_dagster_sa_emails
  member    = "serviceAccount:${each.value}"
}
