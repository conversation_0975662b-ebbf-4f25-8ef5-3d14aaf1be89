locals {
  dagster_proportional_health_monitors = {
    "forecasting_monitors" = {
      services = [
        "dagster/realtime-dags/same_day_feature_calculation_job",
        "dagster/realtime-dags/same_day_forecast_job",
        "dagster/realtime-dags/calc_features_same_day_partition_sensor",
        "dagster/realtime-dags/forecasting_same_day_partition_sensor",
        "dagster/realtime-dags/forecasting_future_job_sensor",
        "dagster/realtime-dags/future_forecast_job",
        "dagster/realtime-dags/update_static_models_features_table",
      ],
      critical_handle = var.team_metadata.forecasting.critical_handle,
      datadog_team_id = var.team_metadata.forecasting.datadog_team_id,
    }
    "computer_vision_monitors" = {
      services        = ["dagster/realtime-dags/calculate_annotation_needs_job"],
      critical_handle = var.team_metadata.computer_vision.critical_handle,
      datadog_team_id = var.team_metadata.computer_vision.datadog_team_id,
    }
    # System monitors that are the responsibility of the team maintaining Dagster.
    "infrastructure_monitors" = {
      services = [
        "dagster/realtime-dags/run_monitor",
        "dagster/realtime-dags/instigator_monitor",
        "dagster/realtime-dags/delete_runs_job",
      ],
      critical_handle = var.team_metadata.catch_all.critical_handle,
      datadog_team_id = var.team_metadata.catch_all.datadog_team_id,
    }
  }
  dagster_non_recoverable_health_monitors = {
    "forecasting_monitors" = {
      services = [
        "dagster/mlops-dags/generic_ml_training_run_training_job",
      "dagster/mlops-dags/generic_forecasting_ml_training_run_training_job"],
      critical_handle = var.team_metadata.forecasting.critical_handle,
      datadog_team_id = var.team_metadata.forecasting.datadog_team_id,
    }
    "computer_vision_monitors" = {
      services = [
        "dagster/mlops-dags/event_detection_run_release_training_job",
        "dagster/mlops-dags/event_detection_run_training_job",
        "dagster/mlops-dags/event_detection_run_backfill_job",
        "dagster/mlops-dags/generic_cv_ml_training_run_training_job",
        "dagster/mlops-dags/obx_to_event_conversion_job",
      ],
      critical_handle = var.team_metadata.computer_vision.critical_handle,
      datadog_team_id = var.team_metadata.computer_vision.datadog_team_id,
    }
    "infrastructure_monitors" = {
      services = [
        "dagster/realtime-dags/bq_snapshots_job",
      ],
      critical_handle = var.team_metadata.catch_all.critical_handle,
      datadog_team_id = var.team_metadata.catch_all.datadog_team_id,
    }
  }
  jobs_to_exclude = [
    "dagster/data-warehouse/*",
    "dagster/realtime-dags/shadow_forecast_job",
  ]
}

# This ensures that the monitoring sensors are running as frequently as they should. These sensors are resposible for sending events to Datadog, so if they are not running, we will not receive any events.
resource "datadog_monitor_json" "dagster_monitors_running_rate" {
  monitor = jsonencode(
    {
      message = <<EOT
                    [See sensors in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/sensors)
                    ${var.team_metadata.catch_all.critical_handle}
                EOT
      name    = "Dagster: Monitor sensor {{ service.name }} running below expected rate"
      options = {
        enable_logs_sample       = false
        groupby_simple_monitor   = false
        include_tags             = true
        new_group_delay          = 60
        notification_preset_name = "hide_all"
        notify_audit             = false
        on_missing_data          = "default"
        thresholds = {
          critical = 5
        }
      }
      priority = null
      query    = <<EOT
        events("source:my_apps env:${var.environment} service:(dagster/realtime-dags/run_monitor OR dagster/realtime-dags/instigator_monitor)").rollup("count").by("service").last("5m") < 5
      EOT
      tags     = ["team:${var.team_metadata.catch_all.datadog_team_id}", "env:${var.environment}"]
      type     = "event-v2 alert"
    }
  )
}

# This is a catch-all for all monitors that are not explicitly assigned to a team (or to the catchall). The alert is sent to the catchall team, it states that the service is missing an explicitly defined monitor.
resource "datadog_monitor_json" "auto_recover_dagster_health_monitor_failure_last_5_min_catchall" {
  monitor = jsonencode(
    {
      message = <<EOT
                    {{#is_exact_match "event.tags.dagster_definition_type" "job"}}
                    [See failure in Dagster UI]({{event.attributes.dagster_webserver_host}}/runs/{{event.attributes.run_id}}).

                    Run ID: {{event.attributes.run_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "sensor"}}
                    [See sensors in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/sensors).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "schedule"}}
                    [See schedules in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/schedules).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}
                    Please add an explicit monitor for this service to prevent this alert from firing in the general channel.
                    ${var.team_metadata.catch_all.warning_handle}
                EOT
      name    = "Dagster catch-all: One or more failures in last 5 minutes for {{service.name}}, this service is missing an explicitly defined monitor"
      options = {
        enable_logs_sample       = false
        groupby_simple_monitor   = false
        include_tags             = true
        new_group_delay          = 60
        notification_preset_name = "hide_all"
        notify_audit             = false
        on_missing_data          = "default"
        thresholds = {
          critical = 1
        }
      }
      priority = null
      query = <<EOT
        events("source:my_apps service:dagster* status:error env:${var.environment} -service:(${
      join(" OR ", concat(
        local.jobs_to_exclude,
        local.dagster_proportional_health_monitors["forecasting_monitors"].services,
        local.dagster_proportional_health_monitors["computer_vision_monitors"].services,
        local.dagster_proportional_health_monitors["infrastructure_monitors"].services,
        local.dagster_non_recoverable_health_monitors["forecasting_monitors"].services,
        local.dagster_non_recoverable_health_monitors["computer_vision_monitors"].services,
        local.dagster_non_recoverable_health_monitors["infrastructure_monitors"].services
    ))})").rollup("count").by("service").last("5m") >= 1
      EOT
    tags = [
      "team:${var.team_metadata.catch_all.datadog_team_id}",
      "env:${var.environment}"
    ]
    type = "event-v2 alert"
  }
)
}

# Alerts for non-recoverable *scheduled* job. This means that each failure opens a new alert and will not recover automatically.
resource "datadog_monitor_json" "no_auto_recover_scheduled_health_monitor_failure_last_15_min" {
  for_each = local.dagster_non_recoverable_health_monitors
  monitor = jsonencode(
    {
      message = <<EOT
                    {{#is_alert}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "job"}}
                    [See failure in Dagster UI]({{event.attributes.dagster_webserver_host}}/runs/{{event.attributes.run_id}}).

                    Run ID: {{event.attributes.run_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "sensor"}}
                    [See sensors in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/sensors).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "schedule"}}
                    [See schedules in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/schedules).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    ${each.value.critical_handle}
                    {{/is_alert}}
                EOT
      name    = "Dagster (${each.key}): One or more failures in last 15 minutes for scheduled job {{service.name}}"
      options = {
        enable_logs_sample       = false
        groupby_simple_monitor   = false
        include_tags             = true
        new_group_delay          = 60
        notification_preset_name = "hide_all"
        notify_audit             = false
        on_missing_data          = "default"
        thresholds = {
          critical = 1
        }
      }
      priority = null
      query    = "events(\"source:my_apps status:error env:${var.environment} service:(${join(" OR ", each.value.services)}) dagster_instigation_type:schedule\").rollup(\"count\").by(\"service,message\").last(\"15m\") >= 1",
      tags = [
        "team:${each.value.datadog_team_id}",
        "env:${var.environment}"
      ]
      type = "event-v2 alert"
    }
  )
}

# Alerts for proportional health monitors. This means that the alert is sent when the error rate is over 50% in the last 15 minutes.
resource "datadog_monitor_json" "dagster_health_monitor_error_over_50_percent" {
  for_each = local.dagster_proportional_health_monitors
  monitor = jsonencode(
    {
      message = <<EOT
                    {{#is_exact_match "event.tags.dagster_definition_type" "job"}}
                    [See failure in Dagster UI]({{event.attributes.dagster_webserver_host}}/runs/{{event.attributes.run_id}}).

                    Run ID: {{event.attributes.run_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "sensor"}}
                    [See sensors in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/sensors).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    {{#is_exact_match "event.tags.dagster_definition_type" "schedule"}}
                    [See schedules in Dagster UI]({{event.attributes.dagster_webserver_host}}/overview/schedules).

                    Tick ID: {{event.attributes.tick_id}}
                    {{/is_exact_match}}

                    ${each.value.critical_handle}
                EOT
      name : "Dagster (${each.key}): Error rate over 50% in last 15 minutes for {{service.name}}",
      options : {
        thresholds : {
          critical : 0.5
        },
        enable_logs_sample : false,
        notify_audit : false,
        on_missing_data : "default",
        include_tags : true,
        variables : [
          {
            data_source : "events",
            name : "query",
            indexes : [
              "*"
            ],
            compute : {
              aggregation : "count"
            },
            group_by : [
              {
                facet : "service",
                limit : 100,
                sort : {
                  order : "desc",
                  aggregation : "count"
                }
              }
            ],
            search : {
              query : "source:my_apps status:error env:${var.environment} service:(${join(" OR ", each.value.services)})"
            },
            storage : "hot"
          },
          {
            data_source : "events",
            name : "query1",
            indexes : [
              "*"
            ],
            compute : {
              aggregation : "count"
            },
            group_by : [
              {
                facet : "service",
                limit : 100,
                sort : {
                  order : "desc",
                  aggregation : "count"
                }
              }
            ],
            search : {
              query : "source:my_apps status:ok env:${var.environment} service:(${join(" OR ", each.value.services)})"
            },
            storage : "hot"
          }
        ],
        new_group_delay : 60,
        notification_preset_name : "hide_all",
        groupby_simple_monitor : false,
        silenced : {}
      },
      query : "formula(\"default_zero(query) / (default_zero(query) + default_zero(query1))\").last(\"15m\") >= 0.5",
      tags : [
        "team:${each.value.datadog_team_id}",
        "env:${var.environment}"
      ],
      type = "event-v2 alert"
    }
  )
}
