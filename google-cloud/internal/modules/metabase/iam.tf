locals {
  metabase_sa_roles = [
    "roles/container.serviceAgent",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/secretmanager.secretAccessor",
    "roles/bigquery.jobUser"
  ]
}

resource "google_service_account" "metabase_sa" {
  account_id   = "metabase-sa"
  project      = var.project_id
  display_name = "Metabase Service Account"
}

// These roles are needed for the Metabase SA
resource "google_project_iam_member" "metabase_roles" {
  for_each = toset(local.metabase_sa_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.metabase_sa.email}"
}

# This block adds the service account as a Workload Identity User.
# The ${var.project}.svc.id.goog bit indicates that it is a Workflow Identity namespace
# and the bit in [...] is the name of the Kubernetes service account we want to allow to 
# be bound to this.
# Note - it's ok to set this up before the ksa (Kubernetes Service Account) is created. It doesn't
# validate that the ksa exists
# See:
# * https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity#authenticating_to
# * https://discuss.hashicorp.com/t/gcp-iam-role-to-service-binding/13358

resource "google_service_account_iam_binding" "metabase_wi_iam" {
  service_account_id = google_service_account.metabase_sa.name
  role               = "roles/iam.workloadIdentityUser"

  members = [
    "serviceAccount:${var.project_id}.svc.id.goog[${var.metabase_namespace}/${var.metabase_ksa}]"
  ]
}
