variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "network_id" {
  description = "Network id for the cloud SQL to use"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "db_tier" {
  description = "The tier for the sql instance."
  type        = string
  default     = "db-g1-small"
}

// required
variable "db_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_13"
}

variable "db_authorized_networks" {
  description = "A list of authorized networks that are allowed to access cloudsql"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "db_backup_configuration" {
  description = "The backup_configuration settings subblock for the database settings"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = string
    point_in_time_recovery_enabled = bool
  })
  default = {
    enabled                        = false
    start_time                     = null
    location                       = null
    point_in_time_recovery_enabled = false
  }
}

variable "db_additional_users" {
  description = "A list of users to be created in your cluster"
  type = list(object({
    name     = string
    password = string
  }))
  default = []
}

variable "db_secretname_admin_user_name" {
  description = "SecretManager Secret name where admin user name is stored"
  type        = string
}

variable "db_secretname_admin_user_password" {
  description = "SecretManager Secret name where admin users password is stored."
  type        = string
}

variable "metabase_ksa" {
  description = "Metabase's Kubernetes Service Account to bind to the Google Service Account via Workload Identity."
  type        = string
  default     = "metabase-ksa-wi"
}

variable "metabase_namespace" {
  description = "Metabase's Kubernetes namespace."
  type        = string
  default     = "metabase"
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
