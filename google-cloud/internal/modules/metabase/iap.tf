### IAP settings. Leaving here for future use.
# Enable IAP
#resource "google_project_service" "iap_service" {
#  project = var.project_id
#  service = "iap.googleapis.com"
#}

# data "google_client_openid_userinfo" "current_identity" {
# }

# Configure OAuth2 consent screen
# resource "google_iap_brand" "iap_brand" {
#  support_email     = data.google_client_openid_userinfo.current_identity.email
#   application_title = "Cloud IAP protected Application"
#   project           = var.project_id
#   # depends_on        = [google_project_service.iap_service]
# }

# resource "google_iap_client" "metabase_iap_client" {
#   display_name  = "Metabase Auth"
#   brand         =  google_iap_brand.iap_brand.name
# }