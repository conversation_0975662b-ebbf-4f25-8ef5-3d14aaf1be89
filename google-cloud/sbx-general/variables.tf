variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}
