/******************************************
	Demo Buckets per customer
 *****************************************/

module "customer_demo_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  count                       = length(var.customer_list)
  bucket_name                 = "${var.environment}-customer-${element(var.customer_list, count.index)}-ml-demos"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  iam_members                 = var.customer_data_bucket_iam_members
  labels                      = var.labels
}
/******************************************
	Inference Models
 *****************************************/

module "inference_models" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-inference-models"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}

/******************************************
	Training Data
 *****************************************/

module "training_data" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-ml-training-data"
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}
