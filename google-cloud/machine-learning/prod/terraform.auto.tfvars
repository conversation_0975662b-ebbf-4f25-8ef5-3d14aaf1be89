/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

region         = "us-central1"
project_id     = "prod-ml-2fc132"
project_number = "21999772831"
environment    = "prod"


labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-machine-learning"
  "vanta-contains-user-data" = "true"
  "vanta-contains-ephi"      = "true"
  "vanta-no-alert"           = "it-stores-data-from-prod-environment"
}

inference_server_instance_count = 2
inference_server_instance_type  = "e2-highcpu-4"
inference_server_image          = "ubuntu-1804-lts"
inference_server_zone           = "us-central1-a"

inference_server_gpu_instance_count = 1
inference_server_gpu_instance_type  = "a2-highgpu-1g"
inference_server_gpu_image          = "projects/ml-images/global/images/c1-deeplearning-tf-2-6-cu110-v20211011-debian-10"
inference_server_gpu_disk_size      = 100


training_server_instance_count = 1
training_server_instance_type  = "a2-highgpu-1g"
training_server_image          = "projects/ml-images/global/images/c1-deeplearning-tf-2-6-cu110-v20211011-debian-10"
training_server_zone           = "us-central1-a"
training_server_disk_size      = 250

# sub network used by all the vms
subnetwork_project = "prod-network-bfb30f"
subnetwork         = "prod-central1-compute"

/******************************************
 Customer data bucket Resource Related Variables
 *****************************************/

customer_list = [
  "northbay"
]

customer_data_bucket_iam_members = [{
  role   = "roles/storage.admin"
  member = "group:<EMAIL>"
}]
