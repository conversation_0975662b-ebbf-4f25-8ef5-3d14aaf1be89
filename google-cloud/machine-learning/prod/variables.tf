/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "project_number" {
  description = "GCP Project number corresponding to the project_id"
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

# Variables for inference machines
variable "inference_server_instance_count" {
  description = "Number of inference instances to start"
  type        = number
}

variable "inference_server_instance_type" {
  description = "GCP instance type for inference machines"
  type        = string
}

variable "inference_server_zone" {
  description = "Zone to place instances in"
  type        = string
}

variable "inference_server_image" {
  description = "Image to use for inference server"
  type        = string
}

variable "inference_server_gpu_instance_count" {
  description = "Number of inference instances with GPUs to start"
  type        = number
}

variable "inference_server_gpu_instance_type" {
  description = "GCP instance type for inference machines with a GPU"
  type        = string
}

variable "inference_server_gpu_image" {
  description = "Image to use for inference server with a GPU"
  type        = string
}

variable "inference_server_gpu_disk_size" {
  description = "Size of boot disk for inference server with a GPU"
  type        = number
}

# Variables for training machine
variable "training_server_instance_count" {
  description = "Number of training instances to start"
  type        = number
}

variable "training_server_instance_type" {
  description = "GCP instance type for training machines"
  type        = string
}

variable "training_server_zone" {
  description = "Zone to place instances in"
  type        = string
}

variable "training_server_image" {
  description = "Image to use for inference server"
  type        = string
}

variable "training_server_disk_size" {
  description = "Size of boot disk for training server"
  type        = number
}

variable "subnetwork_project" {
  description = "The project ID where the desired subnetwork is provisioned"
}

variable "subnetwork" {
  description = "The name of the subnetwork to deploy instances into"
}

/******************************************
	GCS Bucket Related Variables
 *****************************************/
variable "customer_list" {
  description = "The names of customers to create buckets for"
  type        = list(string)
  default     = []
}

variable "customer_data_bucket_iam_members" {
  description = "The list of IAM members to grant permissions on the bucket."
  type = list(object({
    role   = string
    member = string
  }))
  default = []
}

variable "clearml_self_hosted_api_key" {
  description = "ClearML API Key (Self Hosted)"
  type        = string
}

variable "clearml_self_hosted_api_secret" {
  description = "ClearML API Secret (Self Hosted)"
  type        = string
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "encord_api_key" {
  description = "Encord Api Secret"
  type        = string
}
