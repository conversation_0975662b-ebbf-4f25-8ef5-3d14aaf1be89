module "event-updates" {
  source = "../modules/event-updates"

  project_id        = var.project_id
  environment       = var.environment
  labels            = var.labels
  deployment_suffix = ""
  dataflow_sa_email = local.dataflow_sa
  event_model_sa    = local.realtime_event_model_sa
}

module "event-updates-shadow" {
  source = "../modules/event-updates"

  project_id        = var.project_id
  environment       = var.environment
  labels            = var.labels
  deployment_suffix = "-shadow"
  dataflow_sa_email = local.dataflow_sa
  event_model_sa    = local.realtime_event_model_shadow_sa
}
