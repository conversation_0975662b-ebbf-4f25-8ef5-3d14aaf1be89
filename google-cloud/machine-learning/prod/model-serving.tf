module "model_serving" {
  source                       = "../modules/model-serving"
  gke_project_id               = local.gke_project_id
  inference_models_bucket_name = module.inference_models.name
  k8s_service_accounts = [
    {
      namespace            = "model-static-start-offset",
      service_account_name = "model-static-start-offset"
    },
    {
      namespace            = "model-static-start-offset",
      service_account_name = "model-static-start-offset-shadow"
    },
    {
      namespace            = "model-turnover",
      service_account_name = "model-turnover"
    },
    {
      namespace            = "model-turnover",
      service_account_name = "model-turnover-shadow"
    },
    {
      namespace            = "model-dynamic-case-end",
      service_account_name = "model-dynamic-case-end"
    },
    {
      namespace            = "model-dynamic-case-end",
      service_account_name = "model-dynamic-case-end-shadow"
    },
    {
      namespace            = "model-case-slotting",
      service_account_name = "model-case-slotting"
    },
    {
      namespace            = "model-event-model-forecasts",
      service_account_name = "model-event-model-forecasts",
    },
    {
      namespace            = "model-event-model-forecasts",
      service_account_name = "model-event-model-forecasts-shadow",
    },
    {
      namespace            = "forecast-combiner",
      service_account_name = "forecast-combiner"
    },
    {
      namespace            = "forecast-combiner",
      service_account_name = "forecast-combiner-shadow"
    },
    {
      namespace            = "apella-yolov5",
      service_account_name = "apella-yolov5"
    },
    {
      namespace            = "resnet-embedder",
      service_account_name = "resnet-embedder"
    },
    {
      namespace            = "model-bayesian-case-duration",
      service_account_name = "model-bayesian-case-duration"
    },
    {
      namespace            = "model-bayesian-case-duration",
      service_account_name = "model-bayesian-case-duration-pre-populate"
    },
    {
      namespace            = "model-bayesian-case-duration-schedule",
      service_account_name = "model-bayesian-case-duration-schedule"
    },
    {
      namespace            = "model-bayesian-case-duration-schedule",
      service_account_name = "model-bayesian-case-duration-schedule-shadow"
    },
    {
      namespace            = "model-bayesian-case-duration-schedule",
      service_account_name = "model-bayesian-case-duration-schedule-shadow-hp"
    },
  ]
  authorized_network          = data.terraform_remote_state.prod_vpc.outputs.network.id
  redis_memory_size_gb        = 2
  shadow_redis_memory_size_gb = 1
  data_platform_project_id    = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_id
}
