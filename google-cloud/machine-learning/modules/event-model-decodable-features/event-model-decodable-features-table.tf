#
# Define the table in BigQuery for event model decodable features
#
resource "google_bigquery_table" "event_model_decodable_features_table" {
  project    = var.project_id
  dataset_id = "${var.environment}_realtime"
  table_id   = "event_model_decodable_features"
  time_partitioning {
    type  = "DAY"
    field = "time_window_start_utc"
  }
  clustering = ["room_id"]
  schema     = <<-SCHEMA
    [
      {
        "name": "room_id",
        "type": "STRING",
        "description": "room id"
      },
      {
        "name": "time_window_start_utc",
        "type": "TIMESTAMP",
        "description": "Start time of the time window for which features were computed, in UTC"
      },
      {
        "name": "time_window_end_utc",
        "type": "TIMESTAMP",
        "description": "End time of the time window for which features were computed, in UTC"
      },
      {
        "name": "updated_time",
        "type": "TIMESTAMP",
        "description": "Time when this record was last updated in Decodable, in UTC"
      },
      {
        "name": "variant",
        "type": "STRING",
        "description": "Identifier for the decodable pipeline variant used to generate these features"
      },
      {
        "name": "float_features",
        "type": "RECORD",
        "mode": "REPEATED",
        "description": "List of single-value float features",
        "fields": [
          {
            "name": "name",
            "type": "STRING",
            "description": "Name of the feature"
          },
          {
            "name": "value",
            "type": "FLOAT",
            "description": "Value of the feature"
          }
        ]
      },
      {
        "name": "float_array_features",
        "type": "RECORD",
        "mode": "REPEATED",
        "description": "List of array-valued float features",
        "fields": [
          {
            "name": "name",
            "type": "STRING",
            "description": "Name of the feature"
          },
          {
            "name": "value",
            "type": "FLOAT",
            "mode": "REPEATED",
            "description": "Array of values for the feature"
          }
        ]
      },
      {
        "name": "subscription_name",
        "type": "STRING",
        "description": "Name of the PubSub subscription that delivered this message"
      },
      {
        "name": "publish_time",
        "type": "TIMESTAMP",
        "description": "Time when the message was published to PubSub, in UTC"
      },
      {
        "name": "attributes",
        "type": "JSON",
        "description": "Additional metadata attributes associated with the message"
      },
      {
        "name": "message_id",
        "type": "STRING",
        "description": "Unique identifier for the PubSub message"
      }
    ]
    SCHEMA
} 