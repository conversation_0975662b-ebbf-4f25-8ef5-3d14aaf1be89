variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "monitor_channel" {
  description = "The slack or incident.io channel to send monitoring alerts to"
}

variable "shadow_monitor_channel" {
  description = "The slack or incident.io channel to send shadow monitoring alerts to"
  nullable    = true
  default     = ""
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
