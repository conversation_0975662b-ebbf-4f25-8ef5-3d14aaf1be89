module "shadow_static_start_offset_model_monitors" {
  # This a hack to ensure that the module is only created if the shadow_monitor_channel is provided.
  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Static Start Offset Model"
  service_id   = "model-static-start-offset-shadow"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_many" : 5000,
    "/predict" : 5000,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "shadow_turnover_model_monitors" {

  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Turnover Model"
  service_id   = "model-turnover-shadow"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_many_cases" : 750,
    "/predict" : 750,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "shadow_forecast_combiner_monitors" {
  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Forecast Combiner"
  service_id   = "forecast-combiner-shadow"
  playbook_url = "https://www.notion.so/apella/Forecast-Production-Issues-182af54ad37a80a39e74f510c3f943df?pvs=4"

  endpoint_p75_latency_configurations = {
    "/predict_for_site" : 10000,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "shadow_event_model_forecasts_monitors" {
  # This a hack to ensure that the module is only created if the shadow_monitor_channel is provided.
  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Event Model Forecasts"
  service_id   = "model-event-model-forecasts-shadow"
  playbook_url = "TBD"

  pod_restarts_count_critical = 2
  endpoint_p75_latency_configurations = {
    "/predict_many" : 750,
    "/predict" : 750,
  }

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "shadow_dynamic_case_end_model_monitors" {
  # This a hack to ensure that the module is only created if the shadow_monitor_channel is provided.
  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Pythia (Dynamic Case End Model)"
  service_id   = "model-dynamic-case-end-shadow"
  playbook_url = "TBD"

  pod_restarts_count_critical = 2
  endpoint_p75_latency_configurations = {
    "/predict_many" : 5000,
    "/predict" : 5000,
  }

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "shadow_bayesian_case_duration_schedule_model_monitors" {
  # This a hack to ensure that the module is only created if the shadow_monitor_channel is provided.
  for_each = toset(
    var.shadow_monitor_channel == "" ? [] : [
      "shadow_case_duration_model_monitors"
    ]
  )
  source = "../model-service-monitors"

  service_name = "Shadow Bayesian Case Duration Schedule Model"
  service_id   = "model-bayesian-case-duration-schedule-shadow"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_case" : 4000,
    "/predict_many_cases" : 4000,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.shadow_monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}
