module "static_start_offset_model_monitors" {
  source = "../model-service-monitors"

  service_name = "Static Start Offset Model"
  service_id   = "model-static-start-offset"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_many" : 5000,
    "/predict" : 5000,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "turnover_model_monitors" {
  source = "../model-service-monitors"

  service_name = "Turnover Model"
  service_id   = "model-turnover"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_many_cases" : 750,
    "/predict" : 750,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "forecast_combiner_monitors" {
  source = "../model-service-monitors"

  service_name = "Forecast Combiner"
  service_id   = "forecast-combiner"
  playbook_url = "https://www.notion.so/apella/Forecast-Production-Issues-182af54ad37a80a39e74f510c3f943df?pvs=4"

  endpoint_p75_latency_configurations = {
    "/predict_for_site" : 10000,
  }

  pod_restarts_count_critical = 2

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "event_model_forecasts_monitors" {
  source = "../model-service-monitors"

  service_name = "Event Model Forecasts"
  service_id   = "model-event-model-forecasts"
  playbook_url = "TBD"

  pod_restarts_count_critical = 2
  endpoint_p75_latency_configurations = {
    "/predict_many" : 750,
    "/predict" : 750,
  }

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "dynamic_case_end_model_monitors" {
  source = "../model-service-monitors"

  service_name = "Pythia (Dynamic Case End Model)"
  service_id   = "model-dynamic-case-end"
  playbook_url = "TBD"

  pod_restarts_count_critical = 2
  endpoint_p75_latency_configurations = {
    "/predict_many" : 5000,
    "/predict" : 5000,
  }

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "bayesian_case_duration_model_monitors" {
  source = "../model-service-monitors"

  service_name = "Bayesian Case Duration Model"
  service_id   = "model-bayesian-case-duration"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/v3/standalone/predict" : 4000,
    "/v3/standalone/sample" : 4000,
    "/predict_case" : 4000,
    "/predict_many_cases" : 4000,
  }

  pod_restarts_count_critical = 5

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel
  datadog_team_id = var.datadog_team_id

  error_percent_critical = 7
  error_percent_warning  = 5
}

module "bayesian_case_duration_schedule_model_monitors" {
  source = "../model-service-monitors"

  service_name = "Bayesian Case Duration Schedule Model"
  service_id   = "model-bayesian-case-duration-schedule"
  playbook_url = "TBD"

  endpoint_p75_latency_configurations = {
    "/predict_case" : 4000,
    "/predict_many_cases" : 4000,
  }

  pod_restarts_count_critical = 5

  environment     = var.environment
  project_id      = var.project_id
  monitor_channel = var.monitor_channel

  error_percent_critical = 7
  error_percent_warning  = 5

  datadog_team_id = var.datadog_team_id
}
