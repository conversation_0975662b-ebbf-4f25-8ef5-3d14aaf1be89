#
# To write to BigQuery, we need a schema
#
resource "google_pubsub_schema" "event_prediction_event_update_schema" {
  name       = "event_update_schema_v2${var.deployment_suffix}"
  type       = "AVRO"
  definition = <<EOT
{
  "name": "event_update",
  "type": "record",
  "fields" : [
      { "name" : "org_id" , "type" : "string" },
      { "name" : "site_id" , "type" : "string" },
      { "name" : "room_id" , "type" : "string" },
      { "name" : "event_id" , "type" : "string" },
      { "name" : "event_name" , "type" : "string" },
      { "name" : "event_timestamp" , "type": "string" },
      { "name" : "process_timestamp" , "type": "string" },
      { "name" : "confidence" , "type" : "float" },
      { "name" : "update_action" , "type" : "string" },
      { "name" : "model_version", "type": "string" },
      {
        "name" : "labels",
        "type" : "string",
        "default" : "[]"
      }
  ]
}
EOT
}

#
# This topic will hold all event updates from the event predictor service
#
resource "google_pubsub_topic" "event_predictor_event_updates" {
  name   = "${var.environment}-event-prediction-event-updates${var.deployment_suffix}"
  labels = var.labels

  schema_settings {
    schema   = "projects/${var.project_id}/schemas/${google_pubsub_schema.event_prediction_event_update_schema.name}"
    encoding = "JSON"
  }
}

# Give Realtime Event Model SA permission to publish to the topic
resource "google_pubsub_topic_iam_member" "realtime_event_model_publishes_event_updates" {
  role   = "roles/pubsub.publisher"
  member = var.event_model_sa.member
  topic  = google_pubsub_topic.event_predictor_event_updates.name
}

#
# And we want to copy all of what is in this topic to big query
# so we need a subscription
#
resource "google_pubsub_subscription" "event_prediction_event_update_subscription" {
  name    = "prediction_event_updates_to_bigquery_subscription${var.deployment_suffix}"
  topic   = google_pubsub_topic.event_predictor_event_updates.id
  project = var.project_id

  bigquery_config {
    table            = "${var.project_id}:${var.environment}_realtime.event_prediction_event_updates${var.deployment_suffix}"
    use_topic_schema = true
  }
}

#
# And we need permission to read from this subscription
#
resource "google_pubsub_subscription_iam_member" "reads_event_predictor_event_updates" {
  member       = "serviceAccount:${var.dataflow_sa_email}"
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.event_prediction_event_update_subscription.id
}


#
# we need to define the table in bigquery
#
resource "google_bigquery_table" "event_prediction_event_updates_table" {
  project    = var.project_id
  dataset_id = "${var.environment}_realtime"
  table_id   = "event_prediction_event_updates${var.deployment_suffix}"

  # Configure clustering on site_id
  clustering = ["site_id"]

  # Partition the table by `event_timestamp` TIMESTAMP column
  time_partitioning {
    type  = "DAY"
    field = "event_timestamp"
  }

  schema = <<-SCHEMA
    [
      {
        "name": "org_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "site_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "room_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "event_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "event_name",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "event_timestamp",
        "type": "TIMESTAMP",
        "mode": "REQUIRED"
      },
      {
        "name": "process_timestamp",
        "type": "TIMESTAMP",
        "mode": "REQUIRED"
      },
      {
        "name": "confidence",
        "type": "FLOAT",
        "mode": "REQUIRED"
      },
      {
        "name": "update_action",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "model_version",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "labels",
        "type": "JSON",
        "mode": "NULLABLE"
      }
    ]
    SCHEMA
}
