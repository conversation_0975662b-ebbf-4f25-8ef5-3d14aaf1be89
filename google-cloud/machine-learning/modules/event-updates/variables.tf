variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["prod"], var.environment)
    error_message = "Environment must be prod."
  }
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "deployment_suffix" {
  type        = string
  description = "deployment suffix, such as '-canary'"
}

variable "dataflow_sa_email" {
  description = "The service account to run the dataflow jobs as"
}

variable "event_model_sa" {
  description = "The service account to publish to both prod and shadow event_updates topics"
}