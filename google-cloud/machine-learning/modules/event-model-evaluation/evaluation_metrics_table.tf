
resource "google_bigquery_table" "event_model_evaluation_metrics_table" {
  project    = var.project_id
  dataset_id = "${var.environment}_ml_analytics"
  table_id   = "event_model_evaluation_metrics"

  # Partition the table by `run_date` TIMESTAMP column
  time_partitioning {
    type  = "DAY"
    field = "run_timestamp"
  }

  schema = <<-SCHEMA
    [
      {
        "name": "run_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "run_timestamp",
        "type": "TIMESTAMP",
        "mode": "REQUIRED"
      },
      {
        "name": "event_type_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "metric_name",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "metric_variant",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "metric_value",
        "type": "FLOAT",
        "mode": "REQUIRED"
      },
      {
        "name": "room_id",
        "type": "STRING",
        "mode": "NULLABLE"
      }
    ]
    SCHEMA
}