
resource "google_bigquery_table" "event_model_evaluation_training_runs_table" {
  project    = var.project_id
  dataset_id = "${var.environment}_ml_analytics"
  table_id   = "event_model_training_runs"

  # Partition the table by `run_timestamp` TIMESTAMP column
  time_partitioning {
    type  = "DAY"
    field = "run_timestamp"
  }

  schema = <<-SCHEMA
    [
      {
        "name": "run_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "run_timestamp",
        "type": "TIMESTAMP",
        "mode": "REQUIRED"
      },
      {
        "name": "clearml_task_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "dataset_range_id",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "model_version",
        "type": "STRING",
        "mode": "REQUIRED"
      },
      {
        "name": "automated",
        "type": "BO<PERSON>EAN",
        "mode": "REQUIRED"
      }
    ]
    SCHEMA
}