#
# Define the table in BigQuery
#
resource "google_bigquery_table" "image_embeddings_table" {
  project    = var.project_id
  dataset_id = "${var.environment}_realtime"
  table_id   = "image_embeddings"
  time_partitioning {
    type  = "DAY"
    field = "frame_time"
  }
  clustering = ["room_id"]
  schema     = <<-SCHEMA
    [
      {
        "name": "guid",
        "type": "STRING"
      },
      {
        "name": "org_id",
        "type": "STRING"
      },
      {
        "name": "room_id",
        "type": "STRING"
      },
      {
        "name": "camera_id",
        "type": "STRING"
      },
      {
        "name": "source",
        "type": "STRING"
      },
      {
        "name": "frame_url",
        "type": "STRING"
      },
      {
        "name": "frame_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "model_name",
        "type": "STRING"
      },
      {
        "name": "model_version",
        "type": "STRING"
      },
      {
        "name": "embedding",
        "type": "FLOAT",
        "mode": "REPEATED"
      },
      {
        "name": "subscription_name",
        "type": "STRING"
      },
      {
        "name": "publish_time",
        "type": "TIMESTAMP"
      },
      {
        "name": "attributes",
        "type": "JSON"
      },
      {
        "name": "message_id",
        "type": "STRING"
      }
    ]
    SCHEMA
}
