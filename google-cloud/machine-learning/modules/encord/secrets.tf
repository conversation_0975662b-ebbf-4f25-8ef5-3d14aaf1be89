

resource "google_secret_manager_secret" "encord_api_key_secret" {
  secret_id = "${var.environment}-encord-api-key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "encord_api_key_secret_version" {
  secret      = google_secret_manager_secret.encord_api_key_secret.id
  secret_data = var.encord_api_key
}
