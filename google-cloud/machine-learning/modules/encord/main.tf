locals {
  bucket_name = "${var.environment}-ml-encord-data"
}

resource "google_storage_bucket" "encord_data" {
  name = local.bucket_name
  # storage class will default to STANDARD
  project                     = var.project_id
  location                    = "US"
  storage_class               = "MULTI_REGIONAL"
  uniform_bucket_level_access = true
  labels                      = var.labels
  cors {
    origin          = var.encord_cors_origins
    method          = var.encord_cors_methods
    response_header = var.encord_cors_response_header
    max_age_seconds = var.encord_cors_max_age_seconds
  }
} 