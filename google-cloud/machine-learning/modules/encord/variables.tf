variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "region" {
  type        = string
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "encord_api_key" {
  description = "Encord Api Secret"
  type        = string
}

variable "service_account_emails" {
  description = "Service account email"
  type        = list(string)
}

variable "encord_service_account_email" {
  description = "Email of the Encord service account"
  type        = string
  default     = "<EMAIL>"
}

variable "mlops_dags_service_account_email" {
  description = "Email of the MLOps Dags service account"
  type        = string
}

variable "labels" {
  description = "Labels to apply to the bucket"
  type        = map(string)
  default     = {}
}

variable "encord_cors_origins" {
  description = "List of origins allowed for CORS"
  type        = list(string)
  default     = ["https://app.us.encord.com", "https://api.us.encord.com"]
}

variable "encord_cors_methods" {
  description = "List of HTTP methods allowed for CORS"
  type        = list(string)
  default     = ["GET"]
}

variable "encord_cors_response_header" {
  description = "List of response headers allowed for CORS"
  type        = list(string)
  default     = ["Content-Type"]
}

variable "encord_cors_max_age_seconds" {
  description = "Maximum age in seconds for CORS preflight requests"
  type        = number
  default     = 3600
}