locals {
  roleListEncordSA = [
    "roles/storage.objectViewer"
  ]
  roleListDagsterMlopsSA = [
    "roles/storage.objectViewer",
    "roles/storage.objectCreator",
    "roles/storage.objectAdmin"
  ]
}

// Grant the service accounts access to the secrets
resource "google_secret_manager_secret_iam_member" "encord_api_key" {
  for_each  = toset(var.service_account_emails)
  project   = var.project_id
  secret_id = google_secret_manager_secret.encord_api_key_secret.id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${each.value}"
}

// Grant Encord service account access to the storage bucket
resource "google_storage_bucket_iam_member" "encord_sampling_data_member_private" {
  for_each = toset(local.roleListEncordSA)
  bucket   = google_storage_bucket.encord_data.name
  role     = each.value
  member   = "serviceAccount:${var.encord_service_account_email}"
}

// Grant MLOps Dags service account access to the storage bucket
resource "google_storage_bucket_iam_member" "dagster_sampling_data_member" {
  for_each = toset(local.roleListDagsterMlopsSA)
  bucket   = google_storage_bucket.encord_data.name
  role     = each.value
  member   = "serviceAccount:${var.mlops_dags_service_account_email}"
}
