variable "gke_project_id" {
  type        = string
  description = "GCP Project ID hosting the GKE cluster"
}

variable "data_platform_project_id" {
  type        = string
  description = "GCP Project ID where the ml-services SA is created"
}

variable "k8s_service_accounts" {
  description = "A list of k8s service account that can impersonate as the ml-services SA"
  type = list(object({
    namespace            = string
    service_account_name = string
  }))
}

variable "inference_models_bucket_name" {
  type        = string
  description = "The name of the bucket where inference models are stored"
}

variable "authorized_network" {
  description = "The authorized network that can connect to Redis"
  type        = string
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "labels" {
  description = "Default labels for cache instance"
  type        = map(string)
  default     = {}
}

variable "redis_memory_size_gb" {
  description = "The size of the Redis instance"
  type        = number
  default     = 1
}

variable "shadow_redis_memory_size_gb" {
  description = "The size of the shadow Redis instance"
  type        = number
  default     = 1
  nullable    = true
}
