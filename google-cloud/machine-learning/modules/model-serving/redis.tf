resource "google_redis_instance" "model-serving-memory-cache" {
  display_name   = "ML Model Serving Memory Cache Instance"
  name           = "model-serving-memory-cache"
  location_id    = var.zone
  labels         = var.labels
  tier           = "STANDARD_HA"
  memory_size_gb = var.redis_memory_size_gb

  authorized_network = var.authorized_network
  connect_mode       = "PRIVATE_SERVICE_ACCESS" # Required for networks in other projects

  redis_version = "REDIS_7_0"
}

resource "google_redis_instance" "model-serving-memory-cache-shadow" {
  // This allows us to only create the shadow Redis instance if the variable is set
  // otherwise, it will not create the resource
  count          = var.shadow_redis_memory_size_gb != null ? 1 : 0
  display_name   = "ML Model Serving Memory Cache Instance for Shadow Models"
  name           = "model-serving-memory-cache-shadow"
  location_id    = var.zone
  labels         = var.labels
  tier           = "STANDARD_HA"
  memory_size_gb = var.shadow_redis_memory_size_gb

  authorized_network = var.authorized_network
  connect_mode       = "PRIVATE_SERVICE_ACCESS" # Required for networks in other projects

  redis_version = "REDIS_7_0"
}
