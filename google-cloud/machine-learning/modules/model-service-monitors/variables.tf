variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "service_name" {
  type        = string
  description = "Human readable service name"
}

variable "service_id" {
  type        = string
  description = "The service id as appears in the datadog metrics"
}

variable "monitor_channel" {
  description = "The slack or incident-io channel to send monitoring alerts to"
}

variable "error_percent_critical" {
  type        = number
  default     = 2
  description = "The percent of 5xx errors to trigger the alert"
}

variable "error_percent_warning" {
  type        = number
  default     = 1
  description = "The percent of 5xx errors to trigger the warning"
}

variable "endpoint_p75_latency_configurations" {
  type        = map(number)
  description = "Endpoints + max p75 latencies"
}

variable "pod_restarts_count_critical" {
  type        = number
  description = "The number of pod restarts required to trigger the alert"
}

variable "playbook_url" {
  type        = string
  description = "The URL to the playbook for the alert"
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
