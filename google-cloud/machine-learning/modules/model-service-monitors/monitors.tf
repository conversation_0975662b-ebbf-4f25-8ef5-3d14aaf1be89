locals {
  datadog_tags = [
    "team:${var.datadog_team_id}"
  ]
  sum_period = "last_15m"
}

module "model_errors_percent_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  type              = "metric alert"
  name              = "${var.service_name} Error % (${local.sum_period})"
  renotify_statuses = toset(["alert", "warn"])
  notify_no_data    = false

  query              = "sum(${local.sum_period}):sum:http_requests.count{env:${var.environment},service:${var.service_id},status:5*}.as_count() * 100 / sum:http_requests.count{env:${var.environment},service:${var.service_id}}.as_count() > ${var.error_percent_critical}"
  threshold_critical = var.error_percent_critical
  threshold_warning  = var.error_percent_warning
  message            = <<END_MESSAGE
  {{#is_alert}} ${var.service_name} is returning 5* status codes. {{/is_alert}}
  {{#is_recovery}} ${var.service_name} has recovered {{/is_recovery}}
  ${var.monitor_channel}
  playbook: ${var.playbook_url}
  END_MESSAGE
  tags               = local.datadog_tags
}

module "model_latency_monitor" {
  for_each = var.endpoint_p75_latency_configurations

  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  type               = "metric alert"
  name               = "${var.service_name} `${each.key}` P75 Latency"
  renotify_statuses  = toset(["alert", "warn"])
  notify_no_data     = false
  query              = "percentile(${local.sum_period}):1000 * p75:http_request_duration_seconds{service:${var.service_id} , env:${var.environment}, endpoint:${each.key}} > ${each.value}"
  threshold_critical = each.value
  message            = <<END_MESSAGE
  {{#is_alert}} P75 ${var.service_name} runtime has exceeded ${each.value} milliseconds for endpoint ${each.key} {{/is_alert}}
  {{#is_recovery}} ${var.service_name} has recovered {{/is_recovery}}
  playbook: ${var.playbook_url}
  ${var.monitor_channel}
  playbook: ${var.playbook_url}
  END_MESSAGE
  tags               = local.datadog_tags
}

module "model_pod_restarts" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  type               = "metric alert"
  name               = "${var.service_name} Pod restarts"
  renotify_statuses  = toset(["alert", "warn"])
  notify_no_data     = false
  query              = "sum(last_5m):monotonic_diff(sum:kubernetes.containers.restarts{env:${var.environment} , service:${var.service_id}}) > ${var.pod_restarts_count_critical}"
  threshold_critical = var.pod_restarts_count_critical
  message            = <<END_MESSAGE
  {{#is_alert}} ${var.service_name} pods are restarting frequently. {{/is_alert}}
  {{#is_recovery}} ${var.service_name} has recovered {{/is_recovery}}
  ${var.monitor_channel}
  playbook: ${var.playbook_url}
  END_MESSAGE
  tags               = local.datadog_tags
}
