
# Create a bucket for the IO manager
module "dagster-io-manager-bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = local.io_manager_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment
  labels                      = var.labels
}

resource "google_storage_bucket_iam_member" "dagster-reads-io-manager" {
  bucket     = local.io_manager_bucket_name
  role       = "roles/storage.admin"
  member     = "serviceAccount:${google_service_account.service_account.email}"
  depends_on = [module.dagster-io-manager-bucket]
}
