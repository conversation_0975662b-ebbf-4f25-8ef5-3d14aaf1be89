variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "region" {
  type        = string
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "dagster_location_name" {
  type        = string
  description = "The Name of your Dagster location. Typically the same as your Dagster github repo name. This will be used to name the Dagster SA and the Dagster IO Manager bucket."
}

variable "gke_project_id" {
  type        = string
  description = "GCP project ID of the GKE clsuter where Dagster jobs run"
}

variable "self_hosted_ksa_namespace" {
  type        = string
  description = "GKE namespace for the Kubernetes Service Account for self-hosted Dagster"
  default     = "dagster"
}

variable "bucket_list" {
  description = "List of bucket names the Dagster SA may access"
  type        = list(string)
  default     = []
}

variable "roles" {
  description = "List of roles to grant the Dagster SA"
  type        = list(string)
  default     = []
}
