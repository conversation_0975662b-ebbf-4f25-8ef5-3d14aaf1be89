locals {
  roleListCloudFunction = [
    "roles/bigquery.jobUser",
    "roles/bigquery.dataOwner",
    "roles/bigquery.readSessionUser",
    "roles/storage.objectAdmin"
  ]
  roleListDataFlowSA = [
    "roles/bigquery.jobUser",
    "roles/bigquery.dataOwner",
    "roles/bigquery.readSessionUser",
    "roles/bigquery.admin"
  ]
  roleListAutoMLModelTrainingSA = [
    "roles/bigquery.jobUser",
    "roles/bigquery.dataOwner",
    "roles/bigquery.readSessionUser",
    "roles/secretmanager.secretAccessor",
    "roles/logging.logWriter",
    "roles/aiplatform.user",
    "roles/storage.objectCreator",
    "roles/storage.objectViewer"
  ]
  roleListrtedgemodelSA = [
    "roles/aiplatform.user"
  ]
  roleListMetabaseBqViewerSA = [
    "roles/bigquery.dataViewer"
  ]
  roleListDagsterMlopsSA = [
    "roles/storage.objectViewer",
    "roles/storage.objectCreator",
    "roles/storage.objectAdmin"
  ]
}


# Confluent service account
resource "google_service_account" "confluent_sa" {
  account_id   = "confluent"
  display_name = "Confluent service account"
}

# Service account to invoke cloud functions
resource "google_service_account" "invoke_functions_sa" {
  account_id   = "invokefunctions"
  display_name = "Invoke cloud functions service account"
}

resource "google_project_iam_member" "dev_invoke_functions_sa" {
  project = var.project_id
  role    = "roles/cloudfunctions.invoker"
  member  = "serviceAccount:${google_service_account.invoke_functions_sa.email}"
}

resource "google_service_account" "cloud_functions_sa" {
  account_id   = "cloudfunctions"
  display_name = "Cloud functions service account"
}

resource "google_project_iam_member" "dev_cloud_functions_sa" {
  for_each = toset(local.roleListCloudFunction)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.cloud_functions_sa.email}"
}

# Dataflow access to bigquery
resource "google_project_iam_member" "dev_dataflow_group_roles" {
  for_each = toset(local.roleListDataFlowSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.dataflow_sa}"
}

# Allow dagster_realtime_dags_sa to read/write forecasting models in ml buckets
resource "google_storage_bucket_iam_member" "dagster_realtime_dags_manage_inference_bucket" {
  bucket = module.inference_models.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${local.dagster_realtime_dags_sa.email}"
}


resource "google_storage_bucket_iam_member" "dagster_training_data_member" {
  for_each = toset(local.roleListDagsterMlopsSA)
  bucket   = module.training_data.name
  role     = each.value
  member   = "serviceAccount:${module.mlops_dags_dagster_location.service_account.email}"
}

# AutoML Model Training SA

resource "google_service_account" "automl_model_training_sa" {
  account_id   = "automlmodel"
  display_name = "AutoML Image model service account"
}

resource "google_project_iam_member" "dev_automl_model_training_sa" {
  for_each = toset(local.roleListAutoMLModelTrainingSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.automl_model_training_sa.email}"
}

# rtedgemodel-sa access to AI endpoint
resource "google_project_iam_member" "rtedgemodel_group_roles" {
  for_each = toset(local.roleListrtedgemodelSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.rtedgemodel_sa}"
}

# Realtime Processing Services uses ai platform
resource "google_project_iam_member" "realtime_processing_services_uses_aiplatform" {
  project = var.project_id
  role    = "roles/aiplatform.user"
  member  = "serviceAccount:${local.realtime_services_sa.email}"
}
# Realtime Processing Services fetches the transformer model from this bucket
resource "google_storage_bucket_iam_member" "realtime_processing_services_fetches_models" {
  bucket = module.inference_models.name
  role   = "roles/storage.objectViewer"
  member = "serviceAccount:${local.realtime_services_sa.email}"
}

# Service account for Metabase so it can access BigQuery. Metabase requires the SA to be in the
# same project as the BQ instance
resource "google_service_account" "metabase_bq_viewer_sa" {
  account_id   = "metabase-bq-viewer"
  display_name = "Inference server service account"
}

# Allow Metabase to access BigQuery
resource "google_project_iam_member" "metabase_bq_viewer_group_roles" {
  for_each = toset(local.roleListMetabaseBqViewerSA)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.metabase_sa}"
}

# Allow API Server to access resources
resource "google_project_iam_member" "api_server_group_roles" {
  for_each = toset([
    # Allow access to BigQuery for object detection
    "roles/bigquery.dataViewer"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_member" "external_secrets_roles" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${local.external_secrets_sa.email}"
}

#
# Roles granted to project PubSub service accounts in order to allow BigQuery subscriptions to
# topics created in that project.
#
locals {
  pubsub_service_accounts = [
    local.data_platform_pubsub_sa,
    "service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
  ]
  pubsub_roles = [
    "roles/bigquery.dataEditor",
    "roles/bigquery.metadataViewer"
  ]
  pubsub_role_assignments = [
    for pair in setproduct(local.pubsub_service_accounts, local.pubsub_roles) : {
      service_account = pair[0]
      role            = pair[1]
    }
  ]
}

resource "google_project_iam_member" "pubsub_roles" {
  for_each = {
    for assignment in local.pubsub_role_assignments : "${assignment.service_account}.${assignment.role}" => assignment
  }
  project = var.project_id
  role    = each.value.role
  member  = "serviceAccount:${each.value.service_account}"
}

resource "google_service_account" "ml_inference_sa" {
  account_id   = "ml-inference"
  display_name = "Service account for Seldon inference servers"
}

resource "google_storage_bucket_iam_member" "ml_inference_membership" {
  bucket = module.inference_models.name
  role   = "roles/storage.admin"
  member = "serviceAccount:${google_service_account.ml_inference_sa.email}"
}

resource "google_service_account_iam_member" "internal_gke_ml_inference_gsa_workload_identity" {
  service_account_id = google_service_account.ml_inference_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${local.gke_project_id}.svc.id.goog[ml-inference/ml-inference]"
}

# Give mlops-dags SA full access to the snapshot dataset. Allows the SA to create snapshots with
# expiration dates.
resource "google_bigquery_dataset_iam_member" "snapshot_owner_mlops_dags" {
  for_each = toset([
    module.bigquery_dev_ml_cv_dataset_snapshots.bigquery_dataset.dataset_id,
    module.bigquery_dev_realtime_snapshots.bigquery_dataset.dataset_id,
  ])
  dataset_id = each.value
  role       = "roles/bigquery.dataOwner"
  member     = "serviceAccount:${module.mlops_dags_dagster_location.service_account.email}"
}

resource "google_project_iam_member" "ray_sa" {
  for_each = toset([
    "roles/bigquery.jobUser",
    "roles/bigquery.dataEditor",
    "roles/bigquery.readSessionUser",
    "roles/storage.objectViewer"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${local.ray_sa_email}"
}
