/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

region         = "us-central1"
project_id     = "dev-ml-794354"
project_number = "267313437201"
environment    = "dev"


labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "dev-machine-learning"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-dev-environment"
}

inference_server_instance_count = 2
inference_server_instance_type  = "e2-highcpu-4"
inference_server_image          = "ubuntu-1804-lts"
inference_server_zone           = "us-central1-a"

inference_server_gpu_instance_count = 0
inference_server_gpu_instance_type  = "a2-highgpu-1g"
inference_server_gpu_image          = "projects/ml-images/global/images/c1-deeplearning-tf-2-6-cu110-v20211011-debian-10"
inference_server_gpu_disk_size      = 100

# sub network used by all the vms
subnetwork_project = "nonprod-network-9523f0"
subnetwork         = "nonprod-central1-compute"
