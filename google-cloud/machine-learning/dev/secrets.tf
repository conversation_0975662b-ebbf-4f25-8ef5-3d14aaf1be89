resource "google_secret_manager_secret" "clearml_self_hosted_api_key" {
  secret_id = "${var.environment}-clearml-self-hosted-api-key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "clearml_self_hosted_api_key_version" {
  secret      = google_secret_manager_secret.clearml_self_hosted_api_key.id
  secret_data = var.clearml_self_hosted_api_key
}

resource "google_secret_manager_secret" "clearml_self_hosted_api_secret" {
  secret_id = "${var.environment}-clearml-self-hosted-api-secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "clearml_self_hosted_api_secret_version" {
  secret      = google_secret_manager_secret.clearml_self_hosted_api_secret.id
  secret_data = var.clearml_self_hosted_api_secret
}
