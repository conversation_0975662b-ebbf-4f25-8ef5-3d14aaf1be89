locals {
  bigquery_access = [
    {
      role          = "OWNER"
      user_by_email = "mlops-dags@${var.project_id}.iam.gserviceaccount.com"
    },
    {
      role          = "OWNER"
      special_group = "projectOwners"
    },
    {
      role          = "READER"
      user_by_email = local.dagster_self_hosted_sa.email
    }
  ]
}

module "bigquery_dev_ml_analytics" {
  source  = "app.terraform.io/apella/bigquery/google"
  version = "1.0.2"

  project_id     = var.project_id
  dataset_id     = "${var.environment}_ml_analytics"
  dataset_name   = "ML Analytics (${var.environment} environment)"
  dataset_labels = var.labels

  access = local.bigquery_access
}

module "bigquery_dev_ml_cv_dataset" {
  source  = "app.terraform.io/apella/bigquery/google"
  version = "1.0.2"

  project_id     = var.project_id
  dataset_id     = "${var.environment}_ml_cv_dataset"
  dataset_name   = "ML Computer Vision Labeled Datasets (${var.environment} environment)"
  dataset_labels = var.labels

  access = local.bigquery_access
}

module "bigquery_dev_ml_cv_dataset_snapshots" {
  source  = "app.terraform.io/apella/bigquery/google"
  version = "1.0.2"

  project_id     = var.project_id
  dataset_id     = "${var.environment}_ml_cv_dataset_snapshots"
  dataset_name   = "Snapshots for ML Computer Vision Labeled Datasets (${var.environment} environment)"
  dataset_labels = var.labels

  access = local.bigquery_access
}

module "bigquery_dev_realtime" {
  source  = "app.terraform.io/apella/bigquery/google"
  version = "1.0.2"

  project_id     = var.project_id
  dataset_id     = "${var.environment}_realtime"
  dataset_name   = "Realtime predictions (${var.environment} environment)"
  dataset_labels = var.labels

  access = [
    {
      role          = "OWNER"
      user_by_email = "terraform-sa@${var.project_id}.iam.gserviceaccount.com"
    },
    {
      role          = "WRITER"
      user_by_email = google_service_account.confluent_sa.email
    },
    {
      role          = "OWNER"
      user_by_email = local.ray_sa_email
    },
    {
      role          = "OWNER"
      user_by_email = google_service_account.cloud_functions_sa.email
    },
    {
      role          = "READER"
      user_by_email = local.dagster_self_hosted_sa.email
    }
  ]
}

module "bigquery_dev_realtime_snapshots" {
  source  = "app.terraform.io/apella/bigquery/google"
  version = "1.0.2"

  project_id     = var.project_id
  dataset_id     = "${var.environment}_realtime_snapshots"
  dataset_name   = "Snapshots for ML Realtime Tables (${var.environment} environment)"
  dataset_labels = var.labels

  access = local.bigquery_access
}
