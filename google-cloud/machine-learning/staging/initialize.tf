/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-machine-learning-staging"
    }
  }
  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "google"
      version = "~> 5.0"
    }
    datadog = {
      source  = "DataDog/datadog"
      version = ">=3.25.0"
    }
  }
}

data "terraform_remote_state" "nonprod_vpc" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "staging_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-staging"
    }
  }
}

data "terraform_remote_state" "staging_internal" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-internal-staging"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "google_organization" "org" {
  domain = var.domain_name
}

locals {
  org_id                        = data.google_organization.org.org_id
  dataflow_sa                   = data.terraform_remote_state.nonprod_security.outputs.nonprod-dataflow-sa
  rtedgemodel_sa                = data.terraform_remote_state.nonprod_security.outputs.nonprod-rtedgemodel-sa
  realtime_services_sa          = data.terraform_remote_state.staging_data_platform.outputs.realtime_processing_services_sa
  image_embedding_outputs_topic = data.terraform_remote_state.staging_data_platform.outputs.image_embedding_outputs_topic_id
  metabase_sa                   = data.terraform_remote_state.staging_internal.outputs.metabase_sa_email
  dagster_realtime_dags_sa      = data.terraform_remote_state.staging_data_platform.outputs.dagster_realtime_dags_sa
  data_platform_pubsub_sa       = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
  gke_project_id                = data.terraform_remote_state.nonprod_project_factory.outputs.staging-internal-project.project_id
  dagster_self_hosted_sa        = data.terraform_remote_state.staging_internal.outputs.dagster_self_hosted_sa
  ray_sa_email                  = data.terraform_remote_state.staging_internal.outputs.ray_sa.email
}
