/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}


variable "project_id" {
  description = "GCP Project ID"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}



variable "ar_admin_groups" {
  description = "The names of the artificate registry admin group"
  type        = list(any)
  default     = []
}

variable "ar_pull_sas" {
  type        = list(string)
  description = "A list of GCP service accounts that will have permissions to pull images from registry."
  default     = []
}



variable "labels" {
  description = "The key/value labels for resources."
  type        = map(string)
  default     = {}
}


/*****************************************************************************
  Bastion Host
******************************************************************************/

variable "bastion_instance_type" {
  description = "Compute instance to use for bastion"
  type        = string
}

variable "bastion_zone" {
  description = "Zone to put bastion host in"
  type        = string
}

variable "cloudsqlproxy_dev" {
  description = "Configuration for dev cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_datastream_dev" {
  description = "Configuration for Datastream's dev cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_dev" {
  description = "Configuration for EHR dev cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_port_dev" {
  description = "Listening port for the dev EHR SQL proxy"
  type        = string
}

variable "cloudsqlproxy_ehr_staging" {
  description = "Configuration for EHR staging cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_staging_readonly" {
  description = "Configuration for the readonly EHR staging cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_port_staging" {
  description = "Listening port for the staging EHR SQL proxy"
  type        = string
}

variable "cloudsqlproxy_ehr_port_staging_readonly" {
  description = "Listening port for the readonly staging EHR SQL proxy"
  type        = string
}

variable "cloudsqlproxy_datastream_port_dev" {
  description = "Listening port for the dev Datastream SQL proxy"
  type        = string
}

variable "cloudsqlproxy_datastream_staging" {
  description = "Configuration for Datastream's staging cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_datastream_port_staging" {
  description = "Listening port for the staging Datastream SQL proxy"
  type        = string
}

variable "cloudsqlproxy_staging" {
  description = "Configuration for staging cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_feature_store_dev" {
  description = "Configuration for dev feature store cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_feature_store_staging" {
  description = "Configuration for staging feature store cloudsqlproxy"
  type        = string
}

variable "health_check_source_ranges" {
  description = "Google Cloud's health check probe IP ranges"
  type        = list(string)
  default     = ["**********/16", "***********/22"]
}
