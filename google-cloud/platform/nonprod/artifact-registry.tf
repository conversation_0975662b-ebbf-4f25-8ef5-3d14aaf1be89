
# TODO: Remove the nonprod docker registry
# https://apella.atlassian.net/browse/PROD-1963
resource "google_artifact_registry_repository" "nonprod" {
  provider      = google-beta
  project       = var.project_id
  location      = var.region
  repository_id = "${var.environment}-docker-registry"
  description   = "nonprod artifact registry"
  format        = "DOCKER"
  labels        = var.labels
}

// Add github runner to Artifact Registry admins
locals {
  ar_admin_groups_with_gh = concat(
    var.ar_admin_groups,
    ["serviceAccount:${local.nonprod_gh_runner_sa}"]
  )
}

// Add to Artifact Registry Pullers
locals {
  artifact_registry_pullers = concat(
    var.ar_pull_sas,
    [
      # Dev
      "serviceAccount:${data.terraform_remote_state.dev_data_platform.outputs.edge_customer_uploader_sa.email}",

      # Staging
      "serviceAccount:${data.terraform_remote_state.staging_data_platform.outputs.edge_customer_uploader_sa.email}",
    ]
  )
}

data "google_iam_policy" "reg_policy" {
  binding {
    role    = "roles/artifactregistry.admin"
    members = local.ar_admin_groups_with_gh
  }

  binding {
    role    = "roles/artifactregistry.reader"
    members = local.artifact_registry_pullers
  }
}

resource "google_artifact_registry_repository_iam_policy" "reg_policy" {
  provider    = google-beta
  project     = var.project_id
  location    = var.region
  repository  = google_artifact_registry_repository.nonprod.name
  policy_data = data.google_iam_policy.reg_policy.policy_data
}
