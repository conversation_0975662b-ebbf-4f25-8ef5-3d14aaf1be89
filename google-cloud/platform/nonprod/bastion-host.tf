data "google_compute_image" "bastion_image" {
  family  = "ubuntu-2004-lts"
  project = "ubuntu-os-cloud"
}

resource "google_compute_address" "bastion-ip-address" {
  name         = "bastion-host"
  address_type = "INTERNAL"
  subnetwork   = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.id
}

resource "google_compute_instance" "bastion-host" {
  name         = "bastion-host"
  machine_type = var.bastion_instance_type
  zone         = var.bastion_zone
  labels       = var.labels

  boot_disk {
    initialize_params {
      image = data.google_compute_image.bastion_image.self_link
    }
  }

  network_interface {
    subnetwork_project = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.project
    subnetwork         = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.name
    network_ip         = google_compute_address.bastion-ip-address.address
  }

  metadata_startup_script = templatefile(
    "${path.module}/templates/instance-startup.sh",
    {
      cloudsqlproxy_dev                     = var.cloudsqlproxy_dev,
      cloudsqlproxy_datastream_dev          = var.cloudsqlproxy_datastream_dev
      cloudsqlproxy_datastream_port_dev     = var.cloudsqlproxy_datastream_port_dev
      cloudsqlproxy_datastream_staging      = var.cloudsqlproxy_datastream_staging
      cloudsqlproxy_datastream_port_staging = var.cloudsqlproxy_datastream_port_staging
      cloudsqlproxy_staging                 = var.cloudsqlproxy_staging
      cloudsqlproxy_feature_store_dev       = var.cloudsqlproxy_feature_store_dev
      cloudsqlproxy_feature_store_staging   = var.cloudsqlproxy_feature_store_staging
      cloudsqlproxy_ehr_dev                 = var.cloudsqlproxy_ehr_dev
      cloudsqlproxy_ehr_staging             = var.cloudsqlproxy_ehr_staging
      cloudsqlproxy_ehr_staging_readonly    = var.cloudsqlproxy_ehr_staging_readonly
    }
  )

  service_account {
    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    email = data.terraform_remote_state.nonprod_security.outputs.nonprod-bastion-host-sa
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }
}
