# TODO: come back and delete this when we cut over the references to the env-specific output below
output "cloudsqlproxy_datastream_port" {
  value       = var.cloudsqlproxy_datastream_port_dev
  description = "Listening port for the dev Datastream SQL proxy"
}

output "cloudsqlproxy_datastream_port_dev" {
  value       = var.cloudsqlproxy_datastream_port_dev
  description = "Listening port for the dev Datastream SQL proxy"
}

output "cloudsqlproxy_datastream_port_staging" {
  value       = var.cloudsqlproxy_datastream_port_staging
  description = "Listening port for the staging Datastream SQL proxy"
}

output "ehr_cloudsqlproxy_datastream_port_dev" {
  value       = var.cloudsqlproxy_ehr_port_dev
  description = "Listening port for the dev EHR SQL proxy"
}

output "ehr_cloudsqlproxy_datastream_port_staging" {
  value       = var.cloudsqlproxy_ehr_port_staging
  description = "Listening port for the staging EHR SQL proxy"
}

output "bastion_host_zone" {
  value       = var.bastion_zone
  description = "Zone of the bastion host"
}
