/**
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

module "github-action-runner" {
  source  = "app.terraform.io/apella/github-action-runners/google"
  version = "1.0.41"

  create_network           = false
  project_id               = var.project_id
  source_image_family      = var.source_image_family
  org_name                 = var.org_name
  gh_token                 = var.gh_token
  gh_labels                = var.gh_labels
  github_token_secret_name = "${var.instance_name}-gh-token"

  service_account    = local.nonprod_gh_runner_sa
  network_name       = var.network_name
  subnetwork_project = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.project
  subnet_name        = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.name
  instance_name      = var.instance_name
  target_size        = var.target_size
  instance_labels    = var.labels

  health_check = {
    type                = "http"
    port                = 8080
    request_path        = "/healthz"
    check_interval_sec  = 10
    timeout_sec         = 3
    healthy_threshold   = 2
    unhealthy_threshold = 3
    enable_logging      = true
    initial_delay_sec   = 10
    response            = "OK"
    host                = ""
    request             = ""
    proxy_header        = "NONE"
  }

}

module "github-action-runner-test" {
  source  = "app.terraform.io/apella/github-action-runners/google"
  version = "1.0.41"

  create_network           = false
  project_id               = var.project_id
  source_image_family      = var.source_image_family
  org_name                 = var.org_name
  gh_token                 = var.gh_token
  gh_labels                = var.gh_test_labels
  github_token_secret_name = "${var.test_instance_name}-gh-token"

  service_account    = local.nonprod_gh_runner_sa
  network_name       = var.network_name
  subnetwork_project = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.project
  subnet_name        = data.terraform_remote_state.gcp-network-nonprod.outputs.shared_us_central1.name
  instance_name      = var.test_instance_name
  target_size        = var.test_target_size
  instance_labels = merge(
    var.labels,
    {
      "vanta-description" = "nonprod-test-platform"
    }
  )

  health_check = {
    type                = "http"
    port                = 8080
    request_path        = "/healthz"
    check_interval_sec  = 10
    timeout_sec         = 3
    healthy_threshold   = 2
    unhealthy_threshold = 3
    enable_logging      = true
    initial_delay_sec   = 10
    response            = "OK"
    host                = ""
    request             = ""
    proxy_header        = "NONE"
  }
}

locals {
  roleListGithubRunner = [
    "roles/cloudsql.client",
    "roles/compute.instanceAdmin",
    "roles/iam.serviceAccountUser",
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/run.serviceAgent",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer"
  ]
}


#github runner service account
resource "google_project_iam_member" "github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.nonprod_gh_runner_sa}"
}

resource "google_logging_metric" "github_action_runner_test_unhealthy_instance" {
  name        = "github_action_runner_test_unhealthy_instance"
  description = "Counts instance group manager events where instances become UNHEALTHY"
  filter      = <<EOT
    resource.type="gce_instance_group_manager"
    logName="projects/${var.project_id}/logs/compute.googleapis.com%2Finstance_group_manager_events"
    jsonPayload.instanceHealthStateChange.detailedHealthState="UNHEALTHY"
    jsonPayload.instanceHealthStateChange.healthCheck="projects/${var.project_id}/global/healthChecks/nonprod-test-gh-runner-docker-http-healthcheck"
  EOT
  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
  }
}
