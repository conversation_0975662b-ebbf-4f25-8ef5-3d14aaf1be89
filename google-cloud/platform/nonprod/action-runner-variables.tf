variable "zone" {
  type        = string
  description = "The GCP zone to deploy instances into"
  default     = "us-central1-a"
}

variable "network_name" {
  type        = string
  description = "Name for the VPC network"
  default     = "gh-runner-network"
}

variable "create_network" {
  type        = bool
  description = "When set to true, VPC,router and NAT will be auto created"
  default     = false
}

variable "subnetwork_project" {
  type        = string
  description = "The ID of the project in which the subnetwork belongs. If it is not provided, the project_id is used."
  default     = ""
}

variable "subnet_ip" {
  type        = string
  description = "IP range for the subnet"
  default     = "**********/24"
}
variable "subnet_name" {
  type        = string
  description = "Name for the subnet"
  default     = "gh-runner-subnet"
}

variable "restart_policy" {
  type        = string
  description = "The desired Docker restart policy for the runner image"
  default     = "Always"
}

variable "org_name" {
  type        = string
  description = "Name of the org for the Github Action runner"
  default     = "Apella-Technology"
}

variable "gh_token" {
  type        = string
  description = "Github token that is used for generating Self Hosted Runner Token"
  sensitive   = true
}

variable "gh_labels" {
  type        = string
  description = "Github labels to apply to the runner following e.g. nonprod or nonprod,something,else"
}

variable "gh_test_labels" {
  type        = string
  description = "Github labels to apply to the test runner following e.g. nonprod-test or nonprod-test,something,else"
  default     = "nonprod-test"
}

variable "instance_name" {
  type        = string
  description = "The gce instance name"
  default     = "gh-runner"
}

variable "target_size" {
  type        = number
  description = "The number of runner instances"
  default     = 1
}

variable "test_instance_name" {
  type        = string
  description = "The gce instance name for test runner"
  default     = "nonprod-test-gh-runner-docker"
}

variable "test_target_size" {
  type        = number
  description = "The number of test runner instances"
  default     = 1
}

variable "additional_metadata" {
  type        = map(any)
  description = "Additional metadata to attach to the instance"
  default     = {}
}
variable "machine_type" {
  type        = string
  description = "The GCP machine type to deploy"
  default     = "n1-standard-4"
}

variable "cooldown_period" {
  description = "The number of seconds that the autoscaler should wait before it starts collecting information from a new instance."
  default     = 60
}

variable "source_image" {
  type        = string
  description = "Source disk image. If neither source_image nor source_image_family is specified, defaults to the latest public CentOS image."
  default     = ""
}
variable "source_image_family" {
  type        = string
  description = "Source image family. If neither source_image nor source_image_family is specified, defaults to the latest public Ubuntu image."
  default     = "ubuntu-1804-lts"
}
