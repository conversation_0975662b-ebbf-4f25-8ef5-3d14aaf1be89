/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "nonprod-platform-f24dad"
environment = "nonprod"


/******************************************
	artifactory registry Resource Related Variables
 *****************************************/
ar_admin_groups = [
  "group:<EMAIL>",
  "group:<EMAIL>"
]
ar_pull_sas = [
  # dev
  "serviceAccount:<EMAIL>",
  "serviceAccount:<EMAIL>", # Cloud run service agent account
  "serviceAccount:<EMAIL>",          # Dev internal GKE nodes SA

  # staging
  "serviceAccount:<EMAIL>",
  "serviceAccount:<EMAIL>", # Cloud run service agent account
  "serviceAccount:<EMAIL>",       # Staging internal GKE nodes SA

  # nonprod
  "serviceAccount:<EMAIL>"
]

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "nonprod-platform"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
  "vanta-no-alert"           = "it-stores-data-from-dev-environment"
}


/******************************************
	action runner variables
 *****************************************/
source_image_family = "ubuntu-2204-lts"
network_name        = ""
instance_name       = "nonprod-gh-runner-docker"
target_size         = 2
gh_labels           = "nonprod"
gh_test_labels      = "nonprod-test"
test_instance_name  = "nonprod-test-gh-runner-docker"
test_target_size    = 1


/******************************************
	bastion host variables
 *****************************************/

bastion_instance_type                   = "n1-standard-1"
bastion_zone                            = "us-central1-a"
cloudsqlproxy_dev                       = "dev-web-api-72f12b:us-central1:dev-postgres-01=tcp:3306"
cloudsqlproxy_datastream_port_dev       = 5432
cloudsqlproxy_datastream_dev            = "dev-web-api-72f12b:us-central1:dev-postgres-01=tcp:0.0.0.0:5432"
cloudsqlproxy_datastream_port_staging   = 5433
cloudsqlproxy_datastream_staging        = "staging-web-api-3efef9:us-central1:staging-postgres-02=tcp:0.0.0.0:5433"
cloudsqlproxy_staging                   = "staging-web-api-3efef9:us-central1:staging-postgres-02=tcp:3307"
cloudsqlproxy_feature_store_dev         = "dev-data-platform-439b4c:us-central1:dev-feature-store-01=tcp:3308"
cloudsqlproxy_feature_store_staging     = "staging-data-platform-6227fa:us-central1:staging-feature-store-01=tcp:3309"
cloudsqlproxy_ehr_dev                   = "dev-ehr:us-central1:ehr=tcp:0.0.0.0:3310"
cloudsqlproxy_ehr_port_dev              = 3310
cloudsqlproxy_ehr_staging               = "staging-ehr:us-central1:ehr=tcp:0.0.0.0:3311"
cloudsqlproxy_ehr_port_staging          = 3311
cloudsqlproxy_ehr_staging_readonly      = "staging-ehr:us-central1:ehr-replica-read-api=tcp:3312"
cloudsqlproxy_ehr_port_staging_readonly = 3312
