/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-platform-nonprod"
    }
  }
  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "google"
      version = ">= 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}


locals {
  nonprod_gh_runner_sa              = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  data_platform_tf_sa_email_dev     = data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.tf_sa_email
  data_platform_tf_sa_email_staging = data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.tf_sa_email
  shared_gh_runner_sa               = data.terraform_remote_state.prod_security.outputs.github-actions-shared-sa
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}

data "terraform_remote_state" "nonprod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "gcp-network-nonprod" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "dev_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-dev"
    }
  }
}

data "terraform_remote_state" "staging_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-staging"
    }
  }
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}
