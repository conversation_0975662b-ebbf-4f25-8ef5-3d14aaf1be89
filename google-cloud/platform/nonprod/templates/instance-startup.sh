#! /bin/bash

apt update
apt -y upgrade
apt -y autoremove

# Get cloud-sql-proxy
wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O /usr/bin/cloud_sql_proxy
chmod a+x /usr/bin/cloud_sql_proxy

mkdir /var/run/cloud-sql-proxy
mkdir /var/local/cloud-sql-proxy

# Add services
cat >/lib/systemd/system/cloudsqlproxy_dev.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_dev}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_datastream_dev.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for dev Datastream, accessible via host's private IP
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_datastream_dev}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_ehr_dev.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for EHR dev database
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_ehr_dev}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_datastream_staging.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for staging Datastream, accessible via host's private IP
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_datastream_staging}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_staging.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_staging}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_ehr_staging.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for EHR staging database
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_ehr_staging}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_ehr_staging_readonly.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for the readonly EHR staging database
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_ehr_staging_readonly}
Restart=always
StandardOutput=journal
User=root
EOL


systemctl daemon-reload
systemctl start cloudsqlproxy_dev
systemctl start cloudsqlproxy_datastream_dev
systemctl start cloudsqlproxy_ehr_dev
systemctl start cloudsqlproxy_datastream_staging
systemctl start cloudsqlproxy_staging
systemctl start cloudsqlproxy_ehr_staging
systemctl start cloudsqlproxy_ehr_staging_readonly
