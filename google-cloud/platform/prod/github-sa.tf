/**
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

locals {
  github_sa = data.terraform_remote_state.global_security.outputs.github-hosted-sa
  roleListGithub = [
    "roles/cloudbuild.builds.builder",
    "roles/storage.admin"
  ]
  roleListSharedGithub = concat(local.roleListGithub, [
    "roles/artifactregistry.repoAdmin"
  ])
}

#github runner service account
resource "google_project_iam_member" "github_roles" {
  for_each = toset(local.roleListGithub)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.github_sa}"
}

#########################################################
# The Github Hosted Service Account
#########################################################

resource "google_project_iam_member" "github_actions_shared_roles" {
  for_each = toset(local.roleListSharedGithub)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.shared_gh_runner_sa.email}"
}
