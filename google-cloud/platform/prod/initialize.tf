/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-platform-prod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

locals {
  prod_gh_runner_sa         = data.terraform_remote_state.prod_security.outputs.prod-github-runner-sa
  shared_gh_runner_sa       = data.terraform_remote_state.prod_security.outputs.github-actions-shared-sa
  data_platform_tf_sa_email = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.tf_sa_email
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}

data "terraform_remote_state" "gcp-network-prod" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-prod"
    }
  }
}


data "terraform_remote_state" "prod_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-prod"
    }
  }
}

data "terraform_remote_state" "dev_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-dev"
    }
  }
}

data "terraform_remote_state" "prod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}
