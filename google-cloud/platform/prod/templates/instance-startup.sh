#! /bin/bash

apt update
apt -y upgrade
apt -y autoremove

# Get cloud-sql-proxy

wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O /usr/bin/cloud_sql_proxy
chmod a+x /usr/bin/cloud_sql_proxy

mkdir /var/run/cloud-sql-proxy
mkdir /var/local/cloud-sql-proxy

# Add services
cat >/lib/systemd/system/cloudsqlproxy.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_read_only.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_read_only}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_feature_store.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_feature_store}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_feature_store_shadow.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_feature_store_shadow}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_datastream.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for Datastream, accessible via host's private IP
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_datastream}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_ehr.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for EHR prod database
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_ehr}
Restart=always
StandardOutput=journal
User=root
EOL

cat >/lib/systemd/system/cloudsqlproxy_ehr_readonly.service <<EOL
[Install]
WantedBy=multi-user.target

[Unit]
Description=Google Cloud Compute Engine SQL Proxy for the readonly EHR prod database
Requires=network.target
After=network.target

[Service]
Type=simple
WorkingDirectory=/usr/bin
ExecStart=/usr/bin/cloud_sql_proxy -dir=/var/run/cloud-sql-proxy -instances=${cloudsqlproxy_ehr_readonly}
Restart=always
StandardOutput=journal
User=root
EOL

systemctl daemon-reload
systemctl start cloudsqlproxy
systemctl start cloudsqlproxy_read_only
systemctl start cloudsqlproxy_feature_store
systemctl start cloudsqlproxy_feature_store_shadow
systemctl start cloudsqlproxy_datastream
systemctl start cloudsqlproxy_ehr
systemctl start cloudsqlproxy_ehr_readonly
