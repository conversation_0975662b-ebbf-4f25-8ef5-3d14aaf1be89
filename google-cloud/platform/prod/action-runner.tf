/**
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

module "github-action-runner" {
  source  = "app.terraform.io/apella/github-action-runners/google"
  version = "1.0.41"

  create_network           = false
  project_id               = var.project_id
  source_image_family      = var.source_image_family
  org_name                 = var.org_name
  gh_token                 = var.gh_token
  gh_labels                = var.gh_labels
  github_token_secret_name = "${var.instance_name}-gh-token"

  service_account    = local.prod_gh_runner_sa
  network_name       = var.network_name
  subnetwork_project = data.terraform_remote_state.gcp-network-prod.outputs.shared_us_central1.project
  subnet_name        = data.terraform_remote_state.gcp-network-prod.outputs.shared_us_central1.name
  instance_name      = var.instance_name
  target_size        = var.target_size
  instance_labels = merge(
    var.default_labels,
    {
      "vanta-no-alert" = "this-instance-autoscales-based-on-cpu-for-github-actions"
    }
  )

  health_check = {
    type                = "http"
    port                = 8080
    request_path        = "/healthz"
    check_interval_sec  = 10
    timeout_sec         = 3
    healthy_threshold   = 2
    unhealthy_threshold = 3
    enable_logging      = true
    initial_delay_sec   = 10
    response            = "OK"
    host                = ""
    request             = ""
    proxy_header        = "NONE"
  }
}

locals {
  roleListGithubRunner = [
    "roles/cloudsql.client",
    "roles/compute.instanceAdmin",
    "roles/iam.serviceAccountUser",
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/run.serviceAgent",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer"
  ]
}


# github runner service account
resource "google_project_iam_member" "github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.prod_gh_runner_sa}"
}
