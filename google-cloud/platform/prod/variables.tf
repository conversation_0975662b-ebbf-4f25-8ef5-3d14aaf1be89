/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}


variable "project_id" {
  description = "GCP Project ID"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "ar_admin_groups" {
  description = "The names of the artifact registry admin group"
  type        = list(any)
  default     = []
}

variable "ar_pull_sas" {
  type        = list(string)
  description = "A list of GCP service accounts that will have permissions to pull images from registry."
  default     = []
}

variable "default_labels" {
  description = "The default key/value labels for resources. May be overridden or amended by individual resources."
  type        = map(string)
  default     = {}
}

/*****************************************************************************
  Bastion Host
******************************************************************************/

variable "bastion_instance_type" {
  description = "Compute instance to use for bastion"
  type        = string
}

variable "bastion_zone" {
  description = "Zone to put bastion host in"
  type        = string
}

variable "cloudsqlproxy" {
  description = "Configuration for cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_read_only" {
  description = "Configuration for cloud api server cloudsqlproxy_read_only instance"
  type        = string
}

variable "cloudsqlproxy_ehr" {
  description = "Configuration for EHR cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_readonly" {
  description = "Configuration for the readonly EHR cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_ehr_port" {
  description = "Listening port for the EHR SQL proxy"
  type        = string
}

variable "cloudsqlproxy_ehr_port_readonly" {
  description = "Listening port for the readonly EHR SQL proxy"
  type        = string
}


variable "cloudsqlproxy_feature_store" {
  description = "Configuration for cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_feature_store_shadow" {
  description = "Configuration for cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_datastream" {
  description = "Configuration for Datastream's dev cloudsqlproxy"
  type        = string
}

variable "cloudsqlproxy_datastream_port" {
  description = "Listening port for the Datastream SQL proxy"
  type        = string
}

variable "health_check_source_ranges" {
  description = "Google Cloud's health check probe IP ranges"
  type        = list(string)
  default     = ["**********/16", "***********/22"]
}
