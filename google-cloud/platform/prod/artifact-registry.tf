locals {
  labels = merge(
    var.default_labels,
    {
      "vanta-owner" = "dorian"
  })
}

resource "google_service_account" "artifact_registry_repo_admin" {
  account_id   = "artifact-registry-repo-admin"
  display_name = "Service account for reading and writing from artifact registries"
}

resource "google_artifact_registry_repository" "prod" {
  provider      = google
  project       = var.project_id
  location      = var.region
  repository_id = "${var.environment}-docker-registry"
  description   = "prod artifact registry"
  format        = "DOCKER"
  labels        = local.labels
}

resource "google_artifact_registry_repository" "prod-python" {
  provider      = google
  project       = var.project_id
  location      = var.region
  repository_id = "${var.environment}-python-registry"
  description   = "Apella private Python artifact registry"
  format        = "PYTHON"
  labels        = local.labels
}

resource "google_artifact_registry_repository" "prod-npm" {
  provider      = google
  project       = var.project_id
  location      = var.region
  repository_id = "${var.environment}-npm-registry"
  description   = "prod node artifact registry"
  format        = "NPM"
  labels        = local.labels
}

resource "google_artifact_registry_repository" "prod-helm" {
  provider      = google
  project       = var.project_id
  location      = var.region
  repository_id = "${var.environment}-helm-registry"
  description   = "prod helm artifact registry"
  format        = "DOCKER"
}

// Add github runner to Artifact Registry admins
locals {
  ar_admin_groups_with_gh = concat(
    var.ar_admin_groups,
    ["serviceAccount:${local.prod_gh_runner_sa}"]
  )
}

// Add to Artifact Registry Pullers
locals {
  artifact_registry_pullers = concat(
    var.ar_pull_sas,
    [
      "serviceAccount:${data.terraform_remote_state.dev_data_platform.outputs.edge_customer_uploader_sa.email}",
      "serviceAccount:${data.terraform_remote_state.prod_data_platform.outputs.edge_customer_uploader_sa.email}",
    ]
  )
}

data "google_iam_policy" "reg_policy" {
  binding {
    role    = "roles/artifactregistry.admin"
    members = local.ar_admin_groups_with_gh
  }

  binding {
    role    = "roles/artifactregistry.reader"
    members = local.artifact_registry_pullers
  }

  binding {
    role = "roles/artifactregistry.repoAdmin"
    members = [
      "serviceAccount:${google_service_account.artifact_registry_repo_admin.email}"
    ]
  }
}

resource "google_artifact_registry_repository_iam_policy" "reg_policy" {
  provider    = google
  project     = var.project_id
  location    = var.region
  repository  = google_artifact_registry_repository.prod.name
  policy_data = data.google_iam_policy.reg_policy.policy_data
}

resource "google_artifact_registry_repository_iam_policy" "reg_policy_python" {
  provider    = google
  project     = var.project_id
  location    = var.region
  repository  = google_artifact_registry_repository.prod-python.name
  policy_data = data.google_iam_policy.reg_policy.policy_data
}

resource "google_artifact_registry_repository_iam_policy" "reg_policy_npm" {
  provider    = google
  project     = var.project_id
  location    = var.region
  repository  = google_artifact_registry_repository.prod-npm.name
  policy_data = data.google_iam_policy.reg_policy.policy_data
}

resource "google_artifact_registry_repository_iam_policy" "reg_policy_helm" {
  provider    = google
  project     = var.project_id
  location    = var.region
  repository  = google_artifact_registry_repository.prod-helm.name
  policy_data = data.google_iam_policy.reg_policy.policy_data
}

resource "google_project_iam_member" "ar_repo_admin_roles" {
  for_each = toset(["roles/iam.serviceAccountTokenCreator"])
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.artifact_registry_repo_admin.email}"
}

/*
 * Snyk security scanner
 */

locals {
  snyk_roles = [
    "roles/artifactregistry.reader",
    "roles/browser",
  ]
}

resource "google_service_account" "snyk_scanner" {
  account_id   = "snyk-scanner"
  display_name = "Service account for Snyk security scanner"
}

resource "google_project_iam_member" "snyk_roles" {
  for_each = toset(local.snyk_roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.snyk_scanner.email}"
}
