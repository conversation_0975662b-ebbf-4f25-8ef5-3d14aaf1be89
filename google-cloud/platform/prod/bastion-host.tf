data "google_compute_image" "bastion_image" {
  family  = "ubuntu-2004-lts"
  project = "ubuntu-os-cloud"
}

resource "google_compute_address" "bastion-ip-address" {
  name         = "bastion-host"
  address_type = "INTERNAL"
  subnetwork   = data.terraform_remote_state.gcp-network-prod.outputs.shared_us_central1.id
}

resource "google_compute_instance" "bastion-host" {
  name         = "bastion-host"
  machine_type = var.bastion_instance_type
  zone         = var.bastion_zone
  labels = merge(
    var.default_labels,
    {
      "vanta-owner" = "dorian"
  })

  boot_disk {
    initialize_params {
      image = data.google_compute_image.bastion_image.self_link
    }
  }

  network_interface {
    subnetwork_project = data.terraform_remote_state.gcp-network-prod.outputs.shared_us_central1.project
    subnetwork         = data.terraform_remote_state.gcp-network-prod.outputs.shared_us_central1.name
    network_ip         = google_compute_address.bastion-ip-address.address
  }

  metadata_startup_script = templatefile(
    "${path.module}/templates/instance-startup.sh",
    {
      cloudsqlproxy                      = var.cloudsqlproxy
      cloudsqlproxy_read_only            = var.cloudsqlproxy_read_only
      cloudsqlproxy_feature_store        = var.cloudsqlproxy_feature_store
      cloudsqlproxy_feature_store_shadow = var.cloudsqlproxy_feature_store_shadow
      cloudsqlproxy_datastream           = var.cloudsqlproxy_datastream
      cloudsqlproxy_ehr                  = var.cloudsqlproxy_ehr
      cloudsqlproxy_ehr_readonly         = var.cloudsqlproxy_ehr_readonly
    }
  )

  service_account {
    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    email = data.terraform_remote_state.prod_security.outputs.prod-bastion-host-sa
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }
}
