/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "prod-platform-29b5cb"
environment = "prod"


/******************************************
	artifactory registry Resource Related Variables
 *****************************************/
ar_admin_groups = [
  "group:<EMAIL>",
  "group:<EMAIL>",
  # Prod Github Runner
  "serviceAccount:<EMAIL>"
]

# XXX These accounts are granted read permission in every Artifact Registry repository (docker,
# python, helm, npm) even though most only need access to one of them
ar_pull_sas = [
  # dev
  "serviceAccount:<EMAIL>",             # ArgoCD service account to pull Helm files
  "serviceAccount:<EMAIL>",  # ArgoCD image updater service account to pull Helm files
  "serviceAccount:<EMAIL>",          # Load Generator
  "serviceAccount:<EMAIL>", # dev-web-api Cloud Run service agent account
  "serviceAccount:<EMAIL>",                        # dev-web-api Cloud Build service agent account
  "serviceAccount:<EMAIL>",          # Dev internal GKE nodes SA
  "serviceAccount:<EMAIL>",  # Edge ArgoCD image updater service

  # staging
  "serviceAccount:<EMAIL>",            # ArgoCD service account to pull Helm files
  "serviceAccount:<EMAIL>", # ArgoCD image updater service account to pull Helm files
  "serviceAccount:<EMAIL>",         # Load Generator
  "serviceAccount:<EMAIL>",   # staging-web-api Cloud Run service agent account
  "serviceAccount:<EMAIL>",                          # staging-web-api Cloud Build service agent account
  "serviceAccount:<EMAIL>",         # Staging internal GKE nodes SA

  # nonprod github runner
  "serviceAccount:<EMAIL>",

  # prod
  "serviceAccount:<EMAIL>",             # ArgoCD service account to pull Helm files
  "serviceAccount:<EMAIL>",  # ArgoCD image updater service account to pull Helm files
  "serviceAccount:<EMAIL>",  # prod-web-api Cloud Run service agent account
  "serviceAccount:<EMAIL>",                         # prod-web-api Cloud Build service agent account
  "serviceAccount:<EMAIL>",          # prod internal GKE nodes SA
  "serviceAccount:<EMAIL>", # Edge ArgoCD image updater service
  "serviceAccount:<EMAIL>",                  # Dagster and event model training VMs

  # Miscellaneous cloud build service accounts
  "serviceAccount:<EMAIL>", # prod-platform
  "serviceAccount:<EMAIL>", # dev-data-platform
  "serviceAccount:<EMAIL>", # prod-data-platform
  "serviceAccount:<EMAIL>", # staging-data-platform
]

default_labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-platform"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
}

/******************************************
	action runner variables
 *****************************************/
source_image_family = "ubuntu-2204-lts"
network_name        = ""
subnetwork_project  = "prod-network-bfb30f"
subnet_name         = "prod-central1-shared-compute"
instance_name       = "prod-gh-runner-docker"
target_size         = 1
gh_labels           = "prod,artifact"

/******************************************
	bastion host variables
 *****************************************/

bastion_instance_type              = "n1-standard-1"
bastion_zone                       = "us-central1-a"
cloudsqlproxy_read_only            = "prod-web-api-7f60bf:us-central1:prod-postgres-01-replica-analytics=tcp:3308"
cloudsqlproxy                      = "prod-web-api-7f60bf:us-central1:prod-postgres-01=tcp:3306"
cloudsqlproxy_feature_store        = "prod-data-platform-027529:us-central1:prod-feature-store-01=tcp:3307"
cloudsqlproxy_feature_store_shadow = "prod-data-platform-027529:us-central1:prod-feature-store-01-shadow=tcp:3309"
cloudsqlproxy_datastream_port      = 5432
cloudsqlproxy_datastream           = "prod-web-api-7f60bf:us-central1:prod-postgres-01=tcp:0.0.0.0:5432"
cloudsqlproxy_ehr                  = "prod-ehr:us-central1:ehr=tcp:0.0.0.0:3310"
cloudsqlproxy_ehr_port             = 3310
cloudsqlproxy_ehr_readonly         = "prod-ehr:us-central1:ehr-replica-read-api=tcp:3311"
cloudsqlproxy_ehr_port_readonly    = 3311
