locals {
  accessor_members = [
    for email in var.launch_darkly_secret_accessors : "serviceAccount:${email}"
  ]
}

resource "google_secret_manager_secret" "launch_darkly_sdk_key_secret" {
  secret_id = "${var.environment}-launch-darkly-sdk-key"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}

resource "google_secret_manager_secret_version" "launch_darkly_sdk_key_version" {
  secret = google_secret_manager_secret.launch_darkly_sdk_key_secret.id

  secret_data = var.launch_darkly_sdk_key
}


resource "google_secret_manager_secret_iam_member" "launch_darkly_sdk_key_iams" {
  for_each  = toset(local.accessor_members)
  secret_id = google_secret_manager_secret.launch_darkly_sdk_key_secret.id
  role      = "roles/secretmanager.secretAccessor"
  member    = each.value
}

