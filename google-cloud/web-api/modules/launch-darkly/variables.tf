variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  type        = string
  description = "GCP Region"
}
variable "launch_darkly_sdk_key" {
  description = "Launch Darkly SDK key"
  type        = string
  sensitive   = true
}

variable "launch_darkly_secret_accessors" {
  description = "Launch Darkly secret accessors"
  type        = list(string)
}
