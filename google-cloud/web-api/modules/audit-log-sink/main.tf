resource "google_logging_project_bucket_config" "audit-log-bucket" {
  bucket_id      = "${var.service_name}-audit-log-bucket"
  description    = "${var.service_name} Audit Log Bucket"
  location       = "us-central1"
  project        = var.project_id
  retention_days = 3650 # 10 years
}

resource "google_logging_project_sink" "audit-log-sink" {
  name        = "${var.service_name}-audit-log-sink"
  description = "${var.service_name} Audit Log Sink"
  destination = "logging.googleapis.com/${google_logging_project_bucket_config.audit-log-bucket.id}"

  filter                 = "jsonPayload.component = \"audit-log\" resource.labels.service_name = \"${var.service_name}\""
  unique_writer_identity = true
}
