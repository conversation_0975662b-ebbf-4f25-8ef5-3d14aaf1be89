resource "google_cloud_run_service" "cubejs" {
  name     = "cubejs-${var.environment}"
  location = var.region
  project  = var.project_id

  template {
    spec {
      containers {
        # A valid image is required, even though we will immediately overwrite it.  So just provide this one.
        image = "us-docker.pkg.dev/cloudrun/container/hello"
        env {
          name  = "SOURCE"
          value = "remote"
        }
        env {
          name  = "TARGET"
          value = "home"
        }
        resources {
          limits = {
            cpu    = "1000m"
            memory = "512Mi"
          }
        }
      }
      container_concurrency = 80
      timeout_seconds       = 30
      service_account_name  = google_service_account.cubejs_sa.email
    }

    metadata {
      annotations = {
        generated-by                              = "magic-modules"
        "run.googleapis.com/launch-stage"         = "BETA"
        "autoscaling.knative.dev/minScale"        = "1"
        "autoscaling.knative.dev/maxScale"        = "2"
        "run.googleapis.com/vpc-access-egress"    = "private-ranges-only"
        "run.googleapis.com/vpc-access-connector" = var.serverless_vpc_connector_id
        "run.googleapis.com/cloudsql-instances"   = var.sql_instance_connection_name
      }
    }

  }

  metadata {
    annotations = {
      generated-by                      = "magic-modules"
      "run.googleapis.com/launch-stage" = "BETA"
    }
    labels = merge(var.labels, var.datadog_labels)
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
  autogenerate_revision_name = true

  lifecycle {
    ignore_changes = [
      metadata[0].annotations,             # Contains properties about the client that created it, like the gcloud cli version
      template[0].metadata[0].annotations, # Contains the same as metadata[0].annotations
      template[0].spec[0].containers       # Contains the docker image and the environment vars
    ]
  }
}

# Auth will be handled internally by Auth0, not IAM.
resource "google_cloud_run_service_iam_member" "cloud_run_noauth" {
  project  = var.project_id
  location = var.region
  service  = google_cloud_run_service.cubejs.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}
