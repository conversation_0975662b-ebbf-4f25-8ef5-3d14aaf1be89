locals {
  # Datadog provider doesn't seem to allow importing slack integration. Therefore we infer the channel name.
  datadog_slack_channel = "@slack-${var.slack_channel_name}"
  datadog_tags          = ["team:orca-orion"]
  latency_threshold_ms  = 800
}

module "p95_latency_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  type = "metric alert"
  name = "CubeJS: P95 latency"
  // Latency metrics for CubeJS seem to be emitted every 30 mins or more.
  query              = "avg(last_12h):avg:gcp.run.request_latencies.p95{configuration_name:${var.configuration_name}} > ${local.latency_threshold_ms}"
  threshold_critical = local.latency_threshold_ms
  message            = "Average P95 latency above ${local.latency_threshold_ms}ms for the past 12 hours.\n\nInspect slow queries [here](https://app.datadoghq.com/logs?query=configuration_name%3A%22${var.configuration_name}%22%20%40duration%3A%3E${local.latency_threshold_ms * 1e6}).\n\n${local.datadog_slack_channel}"
  tags               = local.datadog_tags
}
