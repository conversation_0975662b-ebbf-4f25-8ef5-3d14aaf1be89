variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "configuration_name" {
  type        = string
  description = "Configuration name"
}

#cloud run
variable "serverless_vpc_connector_id" {
  description = "ID for vpc serverless connector"
  type        = string
}

variable "sql_instance_connection_name" {
  type        = string
  description = "Instance Connection Name for postgres SQL database."
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "datadog_labels" {
  description = "Labels to designate a resource to be monitored by Datadog."
  type        = map(string)
  default = {
    "datadog" = "monitored"
  }
}

variable "standard_ssl_policy_name" {
  type        = string
  description = "Standard SSL Policy name"
}

variable "cloudflare_configuration" {
  type = object({
    api_token  = string
    account_id = string
  })
  description = "Cloudflare Provider Configuration"
  sensitive   = true
}


variable "cubejs_namespace" {
  type        = string
  description = "Kubernetes namespace for cubejs"
  default     = "cubejs"
}

variable "cubejs_ksa" {
  type        = string
  description = "Kubernetes Service Account for cubejs"
  default     = "cubejs"
}

variable "internal_project_id" {
  type        = string
  description = "The internal cluster google project id"
}
