locals {
  https_prefix = "${var.environment}-https-cubejs"
  domain       = var.environment == "prod" ? "cubejs" : "cubejs.${var.environment}"
  full_domain  = "${local.domain}.${var.domain_name}"
}

resource "google_compute_global_network_endpoint_group" "cubejs_network_group" {
  name                  = "${local.https_prefix}-neg"
  network_endpoint_type = "INTERNET_FQDN_PORT"
}

resource "google_compute_global_network_endpoint" "cubejs_network_endpoint" {
  global_network_endpoint_group = google_compute_global_network_endpoint_group.cubejs_network_group.id
  port                          = 443
  fqdn                          = trimprefix(google_cloud_run_service.cubejs.status[0].url, "https://")
}

resource "google_compute_backend_service" "cubejs_lb_backend" {
  name                   = "${local.https_prefix}-lb-backend"
  project                = var.project_id
  enable_cdn             = false
  protocol               = "HTTP2"
  timeout_sec            = 35
  custom_request_headers = ["Host: ${google_compute_global_network_endpoint.cubejs_network_endpoint.fqdn}"]

  backend {
    group = google_compute_global_network_endpoint_group.cubejs_network_group.id
  }

  log_config {
    enable = true
  }
}

resource "google_compute_url_map" "cubejs_url_map" {
  name            = "${local.https_prefix}-url-map"
  default_service = google_compute_backend_service.cubejs_lb_backend.id
}

resource "random_id" "certificate" {
  byte_length = 4
  prefix      = "${var.environment}-cubejs-cert-"

  keepers = {
    domains = local.full_domain
  }
}

resource "google_compute_managed_ssl_certificate" "cubejs_ssl_cert" {
  name = random_id.certificate.hex

  lifecycle {
    create_before_destroy = true
  }

  managed {
    domains = tolist([local.full_domain])
  }
}

resource "google_compute_target_https_proxy" "cubejs_target_proxy" {
  name             = "${local.https_prefix}-target-proxy"
  ssl_policy       = var.standard_ssl_policy_name
  ssl_certificates = [google_compute_managed_ssl_certificate.cubejs_ssl_cert.id]
  url_map          = google_compute_url_map.cubejs_url_map.id
}

resource "google_compute_global_address" "cubejs_fwd_address" {
  name = "${local.https_prefix}-fwd-rule-address"
}

resource "google_compute_global_forwarding_rule" "api_fwd_rule" {
  name        = "${local.https_prefix}-fwd-rule"
  target      = google_compute_target_https_proxy.cubejs_target_proxy.id
  ip_protocol = "TCP"
  port_range  = "443"
  ip_address  = google_compute_global_address.cubejs_fwd_address.address
}
