resource "google_service_account" "cubejs_sa" {
  account_id   = "cubejs"
  display_name = "CubeJS service account"
}

locals {
  role_list = [
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/run.serviceAgent",
    "roles/secretmanager.secretAccessor",
  ]
}

resource "google_project_iam_member" "cubejs_service_sa" {
  for_each = toset(local.role_list)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.cubejs_sa.email}"
}


resource "google_service_account_iam_member" "cubejs_sa_wi_iam_member" {
  service_account_id = google_service_account.cubejs_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.internal_project_id}.svc.id.goog[${var.cubejs_namespace}/${var.cubejs_ksa}]"
}
