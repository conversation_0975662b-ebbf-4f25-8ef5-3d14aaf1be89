locals {
  service_account_name = "dagster-analytics-pipelines"
  roles = [
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client"
  ]
  self_hosted_ksa_namespace = "dagster"
  self_hosted_ksa_name      = "analytics-data-pipelines-ksa"
}

resource "google_service_account" "service_account" {
  account_id   = local.service_account_name
  display_name = "Service account for Dagster Analytics Data Pipelines"
}

resource "google_service_account_iam_member" "internal_gke_workload_identity_self_hosted" {
  service_account_id = google_service_account.service_account.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.gke_project_id}.svc.id.goog[${local.self_hosted_ksa_namespace}/${local.self_hosted_ksa_name}]"
}

## General Roles
resource "google_project_iam_member" "general_roles" {
  for_each = toset(local.roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.service_account.email}"
}

output "service_account" {
  value = google_service_account.service_account
}
