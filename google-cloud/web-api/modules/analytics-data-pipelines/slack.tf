resource "google_secret_manager_secret" "analytics_data_pipelines_slack_auth_token_secret" {
  secret_id = "${var.environment}-analytics-data-pipelines-slack-auth-token"
  replication {
    user_managed {
      replicas {
        location = var.region
      }
    }
  }
}
resource "google_secret_manager_secret_version" "slack_auth_token_version" {
  secret = google_secret_manager_secret.analytics_data_pipelines_slack_auth_token_secret.id

  secret_data = var.slack_auth_token
}

resource "google_secret_manager_secret_iam_member" "analytics_data_pipeline_sid" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.analytics_data_pipelines_slack_auth_token_secret.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.service_account.email}"
}
