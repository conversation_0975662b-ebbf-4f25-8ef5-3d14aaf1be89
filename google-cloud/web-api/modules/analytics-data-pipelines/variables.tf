variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "gke_project_id" {
  description = "GKE Project ID"
  type        = string
}

variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

/*****************************************************************************
  Slack Key
******************************************************************************/

variable "slack_auth_token" {
  description = "Auth token for slack"
  type        = string
  sensitive   = true
}
