resource "random_password" "decodable_validation_user_password" {
  length  = 16
  special = true
}

resource "google_sql_user" "decodable_validation_user" {
  project  = var.project_id
  name     = "decodable_validation_user"
  instance = var.cloudsql_instance_name
  password = random_password.decodable_validation_user_password.result
}

resource "google_secret_manager_secret" "validation_db_password_secret" {
  project   = var.project_id
  secret_id = "decodable-validation-user-db-password"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "read_db_password_version" {
  secret      = google_secret_manager_secret.validation_db_password_secret.id
  secret_data = random_password.decodable_validation_user_password.result
}
