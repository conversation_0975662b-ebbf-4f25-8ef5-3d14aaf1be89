resource "random_password" "decodable_migration_user_password" {
  length  = 16
  special = true
}

resource "google_secret_manager_secret" "db_password_secret" {
  project   = var.project_id
  secret_id = "decodable-migration-user-db-password"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "db_password_version" {
  secret      = google_secret_manager_secret.db_password_secret.id
  secret_data = random_password.decodable_migration_user_password.result
}
