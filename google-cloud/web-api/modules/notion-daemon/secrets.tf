resource "google_secret_manager_secret" "notion_readonly_api_key" {
  secret_id = "${var.environment}-notion-readonly-api-key"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
      replicas {
        location = "us-east1"
      }
      replicas {
        location = "us-west1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "notion_readonly_api_key_version" {
  secret      = google_secret_manager_secret.notion_readonly_api_key.id
  secret_data = var.notion_readonly_api_key
}
