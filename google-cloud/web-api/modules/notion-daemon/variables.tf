variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "internal_project_id" {
  type        = string
  description = "Apella Internal GCP Project ID"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "notion_daemon_ksa" {
  description = "The Kubernetes notifier scheduler service account name"
  type        = string
  default     = "notion-daemon"
}

variable "api_server_namespace" {
  description = "The Kubernetes namespace where the api-server is deployed"
  type        = string
  default     = "api-server"
}

variable "notion_readonly_api_key" {
  description = "Notion Readonly API Key"
  type        = string
  sensitive   = true
}
