resource "google_service_account" "notion_daemon_service_account" {
  account_id   = "notion-daemon"
  display_name = "Notion daemon service account"

}


resource "google_service_account_iam_member" "notion_daemon_sa_wi_iam_member" {
  service_account_id = google_service_account.notion_daemon_service_account.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${var.internal_project_id}.svc.id.goog[${var.api_server_namespace}/${var.notion_daemon_ksa}]"
}

resource "google_secret_manager_secret_iam_member" "notion_daemon_sa_secret_access" {
  project   = var.project_id
  secret_id = google_secret_manager_secret.notion_readonly_api_key.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.notion_daemon_service_account.email}"
}
