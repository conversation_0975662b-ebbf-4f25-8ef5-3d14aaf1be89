locals {
  graphql_environment = "${var.environment}" != "prod" ? "${var.environment}." : ""
  api_endpoint        = "https://api.${local.graphql_environment}apella.io"
  sites_map = {
    for site in var.sites : site.site_id => site
  }
}

resource "google_service_account" "notifier_service_account" {
  account_id   = "notifier-scheduler"
  display_name = "notifier scheduler service account"
}

resource "google_cloud_scheduler_job" "twilio_schedule" {
  for_each = local.sites_map
  name     = "${each.key}-notifications_schedule"
  schedule = "* * * * ${coalesce(each.value.days_of_week, "*")}"

  http_target {
    uri         = "${local.api_endpoint}/v1/graphql"
    http_method = "POST"

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      "query" = <<EOT
        mutation { notifyStaffForEvents(
          input: {
            siteId: "${each.key}"
            confidenceThreshold: ${coalesce(each.value.confidence_threshold, 0.8)}
            timeThreshold: "${coalesce(each.value.time_threshold, "PT03M")}"
          }) { 
            success 
            } 
        }
      EOT
    }))

    oidc_token {
      service_account_email = google_service_account.notifier_service_account.email
      audience              = local.api_endpoint
    }
  }

  retry_config {
    retry_count          = 2
    min_backoff_duration = "5s"
    max_backoff_duration = "20s"
    max_retry_duration   = "0s"
    max_doublings        = "3"
  }

  time_zone = coalesce(each.value.timezone, "Etc/UTC")
}


resource "google_cloud_scheduler_job" "twilio_alerts_schedule" {
  for_each = local.sites_map
  name     = "${each.key}-notifications_alerts_schedule"
  schedule = "0 12 * * ${coalesce(each.value.days_of_week, "*")}"

  http_target {
    uri         = "${local.api_endpoint}/v1/graphql"
    http_method = "POST"

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      "query" = <<EOT
        mutation { checkNotificationsErrors(
          input: {
            siteId: "${each.key}"
            timeWindowToSearch: "P1DT2M"
          }) { 
            success 
            } 
        }
      EOT
    }))

    oidc_token {
      service_account_email = google_service_account.notifier_service_account.email
      audience              = local.api_endpoint
    }
  }

  retry_config {
    retry_count          = 2
    min_backoff_duration = "5s"
    max_backoff_duration = "20s"
    max_retry_duration   = "0s"
    max_doublings        = "3"
  }

  time_zone = coalesce(each.value.timezone, "Etc/UTC")
}
