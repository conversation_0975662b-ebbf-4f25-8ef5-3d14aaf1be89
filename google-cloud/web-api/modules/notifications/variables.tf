variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "region" {
  type        = string
  description = "The GCP region"
}

variable "sites" {
  type = list(object({
    site_id              = string
    confidence_threshold = number
    time_threshold       = string
    days_of_week         = string
    timezone             = optional(string)
  }))
  description = "List objects containing siteId, confidenceThreshold, timeThreshold, and which days of the week to run the schedule"
}

variable "retention_period" {
  description = "The number of days to retain messages for"
  default     = "604800s" #7 days same as subscriptions
  type        = string
}

variable "minimum_retry_backoff" {
  description = "The minimum number of seconds to wait before retrying delivery"
  type        = string
  default     = "10s"
}

variable "maximum_retry_backoff" {
  description = "The maximum number of seconds to wait before retrying delivery"
  type        = string
  default     = "300s"
}

variable "cloud_api_server_sa" {
  description = "The Cloud API server service account"
}
