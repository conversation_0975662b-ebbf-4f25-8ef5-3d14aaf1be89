locals {
  datadog_tags = [
    "environment:${var.environment}",
    "project_id:${var.project_id}",
    "team:realtime",
  ]
  datadog_slack_channel                  = "@slack-team-realtime-alert-${var.environment}"
  prod_notification_errors_dashboard_url = "https://hc.hex.tech/apella/app/e0f30a39-3570-46c9-8d30-2719cca90329/latest?rhid=e0f30a39-3570-46c9-8d30-2719cca90329"
  alert_threshold                        = 100
}

module "missing_notifications_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  type               = "metric alert"
  name               = "Missing notifications"
  renotify_statuses  = toset(["alert", "warn"])
  notify_no_data     = false
  query              = "sum(last_10m):sum:missing_notifications.count{env:${var.environment}}.as_count() > ${local.alert_threshold}"
  threshold_critical = local.alert_threshold
  message            = "We missed sending over ${local.alert_threshold} notifications.\n\nNote that Datadog double counts actual errors. Check reasons for missed notifications [here](${local.prod_notification_errors_dashboard_url}).\n\n${local.datadog_slack_channel}"
  tags               = local.datadog_tags
}

module "excess_notifications_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  type               = "metric alert"
  name               = "Excess notifications sent"
  renotify_statuses  = toset(["alert", "warn"])
  notify_no_data     = false
  query              = "sum(last_10m):sum:excess_notifications.count{env:${var.environment}}.as_count() > ${local.alert_threshold}"
  threshold_critical = local.alert_threshold
  message            = "We sent over ${local.alert_threshold} excess notifications.\n\nNote that Datadog double counts actual errors.\n\n${local.datadog_slack_channel}"
  tags               = local.datadog_tags
}


module "duplicate_notifications_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  type               = "metric alert"
  name               = "Duplicate notifications"
  renotify_statuses  = toset(["alert", "warn"])
  notify_no_data     = false
  query              = "sum(last_10m):sum:duplicate_notifications.count{env:${var.environment}}.as_count() > ${local.alert_threshold}"
  threshold_critical = local.alert_threshold
  message            = "We sent over ${local.alert_threshold} duplicate notifications.\n\nNote that Datadog double counts actual errors.\n\n${local.datadog_slack_channel}"
  tags               = local.datadog_tags
}

module "notifications_processor_stop_running_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  type              = "metric alert"
  name              = "Notification Processor Job Stopped Running"
  renotify_statuses = toset(["alert", "warn"])
  notify_no_data    = false

  # This query checks if the sum of job runs for the Notification Processor over the last 5 minutes is zero.
  # It uses a rollup function to average the data over 300 seconds.
  query = "sum(last_5m):sum:notification_processor_job_runs.count{env:${var.environment}}.as_count().rollup(avg, 300) == 0"

  threshold_critical = 0
  message            = "The Notification Processor job stopped running. Please investigate the issue immediately!\n\n${local.datadog_slack_channel}"
  tags               = local.datadog_tags
  evaluation_delay   = 0
}
