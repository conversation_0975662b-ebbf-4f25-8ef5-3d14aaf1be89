locals {
  datadog_slack_channel = "@slack-${var.slack_channel_name}"
  page_alert_channel    = "@webhook-incident_io"
  playbook_message      = var.link_to_playbook != null ? "See the playbook: ${var.link_to_playbook}" : ""
}

module "unacked_messages_count_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enabled.unacked_messages_count

  type               = "metric alert"
  name               = "${var.human_readable_subscription_name} unacked count"
  query              = "max(last_15m):sum:gcp.pubsub.subscription.num_undelivered_messages{project_id:${var.project_id},subscription_id:${var.subscription_name}} > ${var.num_unacked_messages_alert_threshold}"
  threshold_critical = var.num_unacked_messages_alert_threshold
  message            = <<EOT
  ${var.human_readable_subscription_name} has more than ${var.num_unacked_messages_alert_threshold} unacked messages.
  ${local.playbook_message}
  ${local.datadog_slack_channel}
  ${var.environment == "prod" ? local.page_alert_channel : ""}
  EOT
  tags               = var.tags
}

locals {
  age_display = (var.age_of_unacked_messages_alert_threshold_seconds < 60
    ? "${var.age_of_unacked_messages_alert_threshold_seconds} seconds"
    : var.age_of_unacked_messages_alert_threshold_seconds < 60 * 60
    ? "${var.age_of_unacked_messages_alert_threshold_seconds / 60} minutes"
    : var.age_of_unacked_messages_alert_threshold_seconds < 60 * 60 * 24
    ? "${var.age_of_unacked_messages_alert_threshold_seconds / 60 / 60} hours"
  : "${var.age_of_unacked_messages_alert_threshold_seconds / 60 / 60 / 24} days")
}

module "unacked_messages_age_monitor" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.0.0"

  environment = var.environment
  project_id  = var.project_id

  enabled = var.enabled.unacked_messages_age

  type               = "metric alert"
  name               = "${var.human_readable_subscription_name} unacked age"
  query              = "max(last_15m):avg:gcp.pubsub.subscription.oldest_unacked_message_age{project_id:${var.project_id},subscription_id:${var.subscription_name}} > ${var.age_of_unacked_messages_alert_threshold_seconds}"
  threshold_critical = var.age_of_unacked_messages_alert_threshold_seconds
  message            = <<EOT
  ${var.human_readable_subscription_name} has messages older than ${local.age_display}.
  ${local.playbook_message}
  ${local.datadog_slack_channel}
  ${var.environment == "prod" ? local.page_alert_channel : ""}
  EOT
  tags               = var.tags
}
