variable "environment" {
  type        = string
  description = "GCP environment"
}

variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "tags" {
  type        = set(string)
  description = "Set of tags to associate with the monitor"
}

variable "enabled" {
  type = object({
    unacked_messages_count = bool
    unacked_messages_age   = bool
  })
  description = "Whether or not to enable the distinct monitors"
  default = {
    unacked_messages_count = true
    unacked_messages_age   = true
  }
}

variable "subscription_name" {
  description = "the name of the subscription (google_pubsub_subscription.your_sub.name)"
}

variable "human_readable_subscription_name" {
  type        = string
  description = "the human readable name of the subscription"
}

variable "num_unacked_messages_alert_threshold" {
  type        = number
  description = "The number of unacked messages in the subscription to trigger an alert"
  default     = 100
}

variable "age_of_unacked_messages_alert_threshold_seconds" {
  type        = number
  description = "The age of unacked messages, in seconds, in the subscription to trigger an alert"
  default     = 60
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "link_to_playbook" {
  type        = string
  description = "The link to the playbook for this monitor"
  default     = null
}
