locals {
  datadog_tags = [
    "environment:${var.environment}",
    "team:${var.datadog_team_id}"
  ]
  datadog_slack_channel = var.slack_channel_name != null ? "@slack-${var.slack_channel_name}" : ""
  critical_alert        = var.should_page ? "@webhook-incident_io" : ""
}

module "pod_restarts" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  type              = "metric alert"
  name              = "Load Generator pod restarts"
  renotify_statuses = ["alert", "no data"]
  notify_no_data    = false

  query              = "sum(last_5m):monotonic_diff(sum:kubernetes.containers.restarts{service:load-generator, env:${var.environment}}) > ${var.pod_restarts_count_critical}"
  threshold_critical = var.pod_restarts_count_critical
  message            = <<END_MESSAGE
  {{#is_alert}} load-generator pods are restarting frequently.{{/is_alert}}
  {{#is_recovery}} load-generator pods have recovered.{{/is_recovery}}
  See the playbook: ${var.link_to_playbook}.
  ${local.datadog_slack_channel}
  ${local.critical_alert}
  END_MESSAGE
  tags               = local.datadog_tags
}

# Monitor for custom room exception metric
module "exception_count" {
  source  = "app.terraform.io/apella/gcp-monitor/datadog"
  version = "2.1.0"

  environment = var.environment
  project_id  = var.project_id

  type    = "metric alert"
  name    = "Load generation exceptions"
  message = <<END_MESSAGE
  {{#is_alert}} load-generator exceptions are high.{{/is_alert}}
  {{#is_recovery}} load-generator exceptions have recovered.{{/is_recovery}}
    See the playbook: ${var.link_to_playbook}.
  ${local.datadog_slack_channel}
  ${local.critical_alert}
  END_MESSAGE
  query   = "sum(last_15m):sum:loop_exception.count{service:load-generator, env:${var.environment}} > ${var.exception_count_critical}"

  threshold_critical = var.exception_count_critical
  tags               = local.datadog_tags
}
