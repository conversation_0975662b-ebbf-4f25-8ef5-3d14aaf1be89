locals {
  roleListLoadGenerator = [
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/run.serviceAgent",
    "roles/compute.instanceAdmin",
    "roles/secretmanager.secretAccessor",
    "roles/secretmanager.admin"
  ]
}

# Create service account to be used with load generator
resource "google_service_account" "load_generator_sa" {
  account_id   = "loadgenerator"
  display_name = "load generator service account"
  project      = var.project_id
}

# Grant IAM roles to the load generator service account
resource "google_project_iam_member" "loadgenerator_sa_roles" {
  for_each = toset(local.roleListLoadGenerator)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.load_generator_sa.email}"
}

# Configure Workload Identity Federation for the load generator
resource "google_service_account_iam_member" "load_generator_sa_wi_iam_member" {
  service_account_id = google_service_account.load_generator_sa.name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${var.internal_project_id}.svc.id.goog[${var.load_generator_namespace}/${var.load_generator_ksa}]"
} 