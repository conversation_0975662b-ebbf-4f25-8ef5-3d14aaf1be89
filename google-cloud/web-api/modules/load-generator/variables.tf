variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "environment" {
  description = "The environment for where the load generator will be created. Used for naming and labeling where applicable."
  type        = string
}

variable "internal_project_id" {
  description = "The project ID of the internal project where the Kubernetes cluster is running"
  type        = string
}

variable "load_generator_namespace" {
  description = "The Kubernetes namespace where the load generator is deployed"
  type        = string
  default     = "load-generator"
}

variable "load_generator_ksa" {
  description = "The Kubernetes load generator service account name"
  type        = string
  default     = "load-generator"
}

variable "labels" {
  description = "The key/value labels for the resources"
  type        = map(string)
  default     = {}
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "should_page" {
  type        = bool
  description = "Whether this alert should page Incident.io"
}

# Monitoring variables
variable "pod_restarts_count_critical" {
  description = "Number of pod restarts that will trigger an alert"
  type        = number
  default     = 2
}

variable "exception_count_critical" {
  description = "Number of exceptions that will trigger an alert"
  type        = number
  default     = 4
}

variable "link_to_playbook" {
  description = "Link to the playbook for the alert"
  type        = string
  default     = "https://www.notion.so/apella/Load-Generator-Playbook-1c4af54ad37a80dc910fe5e1664093fa"
}

variable "datadog_team_id" {
  description = "Datadog team ID for tagging alerts"
  type        = string
}
