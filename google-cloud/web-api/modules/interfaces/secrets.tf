locals {
  org_basic_auth_credentials = flatten([
    for org_id, config in var.orgs : [
      for credentials in config.basic_credentials : {
        org_id   = org_id
        username = credentials.username
      }
    ]
  ])
}

resource "google_secret_manager_secret" "ehr_interface_basic_auth_secret" {
  for_each = {
    for cred in local.org_basic_auth_credentials : "${cred.org_id}.${cred.username}" => cred
  }
  secret_id = "${each.value.org_id}_${each.value.username}_ehr_interface_secret"
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

resource "google_secret_manager_secret_version" "ehr_interface_basic_auth_secret_version" {
  for_each    = google_secret_manager_secret.ehr_interface_basic_auth_secret
  secret      = each.value.id
  secret_data = random_password.ehr_interface_basic_auth_password[each.key].result
}

resource "random_password" "ehr_interface_basic_auth_password" {
  for_each = google_secret_manager_secret.ehr_interface_basic_auth_secret
  length   = 16
  special  = true
}
