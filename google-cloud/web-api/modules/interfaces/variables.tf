variable "project_id" {
  type        = string
  description = "GCP Project ID"
}

variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "gcp_region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "api_server_url" {
  type        = string
  description = "URL for API Server with https:// prefix"
}

variable "standard_ssl_policy_name" {
  type        = string
  description = "SSL Policy Name"
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "customer_subdomain" {
  description = "The subdomain for customer facing applications in the k8s cluster"
  type        = string
  default     = "customer"
}

variable "interfaces_subdomain" {
  description = "Interfaces' subdomain in this environment"
  type        = string
  default     = "interfaces"
}

# TODO incorporate existing secrets for authentication
variable "orgs" {
  description = "map of orgs to configurations for interfaces"
  type = map(object({
    basic_credentials = list(object({ username = string }))
    }
  ))
}

variable "cloudflare_configuration" {
  type = object({
    api_token  = string
    account_id = string
  })
  description = "Cloudflare Provider Configuration"
  sensitive   = true
}
