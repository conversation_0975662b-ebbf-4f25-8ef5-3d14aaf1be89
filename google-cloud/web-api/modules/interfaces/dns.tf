locals {
  interfaces_domain_with_customer = "${var.customer_subdomain}.${var.domain_name}"
}

data "cloudflare_zones" "default" {
  filter {
    name = var.domain_name
  }
}

resource "cloudflare_record" "interfaces" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = var.environment == "prod" ? var.interfaces_subdomain : "${var.interfaces_subdomain}.${var.environment}"
  value   = var.environment == "prod" ? "${var.interfaces_subdomain}.${local.interfaces_domain_with_customer}" : "${var.interfaces_subdomain}.${var.environment}.${local.interfaces_domain_with_customer}"
  type    = "CNAME"
  ttl     = 300
}
