resource "google_service_account" "service_account" {
  account_id   = "annotation-task-scheduler"
  display_name = "annotation task scheduler service account"
}

resource "google_cloud_scheduler_job" "cloud_scheduler" {
  name             = "annotation-task-scheduler"
  description      = "Cloud Scheduler to generate Annotation Tasks for previous day"
  schedule         = "15 * * * *"
  time_zone        = "America/New_York"
  attempt_deadline = "900s"
  project          = var.project_id

  http_target {
    http_method = "POST"
    uri         = "${var.api_server_url}/v1/graphql"
    headers = {
      "Content-Type" = "application/json"
    }
    body = base64encode(jsonencode({
      operationName = "AnnotationCloudScheduler"
      query         = "mutation AnnotationCloudScheduler {\n  annotationTaskBulkGenerate {\n    success\n  }\n}\n"
    }))

    oidc_token {
      service_account_email = google_service_account.service_account.email
      audience              = var.api_server_url
    }
  }

  retry_config {
    retry_count          = 2
    min_backoff_duration = "5s"
    max_backoff_duration = "3600s"
    max_retry_duration   = "0s"
    max_doublings        = "3"
  }
}
