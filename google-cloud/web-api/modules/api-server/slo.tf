resource "datadog_service_level_objective" "api_server_uptime" {
  count       = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0
  name        = "Cloud API Server uptime SLO"
  type        = "monitor"
  description = "Using existing monitors, this is an SLO for the api-server uptime."
  depends_on = [
    datadog_monitor.k8s_pod_readiness,
    datadog_monitor.request_5xx_too_high
  ]
  monitor_ids = [
    datadog_monitor.k8s_pod_readiness[0].id,
    datadog_monitor.request_5xx_too_high[0].id
  ]

  thresholds {
    timeframe = "7d"
    target    = 99.5
    warning   = 99.9
  }

  thresholds {
    timeframe = "30d"
    target    = 99.5
    warning   = 99.9
  }

  timeframe         = "30d"
  target_threshold  = 99.5
  warning_threshold = 99.9

  tags = concat(local.datadog_tags, ["uptime"])
}
