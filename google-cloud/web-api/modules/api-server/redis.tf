resource "google_redis_instance" "api-server-memory-cache" {
  display_name   = "API Server Memory Cache Instance"
  name           = "api-server-memory-cache"
  location_id    = var.zone
  labels         = var.labels
  tier           = "STANDARD_HA"
  memory_size_gb = 1

  # authorized_network = "projects/${var.subnetwork_project}/global/networks/${var.vpc_network}"
  authorized_network = var.authorized_network
  connect_mode       = "PRIVATE_SERVICE_ACCESS" # Required for networks in other projects

  redis_version = "REDIS_6_X"
}
