# Used for testing EHR connectivity
resource "google_secret_manager_secret" "apella_internal_ehr_interface_secret" {
  secret_id = "apella_internal_0_ehr_interface_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}

# Create a Houston Methodist Secret in staging/prod only
resource "google_secret_manager_secret" "houston_methodist_ehr_interface_secret" {
  count     = var.environment == "dev" ? 0 : 1
  secret_id = "houston_methodist_ehr_interface_secret"

  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}
