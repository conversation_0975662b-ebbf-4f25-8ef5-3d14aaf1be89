locals {
  datadog_tags = [
    "environment:${var.environment}", "project-id:${var.project_id}", "team:platform-services"
  ]
  slack_alert_channel_name_base = "@slack-alert-api-server"
}

resource "datadog_monitor" "pod_failures" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Too many terminated API Server pods for ${var.environment}"
  message = "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):sum:kubernetes.containers.state.terminated{service:api-server, env:${var.environment}, reason:oomkilled} by {version} >= 1"
  type    = "metric alert"

  monitor_thresholds {
    critical = 1
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert"]
  priority          = 1

  tags = local.datadog_tags
}

resource "datadog_monitor" "request_p99_too_high" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "P99 request latency for API Server ${var.environment} is too high."
  message = "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):istio.mesh.request.duration.milliseconds.sum{destination_service_name:api-server, env:${var.environment}}.as_count() / sum:istio.mesh.request.duration.milliseconds.count{destination_service_name:api-server, env:${var.environment}}.as_count() >= 500"
  type    = "metric alert"

  monitor_thresholds {
    critical = 500
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert", "no data"]
  priority          = 1

  tags = local.datadog_tags
}


resource "datadog_monitor" "request_404_too_high" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Request 404 errors for API Server ${var.environment} is too high."
  message = "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}, response_code:404}.as_count() * 100 / sum:istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}}.as_count() > 3.0"
  type    = "metric alert"

  monitor_thresholds {
    critical = 3.0
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert"]
  priority          = 1

  tags = local.datadog_tags
}


resource "datadog_monitor" "request_403_too_high" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Request 403 errors for API Server ${var.environment} is too high."
  message = "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}, response_code:403}.as_count() * 100 / sum:istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}}.as_count() > 0.1"
  type    = "metric alert"

  monitor_thresholds {
    critical = 0.1
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert"]
  priority          = 1

  tags = local.datadog_tags
}

resource "datadog_monitor" "request_4xx_too_high" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Request 4xx errors for API Server ${var.environment} is too high."
  message = "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}, response_code:4*, !response_code:403, !response_code:404}.as_count() * 100 / sum:istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}}.as_count() > 0.1"
  type    = "metric alert"

  monitor_thresholds {
    critical = 0.1
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert"]
  priority          = 1

  tags = local.datadog_tags
}


resource "datadog_monitor" "request_5xx_too_high" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Request 5xx errors for API Server ${var.environment} is too high."
  message = "${local.slack_alert_channel_name_base}-${var.environment} Playbook: https://www.notion.so/apella/General-Observability-Playbook-1c9af54ad37a807ebcf9ed9ca5360f8c?pvs=4"
  query   = "sum(last_15m):istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}, response_code:5*}.as_count() * 100 / sum:istio.mesh.request.count{destination_service_name:api-server, env:${var.environment}}.as_count() > 0.1"
  type    = "metric alert"

  monitor_thresholds {
    critical = 0.1
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert"]
  priority          = 1

  tags = local.datadog_tags
}

resource "datadog_monitor" "k8s_pod_readiness" {
  count              = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0
  name               = "Alert when a high amount of Pods are not ready"
  type               = "query alert"
  query              = "min(last_5m):sum:kubernetes_state.pod.status_phase{!phase:running, environment:${var.environment}, namespace:api-server} / sum:kubernetes_state.pod.status_phase{phase:running, environment:${var.environment}, namespace:api-server} > 0.8"
  message            = <<-EOT
    ${local.slack_alert_channel_name_base}-${var.environment}
    A large portion of your Kubernetes pods are not ready. Investigate the issue.
    Playbook: https://www.notion.so/apella/General-Observability-Playbook-1c9af54ad37a807ebcf9ed9ca5360f8c?pvs=4
    The application can be accessed at:
    ${var.environment == "prod" ? "https://argocd.internal.apella.io/applications/argocd/api-server" : ""}
    ${var.environment == "dev" ? "https://argocd.dev.internal.apella.io/applications/argocd/api-server" : ""}
  EOT
  escalation_message = "Urgent: A large portion of your pods are not ready. Please check the deployment and investigate pod readiness probes."
  monitor_thresholds {
    critical = 0.8
    warning  = 0.6
  }
  notify_no_data    = false
  renotify_interval = 60
  timeout_h         = 0
  evaluation_delay  = 300
  include_tags      = true
  tags              = local.datadog_tags
}
