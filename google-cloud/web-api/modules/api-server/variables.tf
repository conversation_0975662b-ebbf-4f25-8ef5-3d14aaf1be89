variable "authorized_network" {
  description = "The authorized network that can connect to Redis"
  type        = string
}

variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "slack_channel_name" {
  type        = string
  description = "name of Slack channel for alerts. Do not prefix with #"
}

variable "slack_api_token" {
  type        = string
  description = "Slack API Token"
  sensitive   = true
}

variable "labels" {
  description = "Default labels for datasets"
  type        = map(string)
  default     = {}
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
}
