variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "project_id" {
  type = string
}

variable "subnetwork_project" {
  type = string
}

variable "subnet_name" {
  type = string
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "exit_node_instance_type" {
  description = "Compute instance to use for exit_node"
  type        = string
  default     = "n1-standard-1"
}

variable "exit_node_name" {
  description = "Name of the exit node. If not supplied, one will be generated"
  type        = string
  default     = ""
}

variable "exit_node_zone" {
  description = "Zone to put exit_node host in"
  type        = string
}

variable "tailscale_tags" {
  description = "Tags to apply to tailscale hosts"
  type        = list(string)
  default = [
    "tag:exit-node",
  ]
}

variable "exit_node_tags" {
  description = "Network tags to apply to GCP exit nodes"
  type        = list(string)
  default = [
    "tailscale-exit-node",
  ]
}

variable "exit_node_base_name" {
  description = "The name of the exit node to coincide with secrets, sa, ip address names, ..."
  type        = string
  default     = "tailscale-exit-node"
}

variable "connector_tag" {
  description = "The tag to advertise as a connector. If empty string then don't advertise."
  type        = string
  default     = ""
}

variable "subnet_routes" {
  description = "To advertise subnet routes. You can add a comma delimited CIDR list. https://tailscale.com/kb/1019/subnets#how-subnet-routers-work"
  type        = string
  default     = ""

}

variable "create_exit_node_instance" {
  description = "Whether or not the exit node instance should be created, or if this is just a placeholder for a future node"
  type        = bool
  default     = true
}
