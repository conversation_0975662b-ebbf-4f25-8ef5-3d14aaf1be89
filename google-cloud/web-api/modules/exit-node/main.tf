locals {
  role_list = [
    "roles/secretmanager.secretVersionAdder",
    "roles/secretmanager.secretAccessor",
  ]
  instance_name               = var.exit_node_name
  secret_name                 = "${var.exit_node_base_name}-state"
  advertise_connector_command = var.connector_tag == "" ? "" : "--advertise-connector --advertise-tags=${var.connector_tag}"
  advertise_routes_command    = var.connector_tag == "" ? "" : "--advertise-routes=${var.subnet_routes}"
}

resource "tailscale_tailnet_key" "non_ephemeral_exit_node_tailnet_key" {
  reusable  = true
  ephemeral = false
  tags      = var.tailscale_tags
}

resource "google_service_account" "tailscale_exit_node_sa" {
  account_id   = "${var.exit_node_base_name}-sa"
  project      = var.project_id
  display_name = "Tailscale Exit Node Service Account"
}

resource "google_project_iam_member" "tailscale_exit_node_service_sa" {
  for_each = toset(local.role_list)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.tailscale_exit_node_sa.email}"
}

resource "google_compute_address" "tailscale_exit_node_ip_address" {
  name = "${var.exit_node_base_name}-ip-address"
}

resource "google_compute_instance" "tailscale_exit_node" {
  count                     = var.create_exit_node_instance == true ? 1 : 0
  name                      = local.instance_name
  machine_type              = var.exit_node_instance_type
  zone                      = var.exit_node_zone
  labels                    = var.labels
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = "ubuntu-2004-lts"
    }
  }

  network_interface {
    subnetwork_project = var.subnetwork_project
    subnetwork         = var.subnet_name
    access_config {
      nat_ip = google_compute_address.tailscale_exit_node_ip_address.address
    }
  }

  scheduling {
    on_host_maintenance = "MIGRATE"
    automatic_restart   = true
    preemptible         = false
  }

  service_account {
    email = google_service_account.tailscale_exit_node_sa.email
    scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }

  metadata = {
    // This script sets up networking for Tailscale
    startup-script = templatefile("${path.module}/templates/exit_node_startup_script.tpl",
      {
        tailscale_authkey           = tailscale_tailnet_key.non_ephemeral_exit_node_tailnet_key.key,
        tailscale_secret_name       = local.secret_name
        advertise_connector_command = local.advertise_connector_command
        advertise_routes_command    = local.advertise_routes_command
      }
    )
  }

  tags = var.exit_node_tags
}

resource "google_secret_manager_secret" "tailscale_exit_node_state" {
  secret_id = local.secret_name
  replication {
    user_managed {
      replicas {
        location = "us-central1"
      }
    }
  }
}
