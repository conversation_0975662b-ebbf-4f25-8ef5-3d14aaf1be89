# Script to install and configure Tailscale
# The OS is updated
sudo apt update -y && sudo apt upgrade -y

# Enable IP forwarding
echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p /etc/sysctl.conf

#
# Tailscale Setup
#

# Pull the previous state that exists
sudo gcloud secrets versions access "latest" --secret ${tailscale_secret_name} --out-file="/var/lib/tailscale/tailscaled.state"

sudo curl -fsSL https://tailscale.com/install.sh | sh
sudo tailscale up --authkey ${tailscale_authkey} --advertise-exit-node ${advertise_connector_command} ${advertise_routes_command}

# Save the current state after connected so that future node recreations will be registered as the same Tailscale machine
sudo gcloud secrets versions add ${tailscale_secret_name} --data-file="/var/lib/tailscale/tailscaled.state"
