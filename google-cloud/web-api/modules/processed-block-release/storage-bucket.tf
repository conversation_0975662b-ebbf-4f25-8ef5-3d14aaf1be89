module "processed_block_release_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD
  bucket_name                 = "${var.environment}-processed-block-release-data"
  project_id                  = var.project_id
  location                    = var.region
  environment                 = var.environment
  iam_members                 = var.processed_block_release_iam_members
  lifecycle_rules             = var.processed_block_release_data_lifecycle_rules
  labels                      = var.labels
  uniform_bucket_level_access = true

  cors = {
    origin          = []
    method          = []
    response_header = ["*"]
    max_age_seconds = 3000
  }
}

# Allow access to Buck<PERSON> to interact with block release data
resource "google_project_iam_member" "api_server_processed_block_release_bucket_roles" {
  for_each = toset([
    "roles/storage.objectUser"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:api-server@${var.project_id}.iam.gserviceaccount.com"
}