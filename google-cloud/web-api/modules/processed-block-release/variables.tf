variable "project_id" {
  description = "GCP Project ID"
  type        = string
}

variable "environment" {
  type        = string
  description = "GCP environment"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be either dev, staging, or prod."
  }
}

variable "region" {
  description = "GCP region"
  default     = "us-central1"
}

variable "processed_block_release_iam_members" {
  description = "The list of IAM members to grant permissions on the bucket."
  type = list(object({
    role   = string
    member = string
  }))
  default = []
}

variable "processed_block_release_data_lifecycle_rules" {
  description = "The bucket's Lifecycle Rules configuration."
  type = list(object({
    action    = any
    condition = any
  }))
  default = []
}

variable "labels" {
  description = "The key/value labels "
  type        = map(string)
  default     = {}
}
