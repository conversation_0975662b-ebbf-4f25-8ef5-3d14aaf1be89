# Create an integration to a GCP project to collect metrics
# https://docs.datadoghq.com/integrations/google_cloud_platform/#metric-collection

resource "google_service_account" "datadog_service_account" {
  account_id   = "datadog-service-account"
  display_name = "Datadog Service Account"
  description  = "Exports logs and metrics from GCP to Datadog"
}

locals {
  roleList = [
    "roles/compute.viewer",
    "roles/monitoring.viewer",
    "roles/cloudasset.viewer",
    "roles/browser",
  ]
}

resource "google_project_iam_member" "datadog_service_account" {
  for_each = toset(local.roleList)
  project  = google_service_account.datadog_service_account.project
  role     = each.value
  member   = "serviceAccount:${google_service_account.datadog_service_account.email}"
}

resource "google_service_account_key" "datadog_sa_key" {
  service_account_id = google_service_account.datadog_service_account.id
}

locals {
  service_account_decoded_key = jsondecode(base64decode(google_service_account_key.datadog_sa_key.private_key))
}

resource "datadog_integration_gcp" "datadog_gcp_integration" {
  project_id     = google_service_account.datadog_service_account.project
  private_key    = local.service_account_decoded_key["private_key"]
  private_key_id = local.service_account_decoded_key["private_key_id"]
  client_id      = local.service_account_decoded_key["client_id"]
  client_email   = google_service_account.datadog_service_account.email
  host_filters   = join(",", [for key, value in var.datadog_labels : "${key}:${value}"])
}

resource "datadog_integration_gcp_sts" "datadog_sts" {
  client_email    = google_service_account.datadog_service_account.email
  host_filters    = toset([for key, value in var.datadog_labels : "${key}:${value}"])
  automute        = true
  is_cspm_enabled = false
}

// Grant token creator role to the Datadog principal account.
resource "google_service_account_iam_member" "datadog_token_creator_iam" {
  service_account_id = google_service_account.datadog_service_account.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = format("serviceAccount:%s", datadog_integration_gcp_sts.datadog_sts.delegate_account_email)
}
