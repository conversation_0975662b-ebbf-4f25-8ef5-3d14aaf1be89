# Forward all CloudRun logs to Datadog.
# The sink filters the logs to CloudRun. Then publishes to a topic. The subscription then pushes
# the logs to Datadog.
# Followed documentation here:
# https://docs.datadoghq.com/integrations/google_cloud_platform/#log-collection

resource "google_logging_project_sink" "export_logs_to_datadog_sink" {
  name                   = "export-logs-to-datadog"
  destination            = "pubsub.googleapis.com/projects/${google_pubsub_topic.export_logs_to_datadog_topic.project}/topics/${google_pubsub_topic.export_logs_to_datadog_topic.name}"
  filter                 = "resource.type=\"cloud_run_revision\""
  unique_writer_identity = true
}

resource "google_project_iam_member" "log_sink_access_to_topic" {
  project = google_service_account.datadog_service_account.project
  role    = "roles/pubsub.publisher"
  member  = google_logging_project_sink.export_logs_to_datadog_sink.writer_identity
}

resource "google_pubsub_topic" "export_logs_to_datadog_topic" {
  name   = "${var.environment}-export-logs-to-datadog"
  labels = merge(var.labels, var.datadog_labels)
}

resource "google_pubsub_subscription" "export_logs_to_datadog_subscription" {
  name                       = "${var.environment}-export-logs-to-datadog-sub"
  topic                      = google_pubsub_topic.export_logs_to_datadog_topic.id
  labels                     = merge(var.labels, var.datadog_labels)
  message_retention_duration = "604800s" // 7 days is maximum retention
  expiration_policy {
    ttl = "" // never expire this subscription
  }
  ack_deadline_seconds  = 10
  retain_acked_messages = false
  push_config {
    push_endpoint = "https://gcp-intake.logs.datadoghq.com/api/v2/logs?dd-api-key=${var.datadog_api_key}&dd-protocol=gcp"
  }
}


