variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "labels" {
  description = "The key/value labels for all GCP resources."
  type        = map(string)
  default     = {}
}

variable "datadog_labels" {
  description = "Labels to designate a GCP resource to be monitored by Datadog."
  type        = map(string)
  default = {
    "datadog" = "monitored"
  }
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}
