locals {
  datadog_tags = ["team:clinical-data"]
  full_tags = [
    "environment:${var.environment}", "project-id:${var.project_id}", "team:clinical-data"
  ]
  slack_alert_channel_name_base = "@slack-team-ehr-interfaces-alert"
  page_alert_team_name_base     = "@webhook-incident_io"
}

module "redox_events_subscription_monitors" {
  source = "../pubsub-datadog-monitors"

  environment = var.environment
  project_id  = var.project_id

  enabled = {
    unacked_messages_count = var.environment == "prod" || var.environment == "staging"
    unacked_messages_age   = var.environment == "prod" || var.environment == "staging"
  }

  human_readable_subscription_name = "EHR Interfaces Events Subscription"
  slack_channel_name               = var.slack_channel_name
  subscription_name                = var.subscription_name
  tags                             = local.datadog_tags

  num_unacked_messages_alert_threshold            = 50
  age_of_unacked_messages_alert_threshold_seconds = 30 * 60 # 30 minutes
}

module "redox_events_error_subscription_monitors" {
  source = "../pubsub-datadog-monitors"

  environment = var.environment
  project_id  = var.project_id

  enabled = {
    unacked_messages_count = var.environment == "prod" || var.environment == "staging"
    unacked_messages_age   = var.environment == "prod" || var.environment == "staging"
  }

  human_readable_subscription_name = "EHR Interfaces Events Errors Subscription"
  slack_channel_name               = var.slack_channel_name
  subscription_name                = var.error_subscription_name
  tags                             = local.datadog_tags
  link_to_playbook                 = "https://github.com/Apella-Technology/redox-processor#replay-and-reprocess-messages-from-the-error-queue"

  num_unacked_messages_alert_threshold            = 50
  age_of_unacked_messages_alert_threshold_seconds = 24 * 60 * 60 # 1 day
}

resource "datadog_monitor" "ingestor_latencies_monitor" {
  count = var.environment == "prod" || var.environment == "staging" ? 1 : 0

  name    = "Ingestor response time latency too high for {{org_id.name}} [${var.environment}]"
  message = var.environment == "prod" ? local.page_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "min(last_5m):p99:redox_processor_event_ingestor_response_time_seconds{env:${var.environment}} by {org_id} >= ${var.environment == "prod" ? 5 : 30}"
  type    = "query alert"

  monitor_thresholds {
    critical = var.environment == "prod" ? 5 : 30
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert", "no data"]
  priority          = 1

  tags = local.full_tags
}

resource "datadog_monitor" "ingestor_request_count_monitor" {
  count = var.environment == "prod" ? 1 : 0

  name    = "Ingestor incoming request count too low for {{org_id.name}} [${var.environment}]"
  message = var.environment == "prod" ? local.page_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_1d):sum:redox_processor_event_ingestor_response_time_seconds.count{env:${var.environment}} by {org_id}.as_count() <= 0"
  type    = "query alert"

  monitor_thresholds {
    critical = 0
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = true
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert", "no data"]
  priority          = 1

  tags = local.full_tags
}

resource "datadog_monitor" "ingestor_errors_monitor" {
  count = var.environment == "prod" || var.environment == "dev" || var.environment == "staging" ? 1 : 0

  name    = "Ingestor errors too high for {{org_id.name}} [${var.environment}]"
  message = var.environment == "prod" ? local.page_alert_team_name_base : "${local.slack_alert_channel_name_base}-${var.environment}"
  query   = "sum(last_15m):sum:redox_processor_event_ingestor_errors.count{env:${var.environment}} by {org_id}.as_count() >= 10"
  type    = "query alert"

  monitor_thresholds {
    critical = 10
  }

  notify_audit        = false
  require_full_window = false
  notify_no_data      = false
  renotify_interval   = 30
  include_tags        = true
  new_group_delay     = 60
  no_data_timeframe   = 1440
  evaluation_delay    = 300 # 5 minutes.

  renotify_statuses = ["alert", "no data"]
  priority          = 1

  tags = local.full_tags
}
