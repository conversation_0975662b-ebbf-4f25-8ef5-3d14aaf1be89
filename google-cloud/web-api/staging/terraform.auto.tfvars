/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region         = "us-central1"
project_id     = "staging-web-api-3efef9"
project_number = 612883740328
environment    = "staging"

#database settings
database_version         = "POSTGRES_13"
zone                     = "us-central1-c"
tier                     = "db-custom-1-3840"
replica_tier             = "db-custom-1-3840"
replica_active           = false
activation_policy        = "ALWAYS"
availability_type        = "REGIONAL"
secretname_user_name     = "staging-pgadmin-username"
secretname_user_password = "staging-pgadmin-password"

backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

pg_superuser_username_secret = "staging-api-user-name"
pg_superuser_password_secret = "staging-api-user-password"

additional_users = [
  {
    name     = "staging-api-user-name"
    password = "staging-api-user-password"
  }
]

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "staging-api-server-resources"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-emails-and-phone-numbers"
  "vanta-contains-ephi"      = "false"
}

#sub network used by all the vms
subnetwork_project = "nonprod-network-9523f0"
subnetwork         = "nonprod-central1-compute"

dev_group = "<EMAIL>"

#cloud run
serverless_vpc_connector_id = "projects/nonprod-network-9523f0/locations/us-central1/connectors/svpc-nonprod-us-central1"

# Database
database_monitoring_enabled         = true
database_monitoriing_tags           = ["env:staging", "project-id:staging-web-api-3efef9", "team:platform-services"]
database_monitoring_warning_channel = "@slack-team-platform-services-alert-staging"

# KEDA
keda_sa = "serviceAccount:<EMAIL>"


# Site Information
sites = [
  { site_id              = "lab_1",
    confidence_threshold = 0.65,
    time_threshold       = "PT01M",
    days_of_week         = "1-5",
    timezone             = "US/Eastern",
  },
  { site_id              = "palo_alto_1",
    confidence_threshold = 0.8,
    time_threshold       = "PT03M",
    days_of_week         = "*",
  },
]
