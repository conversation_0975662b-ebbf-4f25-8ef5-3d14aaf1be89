/****************************************************************
Create public DNS record for api gateway
*****************************************************************/

data "cloudflare_zones" "default" {
  filter {
    name = "apella.io"
  }
}

resource "cloudflare_record" "api" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "${var.api_server_subdomain}.${var.environment}"
  value   = "${var.api_server_subdomain}.${var.environment}.${var.customer_subdomain}.${var.domain_name}"
  type    = "CNAME"
  ttl     = 300
}
