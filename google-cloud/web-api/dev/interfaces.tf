module "interfaces" {
  source                   = "../modules/interfaces"
  api_server_url           = "${cloudflare_record.api.name}.${var.domain_name}"
  project_id               = var.project_id
  standard_ssl_policy_name = google_compute_ssl_policy.standard-ssl-policy.name
  environment              = var.environment
  cloudflare_configuration = {
    api_token  = var.cloudflare_api_token
    account_id = var.cloudflare_account_id
  }
  gcp_region = var.region
  orgs = {
    "apella_internal_0" = {
      endpoints = ["redox"]
      basic_credentials = [{
        username : "apella_internal_0"
      }]
    },
    "scrubs" = {
      endpoints = ["redox"]
      basic_credentials = [{
        username : "scrubs"
      }]
    },
    "greys_anatomy" = {
      endpoints = ["redox"]
      basic_credentials = [{
        username : "greys_anatomy"
      }]
    },
    "apella_hl7_test" = {
      endpoints = ["hl7"]
      basic_credentials = [{
        username : "apella_hl7_test"
      }]
    },
    "north_bay" = {
      endpoints = ["redox", "hl7"]
      basic_credentials = [{
        username : "north_bay"
      }]
    }
  }
}
