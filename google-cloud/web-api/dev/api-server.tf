module "api-server" {
  source             = "../modules/api-server"
  authorized_network = data.terraform_remote_state.nonprod_vpc.outputs.network.id
  environment        = var.environment
  labels             = var.labels
  slack_channel_name = "team-platform-services-alert-dev"
  slack_api_token    = var.slack_api_token
  datadog_api_key    = var.datadog_api_key
  datadog_app_key    = var.datadog_app_key
  project_id         = var.project_id
}
