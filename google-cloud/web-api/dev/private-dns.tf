data "google_dns_managed_zone" "nonprod_dns_zone" {
  name    = "nonprod"
  project = var.subnetwork_project
}

resource "google_dns_record_set" "cloudsql" {
  project      = var.subnetwork_project
  managed_zone = data.google_dns_managed_zone.nonprod_dns_zone.name
  name         = "dev-sql.nonprod.apella.io."
  type         = "A"
  rrdatas      = [module.cloud_sql.private_ip_address]
  ttl          = 300
}