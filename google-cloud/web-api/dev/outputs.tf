
/*
output "instance_private_ip_address" {
  value = module.cloud_sql.instance_private_ip_address
}

output "instance_self_link" {
  value = module.cloud_sql.instance_self_link
}

*/

output "cloudsql_instance_name" {
  value = module.cloud_sql.instance_name
}

output "api_server" {
  value = "${cloudflare_record.api.name}.${var.domain_name}"
}

output "api_server_sa" {
  value = google_service_account.api_server_sa.email
}

output "notifier_service_account" {
  value = module.notifications.notifier_service_account.email
}

output "cubejs_sa_email" {
  value = module.cubejs.cubejs_sa.email
}

output "dagster_analytics_pipelines_sa" {
  value = module.analytics-data-pipelines.pipelines_service_account
}
