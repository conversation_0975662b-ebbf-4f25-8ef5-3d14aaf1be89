locals {
  nonprod_gh_runner_sa = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  roleListGithubRunner = [
    "roles/compute.instanceAdmin",
    "roles/apigateway.admin",
    "roles/cloudbuild.builds.builder",
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/datastore.indexAdmin",
    "roles/run.admin",
    "roles/run.serviceAgent",
    "roles/storage.admin",
    "roles/viewer",
    "roles/secretmanager.secretAccessor",
    "roles/firebase.sdkAdminServiceAgent",
    "roles/cloudfunctions.admin",  # To deploy cloud functions
    "roles/iam.serviceAccountUser" # To deploy cloud functions that run as a different service account
  ]
}

#github runner service account
resource "google_project_iam_member" "github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${local.nonprod_gh_runner_sa}"
}
