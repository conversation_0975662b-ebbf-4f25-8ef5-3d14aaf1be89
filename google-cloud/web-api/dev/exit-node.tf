module "exit-node" {
  source             = "../modules/exit-node"
  exit_node_name     = "${var.environment}-external-tailscale-exit-node"
  environment        = var.environment
  project_id         = var.project_id
  subnetwork_project = var.subnetwork_project
  subnet_name        = var.subnetwork
  labels             = var.labels
  exit_node_zone     = var.zone
}


module "vpn-exit-node" {
  source              = "../modules/exit-node"
  exit_node_name      = "vpn-exit-node"
  exit_node_base_name = "vpn-exit-node"
  environment         = var.environment
  project_id          = var.project_id
  subnetwork_project  = var.subnetwork_project
  subnet_name         = var.subnetwork
  labels              = var.labels
  exit_node_zone      = var.zone
  tailscale_tags      = ["tag:exit-node", "tag:connector"]
  connector_tag       = "tag:connector"
  subnet_routes       = "192.168.0.0/16"
}
