/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region         = "us-central1"
project_id     = "dev-web-api-72f12b"
project_number = 52250969628
environment    = "dev"

#database settings
database_version         = "POSTGRES_13"
zone                     = "us-central1-c"
tier                     = "db-custom-2-7680"
replica_tier             = "db-custom-1-3840"
replica_active           = false
activation_policy        = "ALWAYS"
availability_type        = "REGIONAL"
secretname_user_name     = "dev-pgadmin-username"
secretname_user_password = "dev-pgadmin-password"

backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

pg_superuser_username_secret = "dev-api-user-name"
pg_superuser_password_secret = "dev-api-user-password"

additional_users = [
  {
    name     = "dev-api-user-name"
    password = "dev-api-user-password"
  }
]

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "dev-api-server-resources"
  "vanta-contains-user-data" = "false"
  "vanta-contains-ephi"      = "false"
}

#sub network used by all the vms
subnetwork_project = "nonprod-network-9523f0"
subnetwork         = "nonprod-central1-compute"

dev_group = "<EMAIL>"

#cloud run
serverless_vpc_connector_id = "projects/nonprod-network-9523f0/locations/us-central1/connectors/svpc-nonprod-us-central1"

# Database
database_monitoring_enabled         = true
database_monitoriing_tags           = ["env:dev", "project-id:dev-web-api-72f12b", "team:platform-services"]
database_monitoring_warning_channel = "@slack-team-platform-services-alert-dev"

# KEDA
keda_sa = "serviceAccount:<EMAIL>"

# Site Information
sites = [
  {
    site_id              = "lab_1",
    confidence_threshold = 0.65,
    time_threshold       = "PT01M",
    days_of_week         = "1-5",
    timezone             = "US/Eastern",
  },
  {
    site_id = "palo_alto_1",
  },
]
