locals {
  ip_configuration = {
    ipv4_enabled        = false
    private_network     = data.terraform_remote_state.nonprod_vpc.outputs.network.id
    require_ssl         = true
    authorized_networks = var.authorized_networks
  }

  read_replicas = var.replica_active == false ? [] : [{
    name             = "-analytics"
    tier             = var.replica_tier
    ip_configuration = local.ip_configuration
    database_flags = [
      {
        name  = "cloudsql.logical_decoding"
        value = "on"
      }
    ]
    user_labels = var.labels
    zone        = var.zone
    # Following are default values, but must be provided for the replica module to work
    disk_autoresize = true
    disk_size       = 10
    disk_type       = "PD_SSD"

    insights_config = {
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }
  }]
}

module "cloud_sql" {
  source  = "app.terraform.io/apella/cloud-sql/google"
  version = "1.4.4"

  name                     = "${var.environment}-postgres-01"
  random_instance_name     = false
  availability_type        = var.availability_type
  database_version         = var.database_version
  project_id               = var.project_id
  zone                     = var.zone
  region                   = var.region
  tier                     = var.tier
  deletion_protection      = false
  secret_project_id        = var.project_id
  secretname_user_name     = var.secretname_user_name
  secretname_user_password = var.secretname_user_password


  ip_configuration = local.ip_configuration

  backup_configuration = var.backup_configuration

  additional_users = var.additional_users
  user_labels      = var.labels

  insights_config = {
    query_string_length     = 1024
    record_application_tags = true
    record_client_address   = true
  }

  read_replicas = local.read_replicas

  database_flags = [
    {
      name  = "cloudsql.logical_decoding"
      value = "on"
    }
  ]

  monitor_enabled         = var.database_monitoring_enabled
  monitor_tags            = var.database_monitoriing_tags
  monitor_warning_channel = var.database_monitoring_warning_channel
}

module "cloudsql-secret-access" {
  source = "../modules/cloudsql-secret-access"

  project_id                   = var.project_id
  pg_superuser_username_secret = var.pg_superuser_username_secret
  pg_superuser_password_secret = var.pg_superuser_password_secret
  data_platform_tf_sa_email    = local.data_platform_tf_sa_email
}

module "decodable_cloudsql" {
  source = "../modules/decodable-cloudsql"

  project_id             = var.project_id
  cloudsql_instance_name = module.cloud_sql.instance_name
}
