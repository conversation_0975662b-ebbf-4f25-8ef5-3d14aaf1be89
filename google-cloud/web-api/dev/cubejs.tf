module "cubejs" {
  source                       = "../modules/cubejs"
  configuration_name           = "cubejs-${var.environment}"
  slack_channel_name           = "team-observations-alert-dev"
  environment                  = var.environment
  project_id                   = var.project_id
  internal_project_id          = local.internal_project_id
  serverless_vpc_connector_id  = var.serverless_vpc_connector_id
  sql_instance_connection_name = module.cloud_sql.instance_connection_name
  standard_ssl_policy_name     = google_compute_ssl_policy.standard-ssl-policy.name
  labels                       = var.labels
  datadog_labels               = var.datadog_labels
  cloudflare_configuration = {
    api_token  = var.cloudflare_api_token
    account_id = var.cloudflare_account_id
  }
}
