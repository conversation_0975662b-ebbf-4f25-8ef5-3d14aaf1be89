/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region         = "us-central1"
project_id     = "prod-web-api-7f60bf"
project_number = 71556946473
environment    = "prod"

#database settings
database_version         = "POSTGRES_13"
zone                     = "us-central1-c"
tier                     = "db-custom-16-61440"
replica_tier             = "db-custom-4-16384"
replica_active           = true
activation_policy        = "ALWAYS"
availability_type        = "REGIONAL"
secretname_user_name     = "prod-pgadmin-username"
secretname_user_password = "prod-pgadmin-password"

backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

pg_superuser_username_secret = "prod-api-user-name"
pg_superuser_password_secret = "prod-api-user-password"

additional_users = [
  {
    name     = "prod-api-user-name"
    password = "prod-api-user-password"
  }
]

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-api-server-resources"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-email-addresses-and-preferences"
  "vanta-contains-ephi"      = "true"
}

#sub network used by all the vms
subnetwork_project = "prod-network-bfb30f"
subnetwork         = "prod-central1-compute"

dev_group = "<EMAIL>"

#cloud run
serverless_vpc_connector_id = "projects/prod-network-bfb30f/locations/us-central1/connectors/svpc-prod-us-central1"

# Database
database_monitoring_enabled          = true
database_monitoriing_tags            = ["env:prod", "project-id:prod-web-api-7f60bf", "team:platform-services"]
database_monitoring_warning_channel  = "@slack-team-platform-services-alert-prod"
database_monitoring_critical_channel = "@webhook-incident_io"

# Exit Node
exit_node_instance_type = "n2d-standard-4"
exit_node_sub_instances = [{
  name : "b",
  create_exit_node_instance : true
  }, {
  name : "c",
  create_exit_node_instance : true
  }, {
  name : "d",
  create_exit_node_instance : true
  }, {
  name : "e",
  create_exit_node_instance : true
  }, {
  name : "f",
  create_exit_node_instance : true
  }, {
  name : "g",
  create_exit_node_instance : false
  }, {
  name : "h",
  create_exit_node_instance : false
  }, {
  name : "i",
  create_exit_node_instance : false
  }, {
  name : "j",
  create_exit_node_instance : false
  }, {
  name : "k",
  create_exit_node_instance : false
}]
exit_node_tags = ["exit-node", "tailscale", "edge"]

# KEDA
keda_sa = "serviceAccount:<EMAIL>"

# Site Information
sites = [
  {
    site_id = "HMH-OPC19",
  },
  {
    site_id = "HMH-OPC18",
  },
  {
    site_id = "HMH-WT03",
  },
  {
    site_id = "HMH-DUNN03",
  },
  {
    site_id = "HMH-DUNN06",
  },
  {
    site_id = "HMH-LD06",
  },
  {
    site_id = "HMH-MAIN03",
  },
  {
    site_id = "HMH-HMTW",
  },
  {
    site_id              = "HF-VH02",
    confidence_threshold = 0.65,
    time_threshold       = "PT01M",
    days_of_week         = "1-5",
    timezone             = "US/Eastern",
  },
  {
    site_id = "TGH-MAIN02",
  },
  {
    site_id = "TGH-CVTOR03",
  },
  {
    site_id = "nb_medical_center_fairfield",
  },
]
