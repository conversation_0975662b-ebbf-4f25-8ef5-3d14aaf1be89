########################################################################
# Redox event processor
# See https://github.com/Apella-Technology/redox-processor
########################################################################

########################################################################
# Redox event processor service account
#
# This service account is what the cloud-functions will be run as
########################################################################

locals {
  roleListRedox = [
    "roles/secretmanager.secretAccessor", # access the redox verification token stored in secrets
    "roles/pubsub.subscriber"
  ]
}

resource "google_service_account" "redox_processor_sa" {
  account_id   = "redox-processor"
  display_name = "redox processor service account"
}

resource "google_project_iam_member" "redox_processor_service_sa" {
  for_each = toset(local.roleListRedox)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

resource "google_service_account_iam_member" "redox_processor_sa_wi_iam_member" {
  service_account_id = google_service_account.redox_processor_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${local.internal_project_id}.svc.id.goog[${var.ehr_namespace}/${var.redox_processor_ksa}]"
}

########################################################################
# Redox ingester output topic
#
# The ingester just takes validated messages it gets and sends it to
# a pubsub topic.
########################################################################

// This is the topic that the redox events will be queued into initially
resource "google_pubsub_topic" "redox_events" {
  name                       = "${var.environment}-redox-events"
  labels                     = var.labels
  message_retention_duration = "259200s" // Set retention to 3 days
}

// Give permission to publish to redox events topic
resource "google_pubsub_topic_iam_member" "redox_processor_publishes_to_redox_event_topic" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.redox_events.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

// Give permission to subscribe to redox events topic
resource "google_pubsub_topic_iam_member" "redox_processor_subscribes_to_redox_event_topic" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.redox_events.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}


// Give permission to ehr projects terraform provisioner to admin the redox events topic
resource "google_pubsub_topic_iam_member" "ehr_project_admins_redox_event_topic" {
  role   = "roles/pubsub.admin"
  topic  = google_pubsub_topic.redox_events.id
  member = "serviceAccount:${data.terraform_remote_state.prod_project_factory.outputs.prod-ehr-project.tf_sa_email}"
}

########################################################################
# Redox processor error topic
#
# If processing errors, we need to be able to write to a different topic
########################################################################

resource "google_pubsub_topic" "redox_events_processing_errored" {
  name   = "${var.environment}-redox-events-processing-errored"
  labels = var.labels
}

// Give permission to publish to redox error events topic
resource "google_pubsub_topic_iam_member" "redox_processor_publishes_to_redox_event_errors_topic" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.redox_events_processing_errored.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

// Give permission to subscribe to redox error events topic
resource "google_pubsub_topic_iam_member" "redox_processor_subscribes_to_redox_event_errors_topic" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.redox_events_processing_errored.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

// For deadlettering to work, we need to give the internal account permission, too
resource "google_pubsub_topic_iam_member" "pubsub_service_publishes_to_redox_event_errors_topic" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.redox_events_processing_errored.id
  member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}

########################################################################
# Redox backfill topic
########################################################################

// This is the topic that backfilled events will be pushed into
resource "google_pubsub_topic" "backfill_redox_events" {
  name   = "${var.environment}-backfill-redox-events"
  labels = var.labels
}

// Give permission to subscribe to redox events topic
resource "google_pubsub_topic_iam_member" "backfill_redox_processor_subscribes_to_redox_event_topic" {
  role   = "roles/pubsub.subscriber"
  topic  = google_pubsub_topic.backfill_redox_events.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

########################################################################
# Redox backfill processor error topic
########################################################################

resource "google_pubsub_topic" "backfill_redox_events_processing_errored" {
  name   = "${var.environment}-backfill-redox-events-processing-errored"
  labels = var.labels
}

// Give permission to publish to backfill redox error events topic
resource "google_pubsub_topic_iam_member" "backfill_redox_processor_publishes_to_backfill_redox_event_errors_topic" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.backfill_redox_events_processing_errored.id
  member = "serviceAccount:${google_service_account.redox_processor_sa.email}"
}

// For deadlettering to work, we need to give the internal account permission, too
resource "google_pubsub_topic_iam_member" "pubsub_service_publishes_to_backfill_redox_event_errors_topic" {
  role   = "roles/pubsub.publisher"
  topic  = google_pubsub_topic.backfill_redox_events_processing_errored.id
  member = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}

########################################################################
# Redox processor subscription
########################################################################

// This subscription sends redox events to the redox processor
resource "google_pubsub_subscription" "ehr_events_subscription" {
  name                       = "${var.environment}-ehr-events-subscription"
  topic                      = google_pubsub_topic.redox_events.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = true
  expiration_policy {
    ttl = "" // never expire this subscription
  }

  ack_deadline_seconds = 90

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  enable_message_ordering = true

  dead_letter_policy {
    dead_letter_topic     = "projects/${var.project_id}/topics/${google_pubsub_topic.redox_events_processing_errored.name}"
    max_delivery_attempts = 5
  }
}


resource "google_pubsub_subscription" "rt_streaming_testing_subscription" {
  name                       = "${var.environment}-rt-streaming-testing"
  topic                      = google_pubsub_topic.redox_events.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = true
  expiration_policy {
    ttl = "" // never expire this subscription
  }

  ack_deadline_seconds = 90

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  enable_message_ordering = true

}

// For deadlettering to work, we need to give the internal account permission, too
resource "google_pubsub_subscription_iam_member" "pubsub_service_subscribes_to_ehr_events_subscription" {
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.ehr_events_subscription.id
  member       = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}


########################################################################
# Redox backfill processor subscription
########################################################################

resource "google_pubsub_subscription" "backfill_ehr_events_subscription" {
  name                       = "${var.environment}-backfill-ehr-events-subscription"
  topic                      = google_pubsub_topic.backfill_redox_events.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = true
  expiration_policy {
    ttl = "" // never expire this subscription
  }
  ack_deadline_seconds = 90

  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }

  enable_message_ordering = true

  dead_letter_policy {
    dead_letter_topic     = "projects/${var.project_id}/topics/${google_pubsub_topic.backfill_redox_events_processing_errored.name}"
    max_delivery_attempts = 5
  }
}

// For deadlettering to work, we need to give the internal account permission, too
resource "google_pubsub_subscription_iam_member" "pubsub_service_subscribes_to_backfill_ehr_event_topic" {
  role         = "roles/pubsub.subscriber"
  subscription = google_pubsub_subscription.backfill_ehr_events_subscription.id
  member       = "serviceAccount:service-${var.project_number}@gcp-sa-pubsub.iam.gserviceaccount.com"
}

########################################################################
# Redox error subscription
#
# In addition to the retries built in above, we also need a way to pull
# the "dead" messages from the dead letter queue.  So we create a pull
# subscription here.
########################################################################

resource "google_pubsub_subscription" "redox_events_error_subscription" {
  name                       = "${var.environment}-redox-events-processing-errors"
  topic                      = google_pubsub_topic.redox_events_processing_errored.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = false
  ack_deadline_seconds       = 90
  expiration_policy {
    ttl = "" // never expire this subscription
  }
}

########################################################################
# Redox backfill error subscription
########################################################################

resource "google_pubsub_subscription" "backfill_redox_events_error_subscription" {
  name                       = "${var.environment}-backfill-redox-events-processing-errors"
  topic                      = google_pubsub_topic.backfill_redox_events_processing_errored.id
  labels                     = var.labels
  message_retention_duration = "604800s" // 7 days is maximum retention
  retain_acked_messages      = false
  ack_deadline_seconds       = 90
  expiration_policy {
    ttl = "" // never expire this subscription
  }
}

module "redox_processor" {
  source                            = "../modules/redox-processor"
  environment                       = var.environment
  datadog_api_key                   = var.datadog_api_key
  datadog_app_key                   = var.datadog_app_key
  slack_channel_name                = "team-ehr-interfaces-alert-prod"
  subscription_name                 = google_pubsub_subscription.ehr_events_subscription.name
  error_subscription_name           = google_pubsub_subscription.redox_events_error_subscription.name
  backfill_subscription_name        = google_pubsub_subscription.backfill_ehr_events_subscription.name
  backfill_errors_subscription_name = google_pubsub_subscription.backfill_redox_events_error_subscription.name
  project_id                        = var.project_id
}
