/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "zone" {
  description = "GCP region for resources"
  default     = "us-central1-a"
}

variable "project_id" {
  description = "GCP Project ID"
}

variable "project_number" {
  description = "GCP Project Number"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "customer_subdomain" {
  description = "The subdomain for customer facing applications in the k8s cluster"
  type        = string
  default     = "customer"
}

variable "api_server_subdomain" {
  description = "API Server's subdomain in this environment"
  type        = string
  default     = "api"
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}

variable "datadog_labels" {
  description = "Labels to designate a resource to be monitored by Datadog."
  type        = map(string)
  default = {
    "datadog" = "monitored"
  }
}

variable "datadog_api_key" {
  description = "Datadog API key for GCP & Terraform"
  type        = string
  sensitive   = true
}

variable "datadog_app_key" {
  description = "Datadog App key for Terraform"
  type        = string
  sensitive   = true
}

variable "database_monitoring_enabled" {
  description = "Should monitors be created for the database"
  type        = bool
  default     = true
}

variable "database_monitoriing_tags" {
  description = "The tags to add to the monitor for the cloud sql database"
  type        = list(string)
}

variable "database_monitoring_warning_channel" {
  description = "The channel to which warnings should be sent"
  type        = string
}

variable "database_monitoring_critical_channel" {
  description = "The channel to which critical alerts should be sent. If not set, uses the warning channel for critical alerts."
  type        = string
}

// required
variable "database_version" {
  description = "The database version to use"
  type        = string
}

variable "tier" {
  description = "The tier for the master instance."
  type        = string
  default     = "db-f1-micro"
}

variable "replica_tier" {
  description = "The tier for the replica instance. Must be same or larger than the master instance"
  type        = string
  default     = "db-f1-micro"
}

variable "replica_active" {
  description = "Whether to activate the db replica"
  type        = bool
  default     = false
}

variable "activation_policy" {
  description = "The activation policy for the master instance.Can be either `ALWAYS`, `NEVER` or `ON_DEMAND`."
  type        = string
  default     = "ALWAYS"
}

variable "availability_type" {
  description = "The availability type for the master instance.This is only used to set up high availability for the PostgreSQL instance. Can be either `ZONAL` or `REGIONAL`."
  type        = string
  default     = "ZONAL"
}

variable "secretname_user_name" {
  description = "SecretManager Secret name where admin user name is stored"
  type        = string
}

variable "secretname_user_password" {
  description = "SecretManager Secret name where admin users password is stored."
  type        = string
}

/*
  additional_users = [
    {
      name     = "prod-pguser-user1name"
      password = "prod-pguser-user1password"
    },
    {
      name     = "prod-pguser-user2name"
      password = "prod-pguser-user2password"
    }
  ]
*/

variable "additional_users" {
  description = "A list of users to be created in your cluster"
  type = list(object({
    name     = string
    password = string
  }))
  default = []
}

variable "backup_configuration" {
  description = "The backup_configuration settings subblock for the database setings"
  type = object({
    enabled                        = bool
    start_time                     = string
    location                       = string
    point_in_time_recovery_enabled = bool
  })
  default = {
    enabled                        = false
    start_time                     = null
    location                       = null
    point_in_time_recovery_enabled = false
  }
}

#authorized_networks = list(map(string))

variable "authorized_networks" {
  description = "A list of authorized networks that are allowed to access cloudsql"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}


variable "subnetwork_project" {
  description = "The project ID where the desired subnetwork is provisioned"
}

variable "subnetwork" {
  description = "The name of the subnetwork to deploy instances into"
}

variable "dev_group" {
  description = "email for develoers group"
  type        = string
}

#cloud run
variable "serverless_vpc_connector_id" {
  description = "ID for vpc serverless connector"
  type        = string
}

##############################################################################################
# Slack notification variables
##############################################################################################
variable "slack_api_token" {
  type        = string
  description = "Slack API Token"
  sensitive   = true
}

# Load generator variables
##############################################################################################

# Workload Identity

variable "load_generator_namespace" {
  description = "The Kubernetes namespace where the load generator is deployed"
  type        = string
  default     = "load-generator"
}

variable "load_generator_ksa" {
  description = "The Kubernetes load generator service account name"
  type        = string
  default     = "load-generator"
}

/*****************************************************************************
cloudflare variables
******************************************************************************/
variable "cloudflare_api_token" {
  description = "cloudflare api token"
  type        = string
  sensitive   = true
}

variable "cloudflare_account_id" {
  description = "cloudflare account id"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  tailscale
******************************************************************************/

variable "tailscale_api_key" {
  description = "The Tailscale API key."
  type        = string
  sensitive   = true
}

variable "tailscale_tailnet" {
  description = "Tailscale network"
  type        = string
  default     = "apella.io"
}

/*****************************************************************************
  Slack Key
******************************************************************************/

variable "slack_auth_token" {
  description = "Auth token for slack"
  type        = string
  sensitive   = true
}

/*****************************************************************************
  API Server
******************************************************************************/

variable "api_server_namespace" {
  description = "The Kubernetes namespace where the api-server is deployed"
  type        = string
  default     = "api-server"
}

variable "api_server_ksa" {
  description = "The Kubernetes api server service account name"
  type        = string
  default     = "api-server"
}

variable "notifier_scheduler_ksa" {
  description = "The Kubernetes notifier scheduler service account name"
  type        = string
  default     = "notifier-scheduler"
}

/*****************************************************************************
  Postgres
******************************************************************************/

variable "pg_superuser_username_secret" {
  description = "Postgres superuser username secret"
  type        = string
}

variable "pg_superuser_password_secret" {
  description = "Postgres superuser password secret"
  type        = string
}

/*****************************************************************************
  Exit Node
******************************************************************************/

variable "exit_node_instance_type" {
  description = "Compute instance to use for exit_node"
  type        = string
  default     = "n2-standard-2"
}

variable "exit_node_sub_instances" {
  description = "A list of the names for our exit node sub instances."
  type = list(object({
    name                      = string
    create_exit_node_instance = bool
  }))
  default = []
}

/*****************************************************************************
  EHR
******************************************************************************/

variable "ehr_namespace" {
  description = "The Kubernetes namespace where the ehr applications are deployed"
  type        = string
  default     = "ehr-interfaces"
}

variable "redox_processor_ksa" {
  description = "The Kubernetes redox-processor service account name"
  type        = string
  default     = "redox-processor"
}

variable "keda_sa" {
  description = "The service account for KEDA in the internal project"
  type        = string
}

variable "sites" {
  description = "List objects containing siteId and other site specific information"
  type = list(object({
    site_id              = string
    confidence_threshold = optional(number)
    time_threshold       = optional(string)
    days_of_week         = optional(string)
    timezone             = optional(string)
  }))
}

/*****************************************************************************
  Launch Darkly
******************************************************************************/

variable "launch_darkly_sdk_key" {
  description = "Launch Darkly SDK key"
  type        = string
  sensitive   = true
}

variable "notion_readonly_api_key" {
  description = "Notion Readonly API Key"
  type        = string
  sensitive   = true
}
