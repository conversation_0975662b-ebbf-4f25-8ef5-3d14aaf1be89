module "exit-node" {
  source                  = "../modules/exit-node"
  exit_node_name          = "${var.environment}-external-tailscale-exit-node"
  environment             = var.environment
  project_id              = var.project_id
  subnetwork_project      = var.subnetwork_project
  subnet_name             = var.subnetwork
  labels                  = merge(var.labels, { "datadog" = "monitored" })
  exit_node_zone          = var.zone
  exit_node_instance_type = var.exit_node_instance_type
}


module "exit-node-sub-a" {
  source                  = "../modules/exit-node"
  exit_node_base_name     = "tailscale-exit-node-sub-a"
  exit_node_name          = "${var.environment}-external-tailscale-exit-node-sub-a"
  environment             = var.environment
  project_id              = var.project_id
  subnetwork_project      = var.subnetwork_project
  subnet_name             = var.subnetwork
  labels                  = merge(var.labels, { "datadog" = "monitored" })
  exit_node_zone          = var.zone
  exit_node_instance_type = var.exit_node_instance_type
}


module "exit-node-subs" {
  for_each                  = { for o in var.exit_node_sub_instances : o.name => o }
  source                    = "../modules/exit-node"
  exit_node_base_name       = "tailscale-exit-node-sub-${each.value.name}"
  exit_node_name            = "${var.environment}-external-tailscale-exit-node-sub-${each.value.name}"
  environment               = var.environment
  project_id                = var.project_id
  subnetwork_project        = var.subnetwork_project
  subnet_name               = var.subnetwork
  labels                    = merge(var.labels, { "datadog" = "monitored" })
  exit_node_zone            = var.zone
  exit_node_instance_type   = var.exit_node_instance_type
  create_exit_node_instance = each.value.create_exit_node_instance
}
