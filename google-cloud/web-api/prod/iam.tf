resource "google_project_iam_member" "cloud_sql_client" {
  project = var.project_id
  role    = "roles/cloudsql.admin"
  member  = "group:<EMAIL>"
}

locals {
  internal_project_id = data.terraform_remote_state.prod_project_factory.outputs.prod-internal-project.project_id
  roleListLoadGenerator = [
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/run.serviceAgent",
    "roles/compute.instanceAdmin",
    "roles/secretmanager.secretAccessor",
    "roles/secretmanager.admin"
  ]

  roleListApiServer = [
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/run.serviceAgent",
    "roles/secretmanager.secretAccessor",
    "roles/secretmanager.admin",
    "roles/storage.objectViewer",
    "roles/cloudtrace.agent",
    "roles/bigquery.jobUser"
  ]

  roleListDevelopers = [
    "roles/datastore.user"
  ]
}

#apiserver service account
resource "google_service_account" "api_server_sa" {
  account_id   = "api-server"
  display_name = "api server service account"
}

resource "google_project_iam_member" "prod_api_server_service_sa" {
  for_each = toset(local.roleListApiServer)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.api_server_sa.email}"
}


resource "google_project_iam_member" "dev_group_roles" {
  for_each = toset(local.roleListDevelopers)
  project  = var.project_id
  role     = each.value
  member   = "group:${var.dev_group}"
}

resource "google_service_account_iam_member" "api_server_sa_wi_iam_member" {
  service_account_id = google_service_account.api_server_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${local.internal_project_id}.svc.id.goog[${var.api_server_namespace}/${var.api_server_ksa}]"
}

resource "google_service_account_iam_member" "prod_notifier_scheduler_sa_wi_iam_member" {
  service_account_id = module.notifications.notifier_service_account.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${local.internal_project_id}.svc.id.goog[${var.api_server_namespace}/${var.notifier_scheduler_ksa}]"
}

# Service account for internal apps to write to the db

resource "google_service_account" "internal_writer_sa" {
  account_id   = "api-server-internal-writer"
  display_name = "SA to write to api server"
}

# Provision access to the bastion host

locals {
  bastionHostSARoles = [
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client"
  ]
}

resource "google_project_iam_member" "bastion_host_sa_role" {
  for_each = toset(local.bastionHostSARoles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${data.terraform_remote_state.prod_security.outputs.prod-bastion-host-sa}"
}

# Provision access to metabase

locals {
  metabaseSARoles = [
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client"
  ]
}

resource "google_project_iam_member" "metabase_sa_role" {
  for_each = toset(local.metabaseSARoles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${data.terraform_remote_state.prod_internal.outputs.metabase_sa_email}"
}

resource "google_project_iam_member" "realtime_dagster_sa_roles" {
  for_each = toset([
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${data.terraform_remote_state.data_platform_project.outputs.dagster_realtime_dags_sa.email}"
}

resource "google_project_iam_member" "self_hosted_dagster_sa_roles" {
  for_each = toset([
    "roles/secretmanager.secretAccessor",
    "roles/cloudsql.client"
  ])
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${data.terraform_remote_state.prod_internal.outputs.dagster_self_hosted_sa.email}"
}
