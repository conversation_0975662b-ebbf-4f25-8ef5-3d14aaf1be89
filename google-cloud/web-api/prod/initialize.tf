/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-web-api-prod"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 2.0"
    }
    tailscale = {
      source  = "tailscale/tailscale"
      version = "~> 0.13.5"
    }
    datadog = {
      source  = "DataDog/datadog"
      version = ">=3.25.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_project" "project" {
}

locals {
  org_id = data.google_organization.org.org_id
}

data "terraform_remote_state" "prod_vpc" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-prod"
    }
  }
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "prod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

data "terraform_remote_state" "prod_internal" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-internal-prod"
    }
  }
}

data "terraform_remote_state" "data_platform_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-prod"
    }
  }
}

data "terraform_remote_state" "ml_project" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-machine-learning-prod"
    }
  }
}

/******************************************
  Cloudflare Authentication
 *****************************************/

provider "cloudflare" {
  api_token  = var.cloudflare_api_token
  account_id = var.cloudflare_account_id
}

locals {
  data_platform_tf_sa_email = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.tf_sa_email
}

provider "tailscale" {
  api_key = var.tailscale_api_key
  tailnet = var.tailscale_tailnet
}
