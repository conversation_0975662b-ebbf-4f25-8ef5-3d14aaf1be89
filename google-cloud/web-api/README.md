# terraform-google-template
Terraform Module: Template

### Dev Setup to test locally
#### Terraform
1. Ensure you have enabled 2FA on app.terraform.io. [2FA](https://www.terraform.io/cloud-docs/users-teams-organizations/2fa)
2. Login to gcloud `gcloud auth login --update-adc`
3. Set current project: `gcloud config set project dev-web-api-72f12b`. Always use dev if planning locally.
4. `terraform login` # if you are running this the first time, you will be asked to create an api token
5. Go to dev terraform. `cd terraform/dev`
6. Run `terraform init`
7. Run `terraform validate` or `terraform plan` to verify terraform
