/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-billing"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "google"
      version = ">= 5.0"
    }
    archive = {
      source  = "archive"
      version = "~> 2.4"
    }
  }
}

provider "google" {
  region      = var.region
  credentials = var.GOOGLE_CREDENTIALS # Gets rid of warning in tf cloud console
}
