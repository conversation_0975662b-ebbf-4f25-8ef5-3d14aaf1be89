# tf-gcp-billing

### Getting Started

#### Prerequisites/Tools required
* Terraform Installed locally
* Terraform Cloud Access
* Pre commit
* tflint

#### Quick setup

* `cd ./terraform` and then Run `terraform init`
* Make some changes to the terraform
* Run `terraform plan` to have a terraform cloud runner execute the plan in the cloud.

### Updating the budget

really all that should happen is the value for the variable `num_installed_operating_rooms` should be updated to
reflect the ground truth in the wild.
