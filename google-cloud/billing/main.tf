resource "google_billing_budget" "monthly_catchall_budget" {
  billing_account = var.billing_account_id
  display_name    = "Apella Catch All - Monthly Budget"

  budget_filter {
    calendar_period        = "MONTH"
    credit_types_treatment = "EXCLUDE_ALL_CREDITS"
  }
  amount {
    specified_amount {
      currency_code = "USD"
      units         = var.monthly_budget_per_operating_room * var.num_installed_operating_rooms
    }
  }

  dynamic "threshold_rules" {
    for_each = var.actual_thresholds
    content {
      threshold_percent = threshold_rules.value
    }
  }

  dynamic "threshold_rules" {
    for_each = var.forcast_thresholds
    content {
      threshold_percent = threshold_rules.value
      spend_basis       = "FORECASTED_SPEND"
    }
  }

  all_updates_rule {
    monitoring_notification_channels = [
      google_monitoring_notification_channel.catchall_email_notification_channel.id,
      google_monitoring_notification_channel.cameron_email_notification_channel.id,
    ]
    disable_default_iam_recipients = true
  }
}

resource "google_billing_budget" "quarterly_catchall_budget" {
  billing_account = var.billing_account_id
  display_name    = "Apella Catch All - Quarterly Budget"

  budget_filter {
    calendar_period        = "QUARTER"
    credit_types_treatment = "EXCLUDE_ALL_CREDITS"
  }

  amount {
    specified_amount {
      currency_code = "USD"
      units         = 3 * var.monthly_budget_per_operating_room * var.num_installed_operating_rooms
    }
  }

  dynamic "threshold_rules" {
    for_each = var.actual_thresholds
    content {
      threshold_percent = threshold_rules.value
    }
  }

  dynamic "threshold_rules" {
    for_each = var.forcast_thresholds
    content {
      threshold_percent = threshold_rules.value
      spend_basis       = "FORECASTED_SPEND"
    }
  }

  all_updates_rule {
    monitoring_notification_channels = [
      google_monitoring_notification_channel.catchall_email_notification_channel.id,
      google_monitoring_notification_channel.cameron_email_notification_channel.id,
    ]
    disable_default_iam_recipients = true
  }
}

resource "google_billing_budget" "annual_catchall_budget" {
  billing_account = var.billing_account_id
  display_name    = "Apella Catch All - Annual Budget"

  budget_filter {
    calendar_period        = "YEAR"
    credit_types_treatment = "EXCLUDE_ALL_CREDITS"
  }

  amount {
    specified_amount {
      currency_code = "USD"
      units         = 12 * var.monthly_budget_per_operating_room * var.num_installed_operating_rooms
    }
  }

  dynamic "threshold_rules" {
    for_each = var.actual_thresholds
    content {
      threshold_percent = threshold_rules.value
    }
  }

  dynamic "threshold_rules" {
    for_each = var.forcast_thresholds
    content {
      threshold_percent = threshold_rules.value
      spend_basis       = "FORECASTED_SPEND"
    }
  }

  all_updates_rule {
    monitoring_notification_channels = [
      google_monitoring_notification_channel.catchall_email_notification_channel.id,
      google_monitoring_notification_channel.cameron_email_notification_channel.id,
    ]
    disable_default_iam_recipients = true
  }
}

resource "google_monitoring_notification_channel" "catchall_email_notification_channel" {
  display_name = "Catchall Email Notification Channel"
  type         = "email"
  project      = var.billing_gcp_project
  labels = {
    email_address = var.email_to_alert
  }
}

resource "google_monitoring_notification_channel" "cameron_email_notification_channel" {
  display_name = "Cameron Email Notification Channel"
  type         = "email"
  project      = var.billing_gcp_project
  labels = {
    email_address = var.cameron_email_to_alert
  }
}

resource "google_monitoring_notification_channel" "catchall_slack_notification_channel" {
  display_name = "Apella Billing Notifications Slack Channel"
  type         = "slack"
  project      = var.billing_gcp_project
  labels = {
    "channel_name" = var.slack_channel_name
  }
  sensitive_labels {
    auth_token = var.SLACK_AUTH_TOKEN
  }
}
