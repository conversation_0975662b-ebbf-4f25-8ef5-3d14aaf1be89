variable "billing_account_id" {
  description = "The default GCP billing account for apella."
  type        = string
  default     = "015CCA-A7978F-DE857E"
}

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
  type        = string
}

variable "GOOGLE_CREDENTIALS" {
  type        = string
  sensitive   = true
  description = "Google Cloud service account credentials"
}

variable "num_installed_operating_rooms" {
  type        = number
  description = "The value for the number of actual operating rooms we are running"
  default     = 369
}

variable "monthly_budget_per_operating_room" {
  type        = number
  description = "The budget target per operating room"
  default     = 500
}

variable "actual_thresholds" {
  description = "list of thresholds to trigger alerting for actual/current spend"
  type        = list(number)
  default     = [0.5, 0.9, 1.0]
}

variable "forcast_thresholds" {
  description = "list of thresholds to trigger alerting for forecasted spend"
  type        = list(number)
  default     = [1.1, 1.5, 2.0]
}

variable "slack_channel_name" {
  description = "The slack channel name to route alerts to"
  default     = "#bot-ops-prod"
  type        = string
}

variable "SLACK_AUTH_TOKEN" {
  type        = string
  sensitive   = true
  description = "Token that allows GCP to talk to slack"
}

variable "billing_gcp_project" {
  type        = string
  description = "Token that allows GCP to talk to slack"
  default     = "prod-gcp-billing-a1e31e"
}

variable "email_to_alert" {
  type        = string
  description = "Who to email when budgets go over thresholds"
  default     = "<EMAIL>"
}

variable "cameron_email_to_alert" {
  type        = string
  description = "Camerons email when budgets go over thresholds"
  default     = "<EMAIL>"
}

# Environment Variables used for naming and labeling
variable "environment" {
  type        = string
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
  default     = "prod"
}

variable "labels" {
  description = "The key/value labels for the master instances."
  type        = map(string)
  default     = {}
}
