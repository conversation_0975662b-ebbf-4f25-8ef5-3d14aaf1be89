resource "google_pubsub_topic" "catchall_pubsub_budget_alerts_topic" {
  name    = "catchall-budget-alerts-topic"
  project = var.billing_gcp_project
}
########################################################################
# Dummy cloud function
#
# In order for a function to be declared in terraform, the code
# must exist in terraform.  However, we don't want to actually include
# the real code here, since we will be doing deployments from elsewhere.
# So we make an dummy .zip file as code stored in storage.
########################################################################

resource "google_storage_bucket" "budget_alert_code_bucket" {
  name                        = "${var.environment}-catchall-budget-alert"
  location                    = var.region
  project                     = var.billing_gcp_project
  uniform_bucket_level_access = true
}

data "archive_file" "placeholder_cloud_func_obj_file" {
  source_dir  = "./assets/dummy-cloud-function"
  type        = "zip"
  output_path = "./assets/dummy-cloud-function.zip"
}

resource "google_storage_bucket_object" "placeholder_cloud_func_obj" {
  name   = "dummy-cloud-function.zip"
  bucket = google_storage_bucket.budget_alert_code_bucket.name
  source = "./assets/dummy-cloud-function.zip"
}

resource "google_cloudfunctions_function" "slack_budget_alerter" {
  name                  = "slack-budget-alerter"
  description           = "Triggers on pub sub event and alerts slack when budgets cross thresholds"
  runtime               = "python310"
  project               = var.billing_gcp_project
  source_archive_bucket = google_storage_bucket.budget_alert_code_bucket.name
  source_archive_object = google_storage_bucket_object.placeholder_cloud_func_obj.name
  entry_point           = "dummy"

  environment_variables = {
    SLACK_AUTH_TOKEN = var.SLACK_AUTH_TOKEN
  }

  event_trigger {
    event_type = "google.pubsub.topic.publish"
    resource   = google_pubsub_topic.catchall_pubsub_budget_alerts_topic.id
  }

  lifecycle {
    # This prevents this dummy function from overwriting
    ignore_changes = [
      entry_point, source_archive_bucket, source_archive_object, labels, environment_variables
    ]
  }


}
