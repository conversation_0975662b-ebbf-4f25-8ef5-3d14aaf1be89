
# Grant each service account serviceAccountUser permissions
resource "google_project_iam_member" "nonprod_platform_tf_sa" {
  project = var.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:<EMAIL>"
}

#non prod platform mig SA needs access to nonprod-github-runner-sa
resource "google_project_iam_member" "nonprod_platform_mig_sa" {
  project = var.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:<EMAIL>"
}

resource "google_service_account_iam_member" "nonprod-compute-instance-service-agent" {
  service_account_id = google_service_account.nonprod_gh_runner_sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:service-${data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
}

/****************************************
   Bastion Host Service Account
 ****************************************/

locals {
  platform-project = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-platform-project
}

resource "google_service_account" "nonprod-bastion-host-sa" {
  account_id   = "nonprod-bastion-host-sa"
  display_name = "Nonproduction bastion host service account"
}

resource "google_service_account_iam_member" "nonprod-compute-instance-service-agent-bastion" {
  service_account_id = google_service_account.nonprod-bastion-host-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:service-${local.platform-project.project_number}@compute-system.iam.gserviceaccount.com"
}


/****************************************
   Google Cloud SQL Proxy Service Account
 ****************************************/
locals {
  internal_project_id = data.terraform_remote_state.nonprod_project_factory.outputs.dev-internal-project.project_id
}


resource "google_service_account" "google_sql_proxy_sa" {
  account_id   = "nonprod-googlesqlproxy"
  display_name = "google sql proxy service account"
}

resource "google_service_account_iam_member" "google_sql_proxy_workload_member" {
  service_account_id = google_service_account.google_sql_proxy_sa.name

  role   = "roles/iam.workloadIdentityUser"
  member = "serviceAccount:${local.internal_project_id}.svc.id.goog[googlesqlproxy/googlesqlproxy-sa]"
}


/****************************************
   GKE Default Service Account
 ****************************************/

resource "google_service_account" "nonprod-gke-sa" {
  account_id   = "nonprod-gke-sa"
  display_name = "Non-production GKE service account"
}

resource "google_service_account_iam_member" "nonprod-compute-instance-service-agent-gke" {
  service_account_id = google_service_account.nonprod-gke-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:service-${local.platform-project.project_number}@compute-system.iam.gserviceaccount.com"
}
