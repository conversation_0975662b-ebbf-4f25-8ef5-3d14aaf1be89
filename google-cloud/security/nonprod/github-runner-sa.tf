locals {
  roleListGithubRunner = []
}


#github runner service account
resource "google_service_account" "nonprod_gh_runner_sa" {
  account_id   = "nonprod-github-runner-sa"
  display_name = "nonprod github runner service account"
}

resource "google_project_iam_member" "github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.nonprod_gh_runner_sa.email}"
}
