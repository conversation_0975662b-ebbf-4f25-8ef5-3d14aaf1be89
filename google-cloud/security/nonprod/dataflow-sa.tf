/****************************************
  Dataflow Service Account
 ****************************************/

locals {
  network-project                = data.terraform_remote_state.nonprod_project_factory.outputs.nonprod-network-project
  dev-dataflow-default-sa        = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  dev-dataflow-compute-sa        = "${data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_number}-<EMAIL>"
  dev-dataflow-compute-system-sa = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
  dev-data-platform-project-id   = data.terraform_remote_state.nonprod_project_factory.outputs.dev-data-platform-project.project_id

  staging-dataflow-default-sa        = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  staging-dataflow-compute-sa        = "${data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number}-<EMAIL>"
  staging-dataflow-compute-system-sa = "service-${data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
  staging-data-platform-project-id   = data.terraform_remote_state.nonprod_project_factory.outputs.staging-data-platform-project.project_id
}

resource "google_service_account" "nonprod-dataflow-sa" {
  account_id   = "nonprod-dataflow-sa"
  display_name = "Nonproduction dataflow service account"
}

# Grant dataflow access to assume other service accounts
# See https://cloud.google.com/dataflow/docs/concepts/security-and-permissions#specifying_a_user-managed_controller_service_account
resource "google_service_account_iam_member" "dev-dataflow-sa-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dev-dataflow-default-sa}"
}
resource "google_service_account_iam_member" "staging-dataflow-sa-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.staging-dataflow-default-sa}"
}

resource "google_service_account_iam_member" "dev-compute-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dev-dataflow-compute-sa}"
}
resource "google_service_account_iam_member" "staging-compute-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.staging-dataflow-compute-sa}"
}

resource "google_service_account_iam_member" "dev-dataflow-uses-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.dev-dataflow-default-sa}"
}
resource "google_service_account_iam_member" "staging-dataflow-uses-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.staging-dataflow-default-sa}"
}

resource "google_service_account_iam_member" "dev-compute-uses-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.dev-dataflow-compute-sa}"
}
resource "google_service_account_iam_member" "staging-compute-uses-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.staging-dataflow-compute-sa}"
}

resource "google_service_account_iam_member" "dev-compute-system-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dev-dataflow-compute-system-sa}"
}
resource "google_service_account_iam_member" "staging-compute-system-creates-tokens-for-nonprod-dataflow" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.staging-dataflow-compute-system-sa}"
}


# Grant network user access
# See https://cloud.google.com/dataflow/docs/guides/specifying-networks#shared

resource "google_project_iam_member" "dev-dataflow-uses-network" {
  project = local.network-project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${local.dev-dataflow-default-sa}"
}
resource "google_project_iam_member" "staging-dataflow-uses-network" {
  project = local.network-project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${local.staging-dataflow-default-sa}"
}

resource "google_project_iam_member" "nonprod-dataflow-network-user" {
  project = local.network-project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${google_service_account.nonprod-dataflow-sa.email}"
}

resource "google_service_account_iam_member" "terraform_creates_dev_dataflow_jobs" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:terraform-sa@${local.dev-data-platform-project-id}.iam.gserviceaccount.com"
}

resource "google_service_account_iam_member" "terraform_creates_staging_dataflow_jobs" {
  service_account_id = google_service_account.nonprod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:terraform-sa@${local.staging-data-platform-project-id}.iam.gserviceaccount.com"
}
