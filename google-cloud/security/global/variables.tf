variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "project_id" {
  description = "GCP Project ID"
  default     = ""
}

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "exclude_folders" {
  description = "Set of folders to exclude from the policy"
  type        = set(string)
  default     = []
}

variable "web_api_projects" {
  description = "The list of web api projects"
  type        = list(string)
  default     = []
}

variable "kubernetes_public_projects" {
  description = "The list of public kubernetes projects"
  type        = list(string)
  default     = []
}

variable "exclude_projects" {
  description = "Set of projects to exclude from the policy"
  type        = set(string)
  default     = []
}
