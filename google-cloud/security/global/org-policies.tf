/******************************************
  Enforce uniform bucket access
 *****************************************/

module "storage-uniform-bucket-level-access" {
  source          = "terraform-google-modules/org-policy/google"
  version         = "5.3.0"
  policy_for      = "organization"
  organization_id = local.org_id
  constraint      = "constraints/storage.uniformBucketLevelAccess"
  policy_type     = "boolean"
  enforce         = true
  exclude_folders = var.exclude_folders
}

/******************************************
  Disable public IP access for sql
 *****************************************/

module "org-disable-sql-publicIp-access" {
  source          = "terraform-google-modules/org-policy/google"
  version         = "5.3.0"
  policy_for      = "organization"
  organization_id = local.org_id
  constraint      = "constraints/sql.restrictPublicIp"
  enforce         = true
  policy_type     = "boolean"
  exclude_folders = var.exclude_folders
}

/******************************************
  Disable External IP addresses on the Org
 *****************************************/

module "org-disable-vm-externalIpaccess" {
  source            = "terraform-google-modules/org-policy/google"
  version           = "5.3.0"
  policy_for        = "organization"
  organization_id   = local.org_id
  constraint        = "constraints/compute.vmExternalIpAccess"
  enforce           = true
  policy_type       = "list"
  allow_list_length = 0
  exclude_folders   = var.exclude_folders
  exclude_projects  = concat(var.web_api_projects, var.kubernetes_public_projects)
}


/******************************************
  Limit resources to US only  
 *****************************************/

module "org-restrict-service-locations" {
  source            = "terraform-google-modules/org-policy/google"
  version           = "5.3.0"
  policy_for        = "organization"
  organization_id   = local.org_id
  constraint        = "constraints/gcp.resourceLocations"
  policy_type       = "list"
  allow             = ["in:us-locations"]
  allow_list_length = 1
  exclude_folders   = var.exclude_folders
  #Secret manager is a global service so any project that uses secrets cannot use this org policy.
  exclude_projects = ["dev-web-api-72f12b", "prod-security-ab8e3e", "nonprod-platform-f24dad"]
}


/******************************************
  Disable creation of the default network
 *****************************************/

module "compute-skip-default-network" {
  source          = "terraform-google-modules/org-policy/google"
  version         = "5.3.0"
  policy_for      = "organization"
  organization_id = local.org_id
  constraint      = "constraints/compute.skipDefaultNetworkCreation"
  policy_type     = "boolean"
  enforce         = true
  exclude_folders = var.exclude_folders
}



locals {
  allowed_domains = ["is:C01vbpfw2"]
  excluded_apella_domain_projects = ["dev-web-apps-be6278", "staging-web-apps-9c7779", "prod-web-apps-82f3c3",
  "dev-web-api-72f12b", "staging-web-api-3efef9", "prod-web-api-7f60bf"]
}

/******************************************
  Domain restricted sharing
 *****************************************/

module "enable-domain-restricted-sharing" {
  source            = "terraform-google-modules/org-policy/google"
  version           = "5.3.0"
  policy_for        = "organization"
  organization_id   = local.org_id
  constraint        = "constraints/iam.allowedPolicyMemberDomains"
  policy_type       = "list"
  allow             = local.allowed_domains
  allow_list_length = 1
  exclude_folders   = var.exclude_folders
  #this is needed because web-apps has a public bucket
  #and web-api has a public cloud-run
  exclude_projects = local.excluded_apella_domain_projects
}

/******************************************

Whitelist domains at Project level
******************************************/
locals {
  project_allowed_domains = ["is:C01vbpfw2", "is:C0147pk0i", "is:C02orx78e"] # apella.io, datadog, encord
}

module "enable-domain-restricted-sharing-projects" {
  source            = "terraform-google-modules/org-policy/google"
  version           = "5.3.0"
  for_each          = toset(var.exclude_projects)
  policy_for        = "project"
  project_id        = each.key
  constraint        = "constraints/iam.allowedPolicyMemberDomains"
  policy_type       = "list"
  allow             = local.project_allowed_domains
  allow_list_length = 3
}

data "google_projects" "dev-apella-projects" {
  filter = "parent.id: 148659832539"
}

data "google_projects" "staging-apella-projects" {
  filter = "parent.id: 478269449143"
}

data "google_projects" "prod-apella-projects" {
  filter = "parent.id: 908426907179"
}

locals {
  expanded_allowed_domains  = ["is:C01vbpfw2", "is:C0147pk0i"] # apella.io, datadog
  shared_projects           = toset(["prod-log-f5365b"])
  dev_projects              = toset([for project in data.google_projects.dev-apella-projects.projects : project.project_id])
  staging_projects          = toset([for project in data.google_projects.staging-apella-projects.projects : project.project_id])
  prod_projects             = toset([for project in data.google_projects.prod-apella-projects.projects : project.project_id])
  all_projects              = setunion(local.dev_projects, local.staging_projects, local.prod_projects, local.shared_projects)
  all_non_excluded_projects = setsubtract(setsubtract(local.all_projects, toset(var.exclude_projects)), toset(local.excluded_apella_domain_projects))
}


module "enable-domain-restricted-sharing-datadog-projects" {
  source            = "terraform-google-modules/org-policy/google"
  version           = "5.3.0"
  for_each          = local.all_non_excluded_projects
  policy_for        = "project"
  project_id        = each.value
  constraint        = "constraints/iam.allowedPolicyMemberDomains"
  policy_type       = "list"
  allow             = local.expanded_allowed_domains
  allow_list_length = 2
}

/******************************************
  Require OS Login
  https://cloud.google.com/compute/docs/oslogin/manage-oslogin-in-an-org#workaround
 *****************************************/

module "org-require-oslogin" {
  source          = "terraform-google-modules/org-policy/google"
  version         = "5.3.0"
  policy_for      = "organization"
  organization_id = local.org_id
  constraint      = "constraints/compute.requireOsLogin"
  policy_type     = "boolean"
  enforce         = true
}
