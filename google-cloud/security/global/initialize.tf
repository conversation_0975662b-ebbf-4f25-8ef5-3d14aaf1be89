/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-security-global"
    }
  }

  required_version = ">= 1.7.5"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 5.0"
    }
  }
}

provider "google" {
  #project = var.project_id
  region = var.region
}

locals {
  org_id = data.google_organization.org.org_id
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "terraform_remote_state" "prod_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

/******************************************
  Fetch Sandbox Folder
 *****************************************/

data "google_active_folder" "sbx" {
  display_name = "sbx"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

/******************************************
  Fetch dev folder
 *****************************************/

data "google_active_folder" "dev" {
  display_name = "dev"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

/******************************************
  Fetch staging folder
 *****************************************/

data "google_active_folder" "staging" {
  display_name = "staging"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

/******************************************
  Fetch shared folder
 *****************************************/

data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${data.google_organization.org.org_id}"
}

/******************************************
  Fetch Prod folder
 *****************************************/

data "google_active_folder" "prod" {
  display_name = "prod"
  parent       = "organizations/${data.google_organization.org.org_id}"
}
