
# Grant each service account serviceAccountUser permissions
resource "google_project_iam_member" "prod_platform_tf_sa" {
  project = var.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:<EMAIL>"
}

#prod platform mig SA needs access to prod-github-runner-sa
resource "google_project_iam_member" "prod_platform_mig_sa" {
  project = var.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:${data.terraform_remote_state.prod_project_factory.outputs.prod-platform-project.project_number}@cloudservices.gserviceaccount.com"
}

resource "google_service_account_iam_member" "admin-account-iam" {
  service_account_id = google_service_account.prod_gh_runner_sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:service-${data.terraform_remote_state.prod_project_factory.outputs.prod-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
}

/****************************************
   Bastion Host Service Account
 ****************************************/

resource "google_service_account" "prod-bastion-host-sa" {
  account_id   = "prod-bastion-host-sa"
  display_name = "Production bastion host service account"
}

resource "google_service_account_iam_member" "prod-compute-instance-service-agent-bastion" {
  service_account_id = google_service_account.prod-bastion-host-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:service-${data.terraform_remote_state.prod_project_factory.outputs.prod-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
}
