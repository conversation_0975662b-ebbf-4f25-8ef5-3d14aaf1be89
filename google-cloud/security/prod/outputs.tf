// Deprecated in favor of `github-runner-sa` below.
output "prod-github-runner-sa" {
  value = google_service_account.prod_gh_runner_sa.email
}

output "github-runner-sa" {
  value = google_service_account.prod_gh_runner_sa.email
}

// Deprecated in favor of `bastion-host-sa` below.
output "prod-bastion-host-sa" {
  value = google_service_account.prod-bastion-host-sa.email
}

output "bastion-host-sa" {
  value = google_service_account.prod-bastion-host-sa.email
}

output "prod-dataflow-sa" {
  value = google_service_account.prod-dataflow-sa.email
}

output "prod-rtedgemodel-sa" {
  value = google_service_account.prod-rtedgemodel-sa.email
}

output "github-actions-shared-sa" {
  value = google_service_account.github_actions_shared_sa
}
