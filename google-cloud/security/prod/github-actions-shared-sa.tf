locals {
  roles = [
    "roles/iam.workloadIdentityUser"
  ]
}

resource "google_service_account" "github_actions_shared_sa" {
  account_id   = "github-actions-shared-sa"
  display_name = "Github Actions Shared SA"
  description  = "The service account for Github-hosted actions to utilize for permissions"
}

resource "google_project_iam_member" "github_actions_shared_sa_roles" {
  for_each = toset(local.roles)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.github_actions_shared_sa.email}"
}
