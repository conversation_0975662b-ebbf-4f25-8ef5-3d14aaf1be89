/****************************************
  Dataflow Service Account
 ****************************************/

locals {
  network-project               = data.terraform_remote_state.prod_project_factory.outputs.prod-network-project
  dataflow-default-sa           = "service-${data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_number}@dataflow-service-producer-prod.iam.gserviceaccount.com"
  dataflow-compute-sa           = "${data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_number}-<EMAIL>"
  dataflow-compute-system-sa    = "service-${data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_number}@compute-system.iam.gserviceaccount.com"
  prod-data-platform-project-id = data.terraform_remote_state.prod_project_factory.outputs.prod-data-platform-project.project_id
}

resource "google_service_account" "prod-dataflow-sa" {
  account_id   = "prod-dataflow-sa"
  display_name = "production dataflow service account"
}

# Grant dataflow access to assume other service accounts
# See https://cloud.google.com/dataflow/docs/concepts/security-and-permissions#specifying_a_user-managed_controller_service_account
resource "google_service_account_iam_member" "prod-dataflow-service-agent" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dataflow-default-sa}"
}

resource "google_service_account_iam_member" "nonprod-compute-engine-service-agent-dataflow" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dataflow-compute-sa}"
}

resource "google_service_account_iam_member" "prod-dataflow-service-agent-user" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.dataflow-default-sa}"
}

resource "google_service_account_iam_member" "prod-dataflow-service-agent-compute-user" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${local.dataflow-compute-sa}"
}

resource "google_service_account_iam_member" "prod-compute-system-service-agent-dataflow" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${local.dataflow-compute-system-sa}"
}


# Grant network user access
# See https://cloud.google.com/dataflow/docs/guides/specifying-networks#shared

resource "google_project_iam_member" "prod-dataflow-service-agent-network" {
  project = local.network-project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${local.dataflow-default-sa}"
}

resource "google_project_iam_member" "prod-dataflow-network-user" {
  project = local.network-project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${google_service_account.prod-dataflow-sa.email}"
}

resource "google_service_account_iam_member" "terraform_creates_prod_dataflow_jobs" {
  service_account_id = google_service_account.prod-dataflow-sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:terraform-sa@${local.prod-data-platform-project-id}.iam.gserviceaccount.com"
}
