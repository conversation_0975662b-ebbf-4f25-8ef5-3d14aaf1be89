locals {
  roleListGithubRunner = []
}


#github runner service account
resource "google_service_account" "prod_gh_runner_sa" {
  account_id   = "prod-github-runner-sa"
  display_name = "prod github runner service account"
}

resource "google_project_iam_member" "prod_github_group_roles" {
  for_each = toset(local.roleListGithubRunner)
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.prod_gh_runner_sa.email}"
}
