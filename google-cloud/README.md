# Google Cloud Infrastructure

This repository contains the infrastructure as code for Apella's Google Cloud.
/project-factory contains directories for each environment: /nonprod (includes `dev` and `stage`), /prod, and /sandbox.
Within each directory are Google Project definitions for the respective environment. For example, 
/project-factory/nonprod/dev-ml.tf defines the Google Project configuration for `dev-ml`.

## Using Existing Google Projects
If a new applications is a natural fit for an existing Google Project, add it to the appropriate /google-cloud directory's
configuration files. For example, if creating a new machine learning application, update the /google-cloud/machine-learning
configuration files as needed.

## Configuring a new Google Project
1. Create new configurations in /google-cloud/project-factory. For example, for applications that need dev, staging, and
prod environments, add `dev-<APP-NAME>` and `staging-<APP-NAME>` to /google-cloud/project-factory/nonprod and
`prod-<APP-NAME>` to /google-cloud/project-factory/prod. Once this is merged, these configs will be used to create
Google Projects in those environments.
2.  Create a directory in /google-cloud and populate it with the necessary Terraform configuration files. 
See other directories in /google-cloud for examples. 
    1. Have the PR reviewed and merge it once it's approved. A Terraform Cloud Plan will be created upon merging. 
    2. Apply the plan in the nonprod ([e.g.](https://app.terraform.io/app/apella/workspaces/gcp-project-factory-nonprod)) 
    and prod ([e.g.](https://app.terraform.io/app/apella/workspaces/gcp-project-factory-prod)) Workspaces.
3. Terraform Cloud needs to be given security keys so that it can update the Google Projects. 
Go to the newly created GCP projects, and create keys for the `terraform-sa` service account. 
We’ll need these keys for the Terraform Cloud Workspace. To get the keys follow these steps:
   1. From the projects list in the Google Cloud Console, select the project (e.g. `dev-<APP-NAME>`). 
   2. If the APIs & services page isn't already open, open the left side menu and select **APIs & services**. 
   3. On the left, choose **Credentials**. 
   4. Click **Create credentials** and then select **API key**.
4. Go to the corresponding workspace in Terraform Cloud ([e.g.](https://app.terraform.io/app/apella/workspaces/dev-media-asset/variables))
   1. Click **Add variable** under **Workspace variables**
   2. Set **Key** to the string *GOOGLE_CREDENTIALS*
   3. Set **Value** to the API key (remove any new lines)
   4. Set **Category** to the string *env*
5. Click "Apply variable set"
6. Repeat steps 3-5 for each Terraform Cloud Workspace.

