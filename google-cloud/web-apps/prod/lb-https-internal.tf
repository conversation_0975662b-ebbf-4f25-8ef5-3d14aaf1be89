# This Load Balancer is the HTTPS Load Balancer with three backends
# for Internal:   2 Bucket and 1 service endpoint 

# ******************************
# locals used by this terraform
# ******************************
locals {
  internal_https_prefix         = "${var.environment}-https-internal"
  tensor_flow_https_prefix      = "${var.environment}-https-tensor-flow"
  technology_radar_https_prefix = "${var.environment}-https-technology-radar"
}

# ******************************
# 1.  Global IP Address for Load Balancer
# ******************************
resource "google_compute_global_address" "https_internal" {
  provider = google
  name     = "${local.internal_https_prefix}-global-address"
}

# ******************************
# 2. Forwarding Rules
# ******************************
resource "google_compute_global_forwarding_rule" "https_internal" {
  provider              = google
  name                  = "${local.internal_https_prefix}-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_internal.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.https_internal.self_link
}

# ******************************
# 3. GCP target proxy
# ******************************
resource "google_compute_target_https_proxy" "https_internal" {
  provider   = google
  name       = "${local.internal_https_prefix}-proxy"
  url_map    = google_compute_url_map.https_internal.self_link
  ssl_policy = google_compute_ssl_policy.standard-ssl-policy.name

  ssl_certificates = [
    google_compute_managed_ssl_certificate.https_internal_cert.self_link
  ]
}

# ******************************
# 4. Google SSL Managed Certificate
# ******************************
# internal
resource "google_compute_managed_ssl_certificate" "https_internal_cert" {
  name = "${local.internal_https_prefix}-certificate"

  managed {
    domains = ["${var.internal_bucket_name}"]
  }
}

# ******************************
# 5. URL Map for Backend Bucket - static content
# ******************************
# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map#example-usage---url-map-basic
resource "google_compute_url_map" "https_internal" {
  project         = var.project_id
  name            = "${local.internal_https_prefix}-url-map"
  default_service = google_compute_backend_bucket.https_internal.self_link


  host_rule {
    hosts        = ["${var.internal_bucket_name}"]
    path_matcher = "path-matcher-1"
  }

  path_matcher {
    name            = "path-matcher-1"
    default_service = google_compute_backend_bucket.https_internal.id
    path_rule {
      paths   = ["/*"]
      service = google_compute_backend_bucket.https_internal.id
    }
    path_rule {
      paths   = ["/v1/*"]
      service = google_compute_backend_service.https_internal.id
    }
    path_rule {
      paths   = ["/models/*"]
      service = google_compute_backend_bucket.https_tensor_flow.id
    }
    path_rule {
      paths   = ["/technology-radar/*"]
      service = google_compute_backend_bucket.https_technology_radar.id
    }
  }

}

# ******************************
# 6. GCS Website backend bucket with CDN Enabled
# ******************************
resource "google_compute_backend_bucket" "https_internal" {
  provider                = google-beta
  name                    = "${local.internal_https_prefix}-backend-bucket"
  description             = "Contains files needed by the internal website"
  bucket_name             = module.internal_bucket.bucket.name
  enable_cdn              = true
  custom_response_headers = var.bucket_custom_response_headers
}

resource "google_compute_backend_bucket" "https_tensor_flow" {
  provider    = google
  name        = "${local.tensor_flow_https_prefix}-backend-bucket"
  description = "Contains files needed by the Tensor Flow models"
  bucket_name = module.tensor_flow_bucket.bucket.name
  enable_cdn  = false
}

resource "google_compute_backend_bucket" "https_technology_radar" {
  provider    = google
  name        = "${local.technology_radar_https_prefix}-backend-bucket"
  description = "Contains files needed by the technology radar"
  bucket_name = module.technology_radar_bucket.bucket.name
  enable_cdn  = false
}

# ******************************
# 7a. GCS Bucket for Internal Site and tensor flow
# ******************************
module "internal_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD

  bucket_name                 = var.internal_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment

  iam_members = [{
    role = "roles/storage.objectViewer"
    # needs to be domain bucket 
    # A domain restriction organization policy is in place. Only members of allowed domains can be added as members of the policy. 
    member = "allUsers"
    #member = "user:<EMAIL>"
  }]

  website = {
    main_page_suffix = "index.html"
    not_found_page   = "index.html"
  }

  lifecycle_rules = [{
    action = {
      type = "Delete"
    }
    condition = {
      num_newer_versions         = 3
      days_since_noncurrent_time = 30
    }
  }]

  labels = var.labels
}

/******************************************
	7b. GCS Bucket Tensor Flow
 *****************************************/

module "tensor_flow_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD

  bucket_name                 = var.tensor_flow_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment

  iam_members = [{
    role   = "roles/storage.objectViewer"
    member = "allUsers"
    #member = "user:<EMAIL>"
  }]

  lifecycle_rules = [{
    action = {
      type = "Delete"
    }
    condition = {
      num_newer_versions         = 3
      days_since_noncurrent_time = 30
    }
    } /*,
  {
    action = {
      type = "Delete"
    }
    condition = {
      days_since_noncurrent_time = 30
    }
  }*/
  ]

  force_destroy = true

  labels = var.labels
}

/******************************************
	7c. GCS Bucket Technology Radar
 *****************************************/
module "technology_radar_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD

  bucket_name                 = var.technology_radar_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment

  iam_members = [{
    role   = "roles/storage.objectViewer"
    member = "allUsers"
    #member = "user:<EMAIL>"
  }]

  lifecycle_rules = [{
    action = {
      type = "Delete"
    }
    condition = {
      num_newer_versions         = 3
      days_since_noncurrent_time = 30
    }
    } /*,
  {
    action = {
      type = "Delete"
    }
    condition = {
      days_since_noncurrent_time = 30
    }
  }*/
  ]

  force_destroy = true

  labels = var.labels
}

# ******************************
# 8. Network Endpoint Group (custom Origins)
# ******************************

#   Network endpoints represent your services (applications, load balancing) and diverse infrastructure (VM instances, containers etc) in a standard manner regardless of their location
#   Name, Type (zonal or internet), network (nonprod), default port (443)
resource "google_compute_global_network_endpoint_group" "https_internal" {
  provider              = google-beta
  name                  = "${local.internal_https_prefix}-network-endpoint"
  network_endpoint_type = "INTERNET_FQDN_PORT"
  default_port          = "443"
}

resource "google_compute_global_network_endpoint" "https_internal" {
  provider                      = google-beta
  global_network_endpoint_group = google_compute_global_network_endpoint_group.https_internal.id
  fqdn                          = local.api_domain_name
  port                          = google_compute_global_network_endpoint_group.https_internal.default_port
}

# ******************************
# 9. Backend Services for Docker
# ******************************

resource "google_compute_backend_service" "https_internal" {
  provider = google-beta
  name     = "${local.internal_https_prefix}-backend-service"
  # CDN enabled for bucket but not for service
  enable_cdn                      = false
  timeout_sec                     = 30
  connection_draining_timeout_sec = 10
  protocol                        = "HTTPS"
  custom_request_headers          = ["host: ${google_compute_global_network_endpoint.https_internal.fqdn}"]
  #custom_response_headers         = ["X-Cache-Hit: {cdn_cache_status}"]
  log_config {
    enable      = true
    sample_rate = 1.0
  }

  backend {
    balancing_mode = "UTILIZATION"
    group          = google_compute_global_network_endpoint_group.https_internal.id
  }
}
