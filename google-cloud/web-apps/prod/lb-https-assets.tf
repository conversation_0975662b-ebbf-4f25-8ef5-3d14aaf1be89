# This Load Balancer is the HTTPS Load Balancer with one backend
# for Apella assets

# ******************************
# locals used by this terraform
# ******************************
locals {
  assets_https_prefix = "${var.environment}-https-assets"
}

# ******************************
# 1.  Global IP Address for Load Balancer
# ******************************
resource "google_compute_global_address" "https_assets" {
  provider = google
  name     = "${local.assets_https_prefix}-global-address"
}

# ******************************
# 2. Forwarding Rules
# ******************************
resource "google_compute_global_forwarding_rule" "https_assets" {
  provider              = google
  name                  = "${local.assets_https_prefix}-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_assets.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.https_assets.self_link
}

# ******************************
# 3. GCP target proxy
# ******************************
resource "google_compute_target_https_proxy" "https_assets" {
  provider   = google
  name       = "${local.assets_https_prefix}-proxy"
  url_map    = google_compute_url_map.https_assets.self_link
  ssl_policy = google_compute_ssl_policy.standard-ssl-policy.name

  ssl_certificates = [
    google_compute_managed_ssl_certificate.https_assets_cert.self_link
  ]
}

# ******************************
# 4. Google SSL Managed Certificate
# ******************************
resource "google_compute_managed_ssl_certificate" "https_assets_cert" {
  name = "${local.assets_https_prefix}-certificate"

  managed {
    domains = ["${var.assets_domain_name}.${var.subdomain}"]
  }
}

# ******************************
# 5. URL Map for Backend Bucket - static content
# ******************************
# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map#example-usage---url-map-basic
resource "google_compute_url_map" "https_assets" {
  project         = var.project_id
  name            = "${local.assets_https_prefix}-url-map"
  default_service = google_compute_backend_bucket.https_assets.self_link


  host_rule {
    hosts        = ["${var.assets_domain_name}.${var.subdomain}"]
    path_matcher = "path-matcher-1"
  }

  path_matcher {
    name            = "path-matcher-1"
    default_service = google_compute_backend_bucket.https_assets.id
    path_rule {
      paths   = ["/*"]
      service = google_compute_backend_bucket.https_assets.id
    }
  }
}

# ******************************
# 6. GCS Website backend bucket with CDN Enabled
# ******************************
resource "google_compute_backend_bucket" "https_assets" {
  provider    = google
  name        = "${local.assets_https_prefix}-backend-bucket"
  description = "Contains files needed by the assets for all websites"
  bucket_name = module.assets_bucket.bucket.name
  enable_cdn  = true
}

# ******************************
# 7a. GCS Bucket for assets
# ******************************
module "assets_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD

  bucket_name                 = var.assets_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment

  iam_members = [{
    role = "roles/storage.legacyObjectReader"
    # needs to be domain bucket 
    # A domain restriction organization policy is in place. Only members of allowed domains can be added as members of the policy. 
    member = "allUsers"
  }]

  cors = {
    origin          = ["*"]
    method          = ["GET"]
    response_header = var.cors_response_header
    max_age_seconds = var.cors_max_age_seconds
  }

  lifecycle_rules = [{
    action = {
      type = "Delete"
    }
    condition = {
      num_newer_versions         = 3
      days_since_noncurrent_time = 30
    }
  }]

  labels = var.labels
}

# ******************************
# 8. Network Endpoint Group (custom Origins)
# ******************************

#   Network endpoints represent your services (applications, load balancing) and diverse infrastructure (VM instances, containers etc) in a standard manner regardless of their location
#   Name, Type (zonal or internet), network (nonprod), default port (443)
resource "google_compute_global_network_endpoint_group" "https_assets" {
  provider              = google-beta
  name                  = "${local.assets_https_prefix}-network-endpoint"
  network_endpoint_type = "INTERNET_FQDN_PORT"
  default_port          = "443"
}
