/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "prod-web-apps-82f3c3"
environment = "prod"


labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-web-apps"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-emails-and-phone-numbers"
  "vanta-contains-ephi"      = "true"
}

/******************************************
	GCS Bucket Related Variables
 *****************************************/
dashboard_bucket_name        = "dashboard.apella.io"
internal_bucket_name         = "internal.apella.io"
tensor_flow_bucket_name      = "tensorflow.apella.io"
assets_bucket_name           = "at-prod-assets"
technology_radar_bucket_name = "at-prod-technology-radar"

bucket_custom_response_headers = [
  "X-Frame-Options: SAMEORIGIN",
  "X-XSS-Protection: 0",
  "X-Content-Type-Options: nosniff",
  "Strict-Transport-Security: max-age=31536000; includeSubDomains",
  "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://beacon-v2.helpscout.net/ https://www.googletagmanager.com https://cdn.jsdelivr.net/npm/ https://*.amplitude.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; object-src 'none'; connect-src 'self' https://apella.us.auth0.com https://api.apella.io https://auth.apella.io https://*.amplitude.com https://app.launchdarkly.com https://beaconapi.helpscout.net https://clientstream.launchdarkly.com https://cubejs.apella.io https://*.cloudfront.net https://events.launchdarkly.com https://*.ingest.sentry.io https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://browser-intake-datadoghq.com; font-src https://assets.apella.io https://fonts.gstatic.com; img-src 'self' blob: https://assets.apella.io https://lh3.googleusercontent.com https://*.google-analytics.com https://*.googletagmanager.com https://cdn.mcauto-images-production.sendgrid.net https://d33v4339jhl8k0.cloudfront.net; worker-src blob:; media-src 'self' blob:; frame-src 'self' https://auth.apella.io;",
  "Referrer-Policy: no-referrer, strict-origin-when-cross-origin",
  "Permissions-Policy: fullscreen=(self), camera=(), microphone=()",
  "Cross-Origin-Opener-Policy: same-origin",
  "Cross-Origin-Resource-Policy: same-site"
]

/******************************************
	Cloudflare related Variables
 *****************************************/
subdomain          = "apella.io"
assets_domain_name = "assets"
