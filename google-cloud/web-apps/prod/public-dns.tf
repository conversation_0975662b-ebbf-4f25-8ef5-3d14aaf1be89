/****************************************************************
Create public DNS record for api gateway
*****************************************************************/

data "cloudflare_zones" "default" {
  filter {
    name = "apella.io"
  }
}

resource "cloudflare_record" "internal" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "internal"
  value   = google_compute_global_address.https_internal.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "dashboard" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "dashboard"
  value   = google_compute_global_address.https_dashboard.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "design" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "design"
  value   = "domains.chromatic.com"
  type    = "CNAME"
  ttl     = 300
}

resource "cloudflare_record" "dashboard-design" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "dashboard-design"
  value   = "domains.chromatic.com"
  type    = "CNAME"
  ttl     = 300
}

resource "cloudflare_record" "assets" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = var.assets_domain_name
  value   = google_compute_global_address.https_assets.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "boards" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "boards"
  value   = google_compute_global_address.https_boards.address
  type    = "A"
  ttl     = 300
}