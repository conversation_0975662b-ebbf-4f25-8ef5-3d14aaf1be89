# This Load Balancer is the HTTPS Load Balancer
# for boards reusing Dashboard's two backends 1. Bucket and 2. API 

# ******************************
# locals used by this terraform
# ******************************
locals {
  boards_https_prefix = "${var.environment}-https-boards"
  boards_domain       = "boards.${var.environment}.${var.domain_name}"
}

# ******************************
# 1.  Global IP Address for Load Balancer
# ******************************
resource "google_compute_global_address" "https_boards" {
  provider = google
  name     = "${local.boards_https_prefix}-global-address"
}

# ******************************
# 2. Forwarding Rules
# ******************************
resource "google_compute_global_forwarding_rule" "https_boards" {
  provider              = google
  name                  = "${local.boards_https_prefix}-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_boards.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.https_boards.self_link
}

# ******************************
# 3. GCP target proxy
# ******************************
resource "google_compute_target_https_proxy" "https_boards" {
  provider   = google
  name       = "${local.boards_https_prefix}-proxy"
  url_map    = google_compute_url_map.https_boards.self_link
  ssl_policy = google_compute_ssl_policy.standard-ssl-policy.name
  ssl_certificates = [
    google_compute_managed_ssl_certificate.https_boards_cert.self_link
  ]
}

# ******************************
# 4. Google SSL Managed Certificate
# ******************************
resource "google_compute_managed_ssl_certificate" "https_boards_cert" {
  name = "${local.boards_https_prefix}-certificate"

  managed {
    domains = ["${local.boards_domain}"]
  }
}

# ******************************
# 5. URL Map for Backend Bucket - static content
# ******************************
# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map#example-usage---url-map-basic
resource "google_compute_url_map" "https_boards" {
  project         = var.project_id
  name            = "${local.boards_https_prefix}-url-map"
  default_service = google_compute_backend_bucket.https_dashboard.self_link


  host_rule {
    hosts        = ["${local.boards_domain}"]
    path_matcher = "path-matcher-1"
  }

  path_matcher {
    name = "path-matcher-1"
    # this is needed since it gives an error if default is not there however a duplicate rule seem to exist
    default_service = google_compute_backend_bucket.https_dashboard.id

    path_rule {
      paths   = ["/*"]
      service = google_compute_backend_bucket.https_dashboard.id
    }
    path_rule {
      paths   = ["/v1/*"]
      service = google_compute_backend_service.https_dashboard.id
    }
  }

}
