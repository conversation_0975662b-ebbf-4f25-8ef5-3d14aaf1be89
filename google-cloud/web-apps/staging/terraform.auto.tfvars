/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "staging-web-apps-9c7779"
environment = "staging"


labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "true"
  "vanta-description"        = "staging-web-apps"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-emails-and-phone-numbers"
  "vanta-contains-ephi"      = "true"
  "vanta-no-alert"           = "it-stores-data-from-staging-environment"
}

/******************************************
	GCS Bucket Related Variables
 *****************************************/
dashboard_bucket_name   = "dashboard.staging.apella.io"
internal_bucket_name    = "internal.staging.apella.io"
design_bucket_name      = "design.staging.apella.io"
tensor_flow_bucket_name = "tensorflow.staging.apella.io"

bucket_custom_response_headers = [
  "X-Frame-Options: SAMEORIGIN",
  "X-XSS-Protection: 0",
  "X-Content-Type-Options: nosniff",
  "Strict-Transport-Security: max-age=31536000; includeSubDomains",
  "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://beacon-v2.helpscout.net/ https://*.googletagmanager.com https://cdn.jsdelivr.net/npm/ https://*.amplitude.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; object-src 'none'; connect-src 'self' https://auth.staging.apella.io https://api.staging.apella.io https://*.amplitude.com https://app.launchdarkly.com https://beaconapi.helpscout.net https://clientstream.launchdarkly.com https://cubejs.staging.apella.io https://*.cloudfront.net https://events.launchdarkly.com https://*.ingest.sentry.io https://*.google-analytics.com https://*.analytics.google.com https://*.googletagmanager.com https://browser-intake-datadoghq.com; font-src https://assets.apella.io https://fonts.gstatic.com; img-src 'self' blob: https://assets.apella.io https://lh3.googleusercontent.com https://*.google-analytics.com https://*.googletagmanager.com https://cdn.mcauto-images-production.sendgrid.net; worker-src blob:; media-src 'self' blob:;",
  "Referrer-Policy: no-referrer, strict-origin-when-cross-origin",
  "Permissions-Policy: fullscreen=(self), camera=(), microphone=()",
  "Cross-Origin-Embedder-Policy: credentialless",
  "Cross-Origin-Opener-Policy: same-origin",
  "Cross-Origin-Resource-Policy: same-site"
]
