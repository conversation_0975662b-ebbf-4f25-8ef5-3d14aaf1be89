/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "project_id" {
  description = "GCP Project ID"
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "labels" {
  description = "The key/value labels for resources."
  type        = map(string)
  default     = {}
}

/******************************************
	GCS Bucket Related Variables
 *****************************************/
variable "dashboard_bucket_name" {
  description = "Name of dashboard gcs bucket"
  type        = string
}

variable "internal_bucket_name" {
  description = "Name of internal gcs bucket"
  type        = string
}

variable "design_bucket_name" {
  description = "Name of design storybook gcs bucket"
  type        = string
}

variable "tensor_flow_bucket_name" {
  description = "Name of tensor flow gcs bucket"
  type        = string
}

variable "bucket_custom_response_headers" {
  description = "Custom HTTP Response Headers"
  type        = list(string)
}

/*****************************************************************************
cloudflare variables
******************************************************************************/
variable "cloudflare_api_token" {
  description = "cloudflare api token"
  type        = string
  sensitive   = true
}

variable "cloudflare_account_id" {
  description = "cloudflare account id"
  type        = string
  sensitive   = true
}
