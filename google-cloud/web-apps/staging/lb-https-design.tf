# This Load Balancer is the HTTPS Load Balancer with two backends
# for Design:   1. Bucket and 2. API 

# ******************************
# locals used by this terraform
# ******************************
locals {
  design_https_prefix = "${var.environment}-https-design"
}

# ******************************
# 1.  Global IP Address for Load Balancer
# ******************************
resource "google_compute_global_address" "https_design" {
  provider = google
  name     = "${local.design_https_prefix}-global-address"
}

# ******************************
# 2. Forwarding Rules
# ******************************
resource "google_compute_global_forwarding_rule" "https_design" {
  provider              = google
  name                  = "${local.design_https_prefix}-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_design.address
  ip_protocol           = "TCP"
  port_range            = "443"
  target                = google_compute_target_https_proxy.https_design.self_link
}

# ******************************
# 3. GCP target proxy
# ******************************
resource "google_compute_target_https_proxy" "https_design" {
  provider   = google
  name       = "${local.design_https_prefix}-proxy"
  url_map    = google_compute_url_map.https_design.self_link
  ssl_policy = google_compute_ssl_policy.standard-ssl-policy.name

  ssl_certificates = [
    google_compute_managed_ssl_certificate.https_design_cert.self_link
  ]
}

# ******************************
# 4. Google SSL Managed Certificate
# ******************************
resource "google_compute_managed_ssl_certificate" "https_design_cert" {
  name = "${local.design_https_prefix}-certificate"

  managed {
    domains = ["${var.design_bucket_name}"]
  }
}


# ******************************
# 5. URL Map for Backend Bucket - static content
# ******************************
# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map#example-usage---url-map-basic
resource "google_compute_url_map" "https_design" {
  project         = var.project_id
  name            = "${local.design_https_prefix}-url-map"
  default_service = google_compute_backend_bucket.https_design.self_link


  host_rule {
    hosts        = ["${var.design_bucket_name}"]
    path_matcher = "path-matcher-1"
  }

  path_matcher {
    name            = "path-matcher-1"
    default_service = google_compute_backend_bucket.https_design.id
    path_rule {
      paths   = ["/*"]
      service = google_compute_backend_bucket.https_design.id
    }
  }

}

# ******************************
# 6. GCS Website backend bucket with CDN Enabled
# ******************************
resource "google_compute_backend_bucket" "https_design" {
  provider                = google-beta
  name                    = "${local.design_https_prefix}-backend-bucket"
  description             = "Contains files needed by the design website"
  bucket_name             = module.design_bucket.bucket.name
  enable_cdn              = true
  custom_response_headers = var.bucket_custom_response_headers
}

# ******************************
# 7. GCS Bucket for Design Site
# ******************************
module "design_bucket" {
  source  = "app.terraform.io/apella/cloud-storage/google"
  version = "1.2.9"
  # storage class will default to STANDARD

  bucket_name                 = var.design_bucket_name
  project_id                  = var.project_id
  location                    = var.region
  uniform_bucket_level_access = true
  environment                 = var.environment

  iam_members = [{
    role = "roles/storage.objectViewer"
    # needs to be domain bucket 
    # A domain restriction organization policy is in place. Only members of allowed domains can be added as members of the policy. 
    member = "allUsers"
    #member = "user:<EMAIL>"
  }]

  website = {
    main_page_suffix = "index.html"
    not_found_page   = "index.html"
  }

  lifecycle_rules = [{
    action = {
      type = "Delete"
    }
    condition = {
      num_newer_versions         = 3
      days_since_noncurrent_time = 30
    }
  }]

  force_destroy = true

  labels = var.labels
}
