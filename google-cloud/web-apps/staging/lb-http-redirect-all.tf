# This Load Balancer is setup for Redirect of HTTP to HTTPS
# Reference:  https://cloud.google.com/cdn/docs/setting-up-http-https-redirect

# ******************************
# locals used by this terraform
# ******************************
locals {
  redirect_http_prefix = "${var.environment}-http-redirect-https"
}

# ******************************
# 2. Forwarding Rules for TCP 80
# ******************************
resource "google_compute_global_forwarding_rule" "http_redirect_internal" {
  provider              = google
  name                  = "${local.redirect_http_prefix}-internal-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_internal.address
  ip_protocol           = "TCP"
  port_range            = "80"
  target                = google_compute_target_http_proxy.http_redirect_internal.self_link
}

resource "google_compute_global_forwarding_rule" "http_redirect_dahsboard" {
  provider              = google
  name                  = "${local.redirect_http_prefix}-dashboard-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_dashboard.address
  ip_protocol           = "TCP"
  port_range            = "80"
  target                = google_compute_target_http_proxy.http_redirect_dashboard.self_link
}

resource "google_compute_global_forwarding_rule" "http_redirect_design" {
  provider              = google
  name                  = "${local.redirect_http_prefix}-design-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_design.address
  ip_protocol           = "TCP"
  port_range            = "80"
  target                = google_compute_target_http_proxy.http_redirect_design.self_link
}

resource "google_compute_global_forwarding_rule" "http_redirect_boards" {
  provider              = google
  name                  = "${local.redirect_http_prefix}-boards-forwarding-rule"
  load_balancing_scheme = "EXTERNAL"
  ip_address            = google_compute_global_address.https_boards.address
  ip_protocol           = "TCP"
  port_range            = "80"
  target                = google_compute_target_http_proxy.http_redirect_boards.self_link
}

# ******************************
# 3. GCP target proxy 
# ******************************
resource "google_compute_target_http_proxy" "http_redirect_internal" {
  project = var.project_id
  name    = "${local.redirect_http_prefix}-internal-proxy"
  url_map = google_compute_url_map.http_redirect.self_link
}

resource "google_compute_target_http_proxy" "http_redirect_dashboard" {
  project = var.project_id
  name    = "${local.redirect_http_prefix}-dashboard-proxy"
  url_map = google_compute_url_map.http_redirect.self_link
}

resource "google_compute_target_http_proxy" "http_redirect_design" {
  project = var.project_id
  name    = "${local.redirect_http_prefix}-design-proxy"
  url_map = google_compute_url_map.http_redirect.self_link
}

resource "google_compute_target_http_proxy" "http_redirect_boards" {
  project = var.project_id
  name    = "${local.redirect_http_prefix}-boards-proxy"
  url_map = google_compute_url_map.http_redirect.self_link
}

# ******************************
# 4. URL Map for HTTP Redirect - no backend
# ******************************
# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_url_map#example-usage---url-map-basic
resource "google_compute_url_map" "http_redirect" {
  project = var.project_id
  name    = "${local.redirect_http_prefix}-url-map"

  default_url_redirect {
    redirect_response_code = "MOVED_PERMANENTLY_DEFAULT" // 301 redirect
    strip_query            = false
    https_redirect         = true
  }

  /* Unclear how to get this setup with no backend but can do it manually through the console
     edit of through console with notes taken. */
  host_rule {
    hosts        = ["${var.dashboard_bucket_name}"]
    path_matcher = "path-matcher-1"
  }

  path_matcher {
    name = "path-matcher-1"
    default_url_redirect {
      https_redirect         = true
      redirect_response_code = "MOVED_PERMANENTLY_DEFAULT"
      strip_query            = false
    }
  }

}

