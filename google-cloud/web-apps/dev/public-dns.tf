/****************************************************************
Create public DNS record for api gateway
*****************************************************************/

data "cloudflare_zones" "default" {
  filter {
    name = "apella.io"
  }
}

resource "cloudflare_record" "internal" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "internal.${var.environment}"
  value   = google_compute_global_address.https_internal.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "dashboard" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "dashboard.${var.environment}"
  value   = google_compute_global_address.https_dashboard.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "dashboard_preview" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "*.dashboard.${var.environment}"
  value   = google_compute_global_address.https_dashboard.address
  type    = "A"
  ttl     = 300
}

resource "cloudflare_record" "boards" {
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = "boards.${var.environment}"
  value   = google_compute_global_address.https_boards.address
  type    = "A"
  ttl     = 300
}
