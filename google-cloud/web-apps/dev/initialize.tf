/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-web-apps-dev"
    }
  }
  required_version = ">= 1.7.5"
}

provider "google" {
  project = var.project_id
  region  = var.region
}


provider "google-beta" {
  project = var.project_id
  region  = var.region
}


data "terraform_remote_state" "nonprod_vpc" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-network-nonprod"
    }
  }
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "web_api" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-web-api-dev"
    }
  }
}

locals {
  api_domain_name = data.terraform_remote_state.web_api.outputs.api_server
}

/******************************************
  Cloudflare Authentication
 *****************************************/

terraform {
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 2.0"
    }
  }
}

provider "cloudflare" {
  api_token  = var.cloudflare_api_token
  account_id = var.cloudflare_account_id
}
