
# Outputs for Level 1 - creation of environment folders

output "root_folder_ids" {
  description = "Folder ids."
  value       = module.lvl1_folders.ids
}

output "root_folder_names" {
  description = "Folder names."
  value       = module.lvl1_folders.names
}

output "root_folder_ids_list" {
  description = "List of folder ids."
  value       = module.lvl1_folders.ids_list
}

output "root_folder_names_list" {
  description = "List of folder names."
  value       = module.lvl1_folders.names_list
}

# Outputs for Level 2 - creation of team folders

/*
# Dev
output "dev_folder_ids" {
  description = "Folder ids."
  value       = module.lvl2_folders_dev.ids
}

output "dev_folder_names" {
  description = "Folder names."
  value       = module.lvl2_folders_dev.names
}

output "dev_folder_ids_list" {
  description = "List of folder ids."
  value       = module.lvl2_folders_dev.ids_list
}

output "dev_folder_names_list" {
  description = "List of folder names."
  value       = module.lvl2_folders_dev.names_list
}

# Stage
output "stage_folder_ids" {
  description = "Folder ids."
  value       = module.lvl2_folders_stage.ids
}

output "stage_folder_names" {
  description = "Folder names."
  value       = module.lvl2_folders_stage.names
}

output "stage_folder_ids_list" {
  description = "List of folder ids."
  value       = module.lvl2_folders_stage.ids_list
}

output "stage_folder_names_list" {
  description = "List of folder names."
  value       = module.lvl2_folders_stage.names_list
}

# Prod
output "prod_folder_ids" {
  description = "Folder ids."
  value       = module.lvl2_folders_prod.ids
}

output "prod_folder_names" {
  description = "Folder names."
  value       = module.lvl2_folders_prod.names
}

output "prod_folder_ids_list" {
  description = "List of folder ids."
  value       = module.lvl2_folders_prod.ids_list
}

output "prod_folder_names_list" {
  description = "List of folder names."
  value       = module.lvl2_folders_prod.names_list
}

# Shared
output "shared_folder_ids" {
  description = "Folder ids."
  value       = module.lvl2_folders_shared.ids
}

output "shared_folder_names" {
  description = "Folder names."
  value       = module.lvl2_folders_shared.names
}

output "shared_folder_ids_list" {
  description = "List of folder ids."
  value       = module.lvl2_folders_shared.ids_list
}

output "shared_folder_names_list" {
  description = "List of folder names."
  value       = module.lvl2_folders_shared.names_list
}
*/