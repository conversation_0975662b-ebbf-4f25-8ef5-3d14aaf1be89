# Folder Module Call

# First level environments under organization
module "lvl1_folders" {
  source  = "app.terraform.io/apella/folders/google"
  version = "1.0.1"

  parent            = "${var.lvl1_parent_type}/${var.lvl1_parent_id}"
  names             = var.lvl1_folder_names
  set_roles         = true
  per_folder_admins = var.per_folder_admins
  all_folder_admins = var.all_folder_admins
}

# Second Level - team folder - lets add them individually in case they need to change by parent in the future 
#  - adding same folder under all Level 1 folder
# Common output
/* ids = {
  "dev" = "folders/xxxx"
  "prod" = "folders/xxx"
  "shared" = "folders/xxxx"
  "stage" = "folders/xxxx"
} */

/*
// dev
module "lvl2_folders_dev" {
  source  = "app.terraform.io/apella/folders/google"
  version = "1.0.0"

  parent            = module.lvl1_folders.ids["dev"]
  names             = var.lvl2_folder_names
  set_roles         = true
  per_folder_admins = var.per_folder_admins
  all_folder_admins = var.all_folder_admins
}

// stage
module "lvl2_folders_stage" {
  source  = "app.terraform.io/apella/folders/google"
  version = "1.0.0"

  parent            = module.lvl1_folders.ids["stage"]
  names             = var.lvl2_folder_names
  set_roles         = true
  per_folder_admins = var.per_folder_admins
  all_folder_admins = var.all_folder_admins
}

// prod
module "lvl2_folders_prod" {
  source  = "app.terraform.io/apella/folders/google"
  version = "1.0.0"

  parent            = module.lvl1_folders.ids["prod"]
  names             = var.lvl2_folder_names
  set_roles         = true
  per_folder_admins = var.per_folder_admins
  all_folder_admins = var.all_folder_admins
}

// shared
module "lvl2_folders_shared" {
  source  = "app.terraform.io/apella/folders/google"
  version = "1.0.0"

  parent            = module.lvl1_folders.ids["shared"]
  names             = var.lvl2_folder_names
  set_roles         = true
  per_folder_admins = var.per_folder_admins
  all_folder_admins = var.all_folder_admins
}
*/