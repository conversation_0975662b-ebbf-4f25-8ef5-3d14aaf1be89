/******************************************
	Core Terraform and Project Related Variables
 *****************************************/

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}


variable "project_id" {
  description = "GCP Project ID"
  default     = ""
}

# Environment Variables used for naming and labeling
variable "environment" {
  description = "The environment for where the this VPC will be created. Used for naming and labeling where applicable."
}

/******************************************
	Level 1 Folder Related Variables
 *****************************************/

variable "lvl1_parent_id" {
  type        = string
  description = "Id of the resource under which the folder will be placed."
}

variable "lvl1_parent_type" {
  type        = string
  description = "Type of the parent resource. One of `organizations` or `folders`."
}

variable "lvl1_folder_names" {
  type        = list(string)
  description = "Folder names."
}

/******************************************
	Level 2:  Future Folder Related Variables
 *****************************************/
/*

variable "lvl2_parent_type" {
  type        = string
  description = "Type of the parent resource. One of `organizations` or `folders`."
}

variable "lvl2_folder_names" {
  type        = list(string)
  description = "Folder names."
}
*/

/******************************************
	Common Folder Related Variables - may need to separate out later
 *****************************************/
/*  NOTE: Each member type is  identified with a prefix, such as a Google Account (user:), service account (serviceAccount:), Google group (group:), 
or a Google Workspace or Cloud Identity domain (domain:). In the following example code snippet, the storage.objectAdmin role is granted to the 
following members by using the appropriate prefix: user:<EMAIL>, serviceAccount:<EMAIL>, group:<EMAIL>, 
and domain:google.com.   */

variable "per_folder_admins" {
  type        = map(string)
  description = "IAM-style members per folder who will get extended permissions."
  default     = {}
}

variable "all_folder_admins" {
  type        = list(string)
  description = "List of IAM-style members that will get the extended permissions across all the folders."
  default     = []
}
