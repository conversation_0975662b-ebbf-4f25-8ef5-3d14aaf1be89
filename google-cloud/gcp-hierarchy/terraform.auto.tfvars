/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region      = "us-central1"
project_id  = "terraform-reference-app"
environment = "prod"


/******************************************
	Level 1:  Folder Related Variables
 *****************************************/

# Id of the resource under which the folder will be placed  
lvl1_parent_id = "212613986540"

# Type of the parent resource. One of `organizations` or `folders`.
lvl1_parent_type = "organizations"

# Folder Names as list(string)
lvl1_folder_names = ["dev", "staging", "prod", "shared", "sbx"]


/******************************************
	Level 2:  Future Folder Related Variables
 *****************************************/
# Id of the resource under which the folder will be placed  

# Type of the parent resource. One of `organizations` or `folders`.
#lvl2_parent_type = "folders"

# Folder Names as list(string)
#lvl2_folder_names     = ["engineering"]

/******************************************
	Common Folder Related Variables - may need to separate out later
 *****************************************/
/*  NOTE: Each member type is  identified with a prefix, such as a Google Account (user:), service account (serviceAccount:), Google group (group:), 
or a Google Workspace or Cloud Identity domain (domain:). In the following example code snippet, the storage.objectAdmin role is granted to the 
following members by using the appropriate prefix: user:<EMAIL>, serviceAccount:<EMAIL>, group:<EMAIL>, 
and domain:google.com.   */

# IAM-style members per folder who will get extended permissions. type = map(string)
per_folder_admins = {}

# all_folder_admins - type = list(string) List of IAM-style members that will get the extended permissions across all the folders."
all_folder_admins = ["user:<EMAIL>", "user:<EMAIL>"]