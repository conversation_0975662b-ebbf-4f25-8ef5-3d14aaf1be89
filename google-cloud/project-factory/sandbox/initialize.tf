/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-project-factory-sandbox"
    }
  }
  required_version = ">= 1.7.5"
}

provider "google" {
  region = var.region
}

locals {
  org_id           = data.google_organization.org.org_id
  vanta_scanner_sa = data.terraform_remote_state.global_security.outputs.vanta-scanner-sa
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_active_folder" "sbx" {
  display_name = "sbx"
  parent       = "organizations/${local.org_id}"
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}
