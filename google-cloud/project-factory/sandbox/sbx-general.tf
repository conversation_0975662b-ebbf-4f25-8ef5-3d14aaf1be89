module "sbx_general_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "general"
  env_name           = "sbx"
  project_id         = "sbx-general-1d26ed"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.sbx.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "apigateway.googleapis.com",
    "appengine.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudkms.googleapis.com",
    "compute.googleapis.com",
    "dns.googleapis.com",
    "firestore.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "iap.googleapis.com",
    "run.googleapis.com",
    "pubsub.googleapis.com",
    "secretmanager.googleapis.com",
    "servicenetworking.googleapis.com",
    "servicecontrol.googleapis.com",
    "servicemanagement.googleapis.com",
    "sqladmin.googleapis.com",
    "sql-component.googleapis.com",
    "vpcaccess.googleapis.com"
  ]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "sandbox",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "cameron",
    vanta-non-prod           = "true"
    vanta-description        = "general-sandbox"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "false"
  }
}

module "sbx_general_vpcaccess_dev" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.sbx_general_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.sbx_general_project.tf_sa_email}"
    },
    maint_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    },
    # required only for the GKE bastion host
    terraform_to_internal_gke = {
      subnet = "nonprod-central1-gke"
      region = "us-central1"
      user   = "serviceAccount:${module.sbx_general_project.tf_sa_email}"
    }
  }
}

resource "google_project_iam_member" "sbx_general_oslogin" {
  project = module.sbx_general_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}
