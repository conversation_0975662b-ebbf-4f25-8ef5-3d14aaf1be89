#creates the project
module "prod_web_apps_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "web-apps"
  env_name           = "prod"
  project_id         = "prod-web-apps-82f3c3"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "compute.googleapis.com",
    "dns.googleapis.com",
    "servicenetworking.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "iap.googleapis.com",
    "secretmanager.googleapis.com",
    "firebase.googleapis.com"
  ]
  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

module "prod_web_apps_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = module.prod_network_project.project_id
  service_project_id    = module.prod_web_apps_project.project_id

  user_subnet_map = {
    terraform_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_web_apps_project.tf_sa_email}"
    },
    maint_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    }
  }
}

/**********************************************************************
 Grant github actions service account permissions to build and deploy
 **********************************************************************/
locals {
  roleGitHubActionWebApps = [
    "roles/storage.objectAdmin"
  ]
}

#this service account is used by Github action runner that runs in our VPC
resource "google_project_iam_member" "prod_githubrunnersa_devwebapps_access" {
  for_each = toset(local.roleGitHubActionWebApps)
  project  = module.prod_web_apps_project.project_id
  role     = each.value
  member   = "serviceAccount:${local.prod_gh_runner_sa}"
}

resource "google_project_iam_member" "prod_web_apps_oslogin" {
  project = module.prod_web_apps_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "prod_web_apps_enable_oslogin" {
  project = module.prod_web_apps_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}

/******************************************
  Add firebase project
******************************************/

resource "google_firebase_project" "prod_web_apps_firebase" {
  provider = google-beta
  project  = module.prod_web_apps_project.project_id
}
