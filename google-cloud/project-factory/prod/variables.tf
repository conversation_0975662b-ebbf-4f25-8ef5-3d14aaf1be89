variable "billing_account_id" {
  description = "The default GCP billing account for apella."
}

variable "region" {
  description = "GCP region for resources"
  default     = "us-central1"
}

variable "domain_name" {
  description = "Name of the GCP domain"
  type        = string
  default     = "apella.io"
}

variable "environment" {
  description = "The environment name. Used for naming and labeling where applicable."
}

variable "shared_folder_id" {
  description = "folder id for shared folder."
}

variable "gcp_admin_email" {
  description = "Email of GCP Admin Group"
  type        = string
}

variable "prod_network_project_id" {
  description = "GCP Project ID for prod network"
}

variable "prod_security_project_id" {
  description = "GCP Project ID for prod security"
}

variable "dev_group" {
  description = "email for develoers group"
  type        = string
}
