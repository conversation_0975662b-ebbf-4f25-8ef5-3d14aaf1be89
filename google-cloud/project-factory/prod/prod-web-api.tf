#creates the project
module "prod_web_api_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "web-api"
  env_name           = "prod"
  project_id         = "prod-web-api-7f60bf"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "apigateway.googleapis.com",
    "appengine.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudscheduler.googleapis.com",
    "compute.googleapis.com",
    "dns.googleapis.com",
    "firestore.googleapis.com",
    "iap.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "pubsub.googleapis.com",
    "redis.googleapis.com",
    "run.googleapis.com",
    "secretmanager.googleapis.com",
    "servicecontrol.googleapis.com",
    "servicemanagement.googleapis.com",
    "servicenetworking.googleapis.com",
    "sql-component.googleapis.com",
    "sqladmin.googleapis.com",
    "vpcaccess.googleapis.com"
  ]



  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "nate",
    vanta-non-prod           = "false"
    vanta-description        = "prod-web-api"
    vanta-contains-user-data = "true"
    vanta-contains-ephi      = "true"
  }
}

module "prod_web_api_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = module.prod_network_project.project_id
  service_project_id    = module.prod_web_api_project.project_id

  user_subnet_map = {
    terraform_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_web_api_project.tf_sa_email}"
    },
    maint_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    }
  }
}

resource "google_project_iam_member" "prod_web_api_oslogin" {
  project = module.prod_web_api_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "prod_web_api_enable_oslogin" {
  project = module.prod_web_api_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}
