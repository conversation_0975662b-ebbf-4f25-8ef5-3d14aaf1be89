#creates the project
module "prod_internal_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "internal"
  env_name           = "prod"
  project_id         = "prod-internal-c5ac6b"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "artifactregistry.googleapis.com",
    "compute.googleapis.com",
    "container.googleapis.com",
    "drive.googleapis.com",
    "iap.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "secretmanager.googleapis.com",
    "servicenetworking.googleapis.com",
    "sheets.googleapis.com",
    "sql-component.googleapis.com",
    "sqladmin.googleapis.com",
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

module "prod_internal_vpcaccess_prod" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.prod_network_project_id
  service_project_id    = module.prod_internal_project.project_id

  user_subnet_map = {
    terraform_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_internal_project.tf_sa_email}"
    },
    maint_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    },
    # required only for the GKE bastion host
    terraform_to_internal_gke = {
      subnet = "prod-central1-internal-gke"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_internal_project.tf_sa_email}"
    }
  }
}

resource "google_project_iam_member" "prod_internal_oslogin" {
  project = module.prod_internal_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

# this allows prod-internal to perform GKE network operations
# https://cloud.google.com/kubernetes-engine/docs/how-to/cluster-shared-vpc#grant_host_service_agent_role
resource "google_project_iam_member" "prod_internal_service_agent_api" {
  project = var.prod_network_project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:service-${module.prod_internal_project.project_number}@container-engine-robot.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "prod_internal_service_agent_api2" {
  project = var.prod_network_project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:${module.prod_internal_project.project_number}@cloudservices.gserviceaccount.com"
}

# Grant access to use the subnetworks
resource "google_project_iam_member" "prod_internal_compute_network_user1" {
  project = var.prod_network_project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:service-${module.prod_internal_project.project_number}@container-engine-robot.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "prod_internal_compute_network_user2" {
  project = var.prod_network_project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${module.prod_internal_project.project_number}@cloudservices.gserviceaccount.com"
}
