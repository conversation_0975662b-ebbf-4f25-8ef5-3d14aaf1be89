#creates the project
module "prod_edge_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "edge"
  env_name           = "prod"
  project_id         = "prod-edge-a171bb"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "secretmanager.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "dorian",
    vanta-non-prod           = "false"
    vanta-description        = "prod-edge"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "false"
  }
}

resource "google_project_iam_member" "prod_edge_account_roles" {
  project = module.prod_edge_project.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:${module.prod_edge_project.tf_sa_email}"
}
