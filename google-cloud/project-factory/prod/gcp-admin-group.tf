/************************************************************
  define roles to be assigned to  GCP Admin
************************************************************/

locals {
  gcp_admin_group_org_roles = [
    "roles/securitycenter.admin",
    "roles/threatdetection.editor",
    "roles/orgpolicy.policyAdmin",
    "roles/iam.securityAdmin",
    "roles/compute.orgSecurityPolicyAdmin",
    "roles/compute.osAdminLogin",
  ]
}

resource "google_organization_iam_member" "gcp_admim_org_role_membership" {
  for_each = toset(local.gcp_admin_group_org_roles)
  org_id   = local.org_id
  role     = each.value
  member   = "group:${var.gcp_admin_email}"
}
