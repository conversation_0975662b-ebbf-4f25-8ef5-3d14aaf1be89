#used to store project outputs

/******************************************
  Projects
******************************************/

output "prod-data-platform-project" {
  value = module.prod_data_platform_project
}

output "prod-log-project" {
  value = module.prod_log_project
}

output "prod-media-asset-project" {
  value = module.prod_media_asset_project
}

output "prod-ml-project" {
  value = module.prod_machine_learning_project
}

output "prod-network-project" {
  value = module.prod_network_project
}

output "prod-platform-project" {
  value = module.prod_platform_project
}

output "prod-security-project" {
  value = module.prod_security_project
}

output "prod-web-api-project" {
  value = module.prod_web_api_project
}

output "prod-ehr-project" {
  value = module.prod_ehr_project
}

output "prod-edge-project" {
  value = module.prod_edge_project
}

output "prod-web-apps-project" {
  value = module.prod_web_apps_project
}

output "prod-internal-project" {
  value = module.prod_internal_project
}

/******************************************
  Service Accounts
******************************************/


output "prod_media_asset_project_id" {
  value = module.prod_media_asset_project.project_id
}

output "prod_web_api_project_id" {
  value = module.prod_web_api_project.project_id
}

output "prod_web_api_project_nbr" {
  value = module.prod_web_api_project.project_number
}
