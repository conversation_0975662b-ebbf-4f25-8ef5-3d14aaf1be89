#creates the project
module "prod_gcp_billing_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "gcp-billing"
  env_name           = var.environment
  project_id         = "prod-gcp-billing-a1e31e"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.shared.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "monitoring.googleapis.com",
    "billingbudgets.googleapis.com",
    "pubsub.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudbuild.googleapis.com",
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}
