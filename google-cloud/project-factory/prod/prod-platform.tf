#creates the project
module "prod_platform_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "platform"
  env_name           = var.environment
  project_id         = "prod-platform-29b5cb"
  group_name         = "GCP-ADMIN"
  folder_id          = var.shared_folder_id
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "artifactregistry.googleapis.com",
    "pubsub.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudscheduler.googleapis.com",
    "secretmanager.googleapis.com",
    "cloudbuild.googleapis.com",
    # Required by Vanta
    "containeranalysis.googleapis.com",
    "containerscanning.googleapis.com",
    "monitoring.googleapis.com"
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/******************************************
  Add group owner to roles/billing.user
    to receive billing alerts
******************************************/

resource "google_billing_account_iam_member" "prod_platform_billing" {
  billing_account_id = var.billing_account_id
  role               = "roles/billing.user"
  member             = "group:${module.prod_platform_project.group_email}"
}

resource "google_project_iam_member" "prod_platform_secretAccessor" {
  project = module.prod_platform_project.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${module.prod_platform_project.tf_sa_email}"
}

/**********************************************************************
 Grant github actions service account permissions to build and deploy
 **********************************************************************/
locals {
  roleGitHubActionProdWebApi = [
    "roles/cloudbuild.builds.builder",
    "roles/storage.admin",
    "roles/viewer"
  ]
}

resource "google_project_iam_member" "prod_githubaction_access" {
  for_each = toset(local.roleGitHubActionProdWebApi)
  project  = module.prod_platform_project.project_id
  role     = each.value
  member   = "serviceAccount:${local.prod_gh_runner_sa}"
}

/******************************************
  Add Vanta Securty Scanner
******************************************/

resource "google_project_iam_member" "prod_platform_vanta_project_scanner" {
  project = module.prod_platform_project.project_id
  role    = "organizations/${local.org_id}/roles/VantaProjectScanner"
  member  = "serviceAccount:${local.vanta_scanner_sa}"
}

resource "google_project_iam_member" "prod_platform_vanta_security_reviewer" {
  project = module.prod_platform_project.project_id
  role    = "roles/iam.securityReviewer"
  member  = "serviceAccount:${local.vanta_scanner_sa}"
}


/******************************************
  Grant access to shared vpc
******************************************/
module "prod_platform_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = module.prod_network_project.project_id
  service_project_id    = module.prod_platform_project.project_id

  user_subnet_map = {
    terraform_to_prod_central = {
      subnet = "prod-central1-shared-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_platform_project.tf_sa_email}"
    },
    maint_to_prod_central = {
      subnet = "prod-central1-shared-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    },
    terraform_to_nonprod_central_runner_sa = {
      subnet = "prod-central1-shared-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_platform_project.project_number}@cloudservices.gserviceaccount.com"
    },
  }
}


locals {
  roleListProdGithubRunner = [
    "roles/cloudsql.client",
    "roles/cloudsql.instanceUser",
    "roles/datastore.user",
    "roles/logging.logWriter",
    "roles/run.serviceAgent",
    "roles/secretmanager.secretAccessor",
    "roles/storage.objectViewer"
  ]
}

resource "google_project_iam_member" "prod_platform_oslogin" {
  project = module.prod_platform_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "prod_platform_enable_oslogin" {
  project = module.prod_platform_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}
