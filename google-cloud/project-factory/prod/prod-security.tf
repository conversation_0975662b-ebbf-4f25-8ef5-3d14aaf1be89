#creates the project
module "prod_security_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "security"
  env_name           = var.environment
  project_id         = "prod-security-ab8e3e"
  group_name         = "GCP-ADMIN"
  folder_id          = var.shared_folder_id
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "cloudbuild.googleapis.com",
    "securitycenter.googleapis.com",
    "pubsub.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "stackdriver.googleapis.com",
    "containeranalysis.googleapis.com",
    "containerscanning.googleapis.com",
    "secretmanager.googleapis.com",
    "dataflow.googleapis.com",
    "compute.googleapis.com",
    # The following additional apis are required by the Vanta scanner
    "iam.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "serviceusage.googleapis.com",
    "bigquery.googleapis.com",
    "sqladmin.googleapis.com",
    "firestore.googleapis.com",
    "storage-api.googleapis.com",
    "cloudasset.googleapis.com"
  ]


  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/******************************************
  Add group owner to roles/billing.user
    to receive billing alerts
******************************************/

resource "google_billing_account_iam_member" "prod_security_billing" {
  billing_account_id = var.billing_account_id
  role               = "roles/billing.user"
  member             = "group:${module.prod_security_project.group_email}"
}

locals {
  terraform_sa_org_roles = [
    "roles/securitycenter.admin",
    "roles/threatdetection.editor",
    "roles/orgpolicy.policyAdmin",
    "roles/iam.securityAdmin",
    "roles/compute.orgSecurityPolicyAdmin",
    "roles/accesscontextmanager.policyAdmin",
    "roles/logging.configWriter",
  ]
}

resource "google_organization_iam_member" "prod_security_org_role_membership" {
  for_each = toset(local.terraform_sa_org_roles)
  org_id   = local.org_id
  role     = each.value
  member   = "serviceAccount:${module.prod_security_project.tf_sa_email}"
}
