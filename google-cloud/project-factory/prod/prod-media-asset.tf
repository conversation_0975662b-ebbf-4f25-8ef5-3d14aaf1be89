locals {
  roles = [
    "roles/iam.serviceAccountUser"
  ]
}

#creates the project
module "prod_media_asset_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "media-asset"
  env_name           = "prod"
  project_id         = "prod-media-asset-5fd208"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "secretmanager.googleapis.com",
    "container.googleapis.com",
    "logging.googleapis.com",
    "bigtable.googleapis.com",
    "bigtableadmin.googleapis.com",
    "apigateway.googleapis.com"
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "abhay",
    vanta-non-prod           = "false"
    vanta-description        = "prod-media-asset"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "true"
  }
}

resource "google_project_iam_member" "prod_media_asset_account_roles" {
  project = module.prod_media_asset_project.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:${module.prod_media_asset_project.tf_sa_email}"
}

# prod media_asset_service
resource "google_project_iam_member" "prod_host_media_asset_agent_gke_sa" {
  project = var.prod_network_project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:service-${module.prod_media_asset_project.project_number}@container-engine-robot.iam.gserviceaccount.com"
}
