/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-project-factory-prod"
    }
  }
  required_version = ">= 1.7.5"
}

provider "google" {
  #project = var.project_id
  region = var.region
}

locals {
  org_id            = data.google_organization.org.org_id
  prod_gh_runner_sa = data.terraform_remote_state.prod_security.outputs.prod-github-runner-sa
  vanta_scanner_sa  = data.terraform_remote_state.global_security.outputs.vanta-scanner-sa
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_active_folder" "prod" {
  display_name = "prod"
  parent       = "organizations/${local.org_id}"
}

data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${local.org_id}"
}

data "terraform_remote_state" "prod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}

/*
data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${local.org_id}"
}
*/
