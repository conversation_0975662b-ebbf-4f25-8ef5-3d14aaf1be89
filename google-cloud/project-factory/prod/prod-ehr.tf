module "prod_ehr_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "ehr"
  env_name           = "prod"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.prod.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "apigateway.googleapis.com",
    "container.googleapis.com",
    "logging.googleapis.com",
    "pubsub.googleapis.com",
    "secretmanager.googleapis.com",

    # For creating postgres instances
    "servicenetworking.googleapis.com",
    "sqladmin.googleapis.com",
  ]

  project_labels = {
    data_classification = "confidential",
    environment         = "prod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",

    # Vanta inventory
    vanta-owner              = "ehr-team",
    vanta-non-prod           = "false"
    vanta-description        = "prod-ehr"
    vanta-contains-user-data = "true"
    vanta-contains-ephi      = "true"
  }
}

module "prod_ehr_vpcaccess_prod" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.prod_network_project_id
  service_project_id    = module.prod_ehr_project.project_id

  user_subnet_map = {
    #To support provisioning a postgres instances in shared network from ehr project
    terraform_to_prod_central = {
      subnet = "prod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.prod_ehr_project.tf_sa_email}"
    }
  }
}
