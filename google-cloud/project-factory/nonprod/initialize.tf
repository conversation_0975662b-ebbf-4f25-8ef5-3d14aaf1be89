/******************************************
  Remote backend configuration
 *****************************************/
terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "gcp-project-factory-nonprod"
    }
  }
  required_version = ">= 1.7.5"
}

provider "google" {
  #project     = var.project_id
  region = var.region
}

locals {
  org_id               = data.google_organization.org.org_id
  nonprod_gh_runner_sa = data.terraform_remote_state.nonprod_security.outputs.nonprod-github-runner-sa
  vanta_scanner_sa     = data.terraform_remote_state.global_security.outputs.vanta-scanner-sa
}

data "google_organization" "org" {
  domain = var.domain_name
}

data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${local.org_id}"
}

data "google_active_folder" "dev" {
  display_name = "dev"
  parent       = "organizations/${local.org_id}"
}

data "google_active_folder" "staging" {
  display_name = "staging"
  parent       = "organizations/${local.org_id}"
}

data "terraform_remote_state" "nonprod_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "global_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-global"
    }
  }
}

/*
data "google_active_folder" "shared" {
  display_name = "shared"
  parent       = "organizations/${local.org_id}"
}
*/
