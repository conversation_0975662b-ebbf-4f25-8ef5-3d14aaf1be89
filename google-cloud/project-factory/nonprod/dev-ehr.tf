module "dev_ehr_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "ehr"
  env_name           = "dev"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.dev.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "apigateway.googleapis.com",
    "container.googleapis.com",
    "logging.googleapis.com",
    "pubsub.googleapis.com",
    "secretmanager.googleapis.com",

    # For creating postgres instances
    "servicenetworking.googleapis.com",
    "sqladmin.googleapis.com",
  ]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "dev",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",

    # Vanta inventory
    vanta-owner              = "ehr-team",
    vanta-non-prod           = "true"
    vanta-description        = "dev-ehr"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "false"
  }
}

module "non_prod_ehr_vpcaccess_dev" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.dev_ehr_project.project_id

  user_subnet_map = {
    #To support provisioning a postgres instances in shared network from ehr project
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.dev_ehr_project.tf_sa_email}"
    }
  }
}
