#creates the project
module "dev_data_platform_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "data-platform"
  env_name           = "dev"
  project_id         = "dev-data-platform-439b4c" # Backwards compatibility for existing projects
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.dev.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "appengine.googleapis.com",
    "artifactregistry.googleapis.com",
    "bigquery.googleapis.com",
    "bigtableadmin.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudscheduler.googleapis.com",
    "compute.googleapis.com",
    "datacatalog.googleapis.com",
    "dataflow.googleapis.com",
    "datastream.googleapis.com",
    "iap.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "pubsub.googleapis.com",
    "redis.googleapis.com",
    "secretmanager.googleapis.com",
    "servicenetworking.googleapis.com",
  ]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

module "dev_data_platform_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.dev_data_platform_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.dev_data_platform_project.tf_sa_email}"
    }
  }
}

resource "google_project_iam_member" "dev_data_platform_oslogin" {
  project = module.dev_data_platform_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "dev_data_platform_enable_oslogin" {
  project = module.dev_data_platform_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}

resource "google_project_iam_member" "dev_data_platform_cf_service_agent_vpcaccess" {
  project = module.dev_data_platform_project.project_id
  member  = "serviceAccount:service-${module.dev_data_platform_project.project_number}@gcf-admin-robot.iam.gserviceaccount.com"
  role    = "roles/vpcaccess.user"
}

resource "google_project_iam_member" "dev_data_platform_cf_service_agent_vpcaccess_root" {
  project = module.nonprod_network_project.project_id
  member  = "serviceAccount:service-${module.dev_data_platform_project.project_number}@gcf-admin-robot.iam.gserviceaccount.com"
  role    = "roles/vpcaccess.user"
}
