locals {
  cloud_ids_roles = [
    "roles/ids.admin",
    "roles/compute.packetMirroringUser"
  ]
}

#creates the project
module "nonprod_network_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "network"
  env_name           = var.environment
  project_id         = "nonprod-network-9523f0"
  group_name         = "GCP-ADMIN"
  folder_id          = var.shared_folder_id
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = ["container.googleapis.com", "dns.googleapis.com", "servicenetworking.googleapis.com", "vpcaccess.googleapis.com", "secretmanager.googleapis.com", "serviceusage.googleapis.com", "compute.googleapis.com", "ids.googleapis.com"]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/******************************************
  Add group owner to roles/billing.user
    to receive billing alerts
******************************************/

resource "google_billing_account_iam_member" "nonprod_network_billing" {
  billing_account_id = var.billing_account_id
  role               = "roles/billing.user"
  member             = "group:${module.nonprod_network_project.group_email}"
}

resource "google_organization_iam_member" "nonprod_network_xpnadmin" {
  org_id = local.org_id
  role   = "roles/compute.xpnAdmin"
  member = "serviceAccount:${module.nonprod_network_project.tf_sa_email}"
}

#this allows dev-web-api project to create DNS records
resource "google_project_iam_member" "nonprod_network_dnsAdmin_dev" {
  project = module.nonprod_network_project.project_id
  role    = "roles/dns.admin"
  member  = "serviceAccount:${module.dev_web_api_project.tf_sa_email}"
}

#this allows staging-web-api project to create DNS records
resource "google_project_iam_member" "nonprod_network_dnsAdmin_staging" {
  project = module.nonprod_network_project.project_id
  role    = "roles/dns.admin"
  member  = "serviceAccount:${module.staging_web_api_project.tf_sa_email}"
}

# this allows sbx-general to perform GKE network operations
# https://cloud.google.com/kubernetes-engine/docs/how-to/cluster-shared-vpc#grant_host_service_agent_role
resource "google_project_iam_member" "nonprod_network_gke_network_sbx_general" {
  project = module.nonprod_network_project.project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_member" "nonprod_network_sbx_general_network_user1" {
  project = module.nonprod_network_project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_member" "nonprod_network_sbx_general_network_user2" {
  project = module.nonprod_network_project.project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:<EMAIL>"
}

# This is a temporary measure until we update tf-iam to better add all relevant roles to Terraform
resource "google_project_iam_member" "terraform_sa_ids_roles" {
  for_each = toset(local.cloud_ids_roles)
  project  = module.nonprod_network_project.project_id
  role     = each.value
  member   = "serviceAccount:${module.nonprod_network_project.tf_sa_email}"
}
