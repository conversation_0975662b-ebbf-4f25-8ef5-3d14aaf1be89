#creates the project
module "staging_web_api_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "web-api"
  env_name           = "staging"
  project_id         = "staging-web-api-3efef9"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.staging.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "apigateway.googleapis.com",
    "appengine.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudfunctions.googleapis.com",
    "cloudscheduler.googleapis.com",
    "compute.googleapis.com",
    "dns.googleapis.com",
    "firestore.googleapis.com",
    "iap.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "pubsub.googleapis.com",
    "redis.googleapis.com",
    "run.googleapis.com",
    "secretmanager.googleapis.com",
    "servicecontrol.googleapis.com",
    "servicemanagement.googleapis.com",
    "servicenetworking.googleapis.com",
    "sql-component.googleapis.com",
    "sqladmin.googleapis.com",
    "vpcaccess.googleapis.com"
  ]



  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "nate",
    vanta-non-prod           = "true"
    vanta-description        = "staging-web-api"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "false"
  }
}

module "non_prod_web_api_vpcaccess_staging" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.staging_web_api_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.staging_web_api_project.tf_sa_email}"
    },
    maint_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    }
  }
}

resource "google_project_iam_member" "staging_web_api_oslogin" {
  project = module.staging_web_api_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "staging_web_api_enable_oslogin" {
  project = module.staging_web_api_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}
