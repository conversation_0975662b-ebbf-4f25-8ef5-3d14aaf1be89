#creates the project
module "nonprod_web_apps_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.0.2"

  app_name           = "customer-apps"
  env_name           = "dev"
  group_name         = "GCP-ADMIN"
  group_role         = "organizations/${local.org_id}/roles/projectMaintainer_1"
  folder_id          = data.google_active_folder.dev.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = ["apigateway.googleapis.com",
    "artifactregistry.googleapis.com",
    "compute.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "iap.googleapis.com",
    "run.googleapis.com"
    ""]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/******************************************
  Add group owner to projectMaintainer_2
******************************************/

resource "google_project_iam_member" "nonprod_web_apps_projectMaintainer_2" {
  project = module.nonprod_web_apps_project.project_id
  role    = "organizations/${local.org_id}/roles/projectMaintainer_2"
  member  = "group:${module.nonprod_web_apps_project.group_email}"
}

module "non_prod_web_apps_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.0"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.nonprod_web_apps_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.nonprod_web_apps_project.tf_sa_email}"
    },
    maint_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    }
  }
}
