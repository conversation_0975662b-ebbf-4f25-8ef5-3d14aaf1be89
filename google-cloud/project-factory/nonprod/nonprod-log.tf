#creates the project
module "nonprod_log_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "log"
  env_name           = var.environment
  project_id         = "nonprod-log-f302cc"
  group_name         = "GCP-ADMIN"
  group_role         = "roles/viewer"
  folder_id          = data.google_active_folder.shared.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = ["container.googleapis.com", "logging.googleapis.com", "serviceusage.googleapis.com", "monitoring.googleapis.com"]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/******************************************
  Grant the ability to write folder log sinks
 *****************************************/
resource "google_folder_iam_member" "log_nonprod_dev_config_writer" {
  folder = data.google_active_folder.dev.name
  role   = "roles/logging.configWriter"
  member = "serviceAccount:${module.nonprod_log_project.tf_sa_email}"
}

resource "google_folder_iam_member" "log_nonprod_staging_config_writer" {
  folder = data.google_active_folder.staging.name
  role   = "roles/logging.configWriter"
  member = "serviceAccount:${module.nonprod_log_project.tf_sa_email}"
}

/******************************************
  Grant ability to view folder ids
 *****************************************/
resource "google_organization_iam_member" "log_nonprod_folder_viewer" {
  org_id = local.org_id
  role   = "roles/resourcemanager.folderViewer"
  member = "serviceAccount:${module.nonprod_log_project.tf_sa_email}"
}

/******************************************
  Grant ability to view organization
 *****************************************/
resource "google_organization_iam_member" "log_prd_org_viewer" {
  org_id = local.org_id
  role   = "roles/resourcemanager.organizationViewer"
  member = "serviceAccount:${module.nonprod_log_project.tf_sa_email}"
}
