#creates the project
module "staging_web_apps_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "web-apps"
  env_name           = "staging"
  project_id         = "staging-web-apps-9c7779"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.staging.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "compute.googleapis.com",
    "dns.googleapis.com",
    "servicenetworking.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "iap.googleapis.com",
    "secretmanager.googleapis.com",
    "firebase.googleapis.com"
  ]
  project_labels = {
    data_classification = "non-confidential",
    environment         = "nonprod",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

/**********************************************************************
 Grant github actions service account permissions to build and deploy
 **********************************************************************/
locals {
  stagingRoleGitHubActionWebApps = [
    "roles/storage.objectAdmin"
  ]
}

#this service account is used by Github action runner that runs in our VPC
resource "google_project_iam_member" "staging_githubrunnersa_stagingwebapps_access" {
  for_each = toset(local.stagingRoleGitHubActionWebApps)
  project  = module.staging_web_apps_project.project_id
  role     = each.value
  member   = "serviceAccount:${local.nonprod_gh_runner_sa}"
}

resource "google_project_iam_member" "staging_web_apps_oslogin" {
  project = module.staging_web_apps_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

resource "google_compute_project_metadata_item" "staging_web_apps_enable_oslogin" {
  project = module.staging_web_apps_project.project_id
  key     = "enable-oslogin"
  value   = "TRUE"
}

/******************************************
  Add firebase project
******************************************/

resource "google_firebase_project" "staging_web_apps_firebase" {
  provider = google-beta
  project  = module.staging_web_apps_project.project_id
}
