#creates the project
module "dev_edge_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "edge"
  env_name           = "dev"
  project_id         = "dev-edge-e943cf"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.dev.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "secretmanager.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
  ]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "dev",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
    # Vanta inventory
    vanta-owner              = "dorian",
    vanta-non-prod           = "true"
    vanta-description        = "dev-edge"
    vanta-contains-user-data = "false"
    vanta-contains-ephi      = "false"
  }
}

resource "google_project_iam_member" "dev_edge_account_roles" {
  project = module.dev_edge_project.project_id
  role    = "roles/iam.serviceAccountUser"
  member  = "serviceAccount:${module.dev_edge_project.tf_sa_email}"
}
