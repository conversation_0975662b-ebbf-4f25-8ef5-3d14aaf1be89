
module "non_prod_web_apps_vpcaccess" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.dev_web_apps_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.dev_web_apps_project.tf_sa_email}"
    },
    terraform_to_staging_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.staging_web_apps_project.tf_sa_email}"
    },
    maint_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    }
  }
}
