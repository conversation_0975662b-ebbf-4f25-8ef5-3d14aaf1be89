# used to store project outputs


/******************************************
  Projects
******************************************/

output "dev-data-platform-project" {
  value = module.dev_data_platform_project
}

output "dev-media-asset-project" {
  value = module.dev_media_asset_project
}

output "dev-ml-project" {
  value = module.dev_machine_learning_project
}

output "dev-web-api-project" {
  value = module.dev_web_api_project
}

output "dev-ehr-project" {
  value = module.dev_ehr_project
}

output "dev-edge-project" {
  value = module.dev_edge_project
}

output "dev-web-apps-project" {
  value = module.dev_web_apps_project
}

output "nonprod-log-project" {
  value = module.nonprod_log_project
}

output "nonprod-network-project" {
  value = module.nonprod_network_project
}

output "nonprod-platform-project" {
  value = module.nonprod_platform_project
}

output "dev-internal-project" {
  value = module.dev_internal_project
}

output "staging-data-platform-project" {
  value = module.staging_data_platform_project
}

output "staging-media-asset-project" {
  value = module.staging_media_asset_project
}

output "staging-ml-project" {
  value = module.staging_machine_learning_project
}

output "staging-web-api-project" {
  value = module.staging_web_api_project
}

output "staging-ehr-project" {
  value = module.staging_ehr_project
}

output "staging-edge-project" {
  value = module.staging_edge_project
}

output "staging-web-apps-project" {
  value = module.staging_web_apps_project
}

output "staging-internal-project" {
  value = module.staging_internal_project
}

/******************************************
  Service Accounts
******************************************/

output "dev_media_asset_project_id" {
  value = module.dev_media_asset_project.project_id
}

output "staging_media_asset_project_id" {
  value = module.staging_media_asset_project.project_id
}
