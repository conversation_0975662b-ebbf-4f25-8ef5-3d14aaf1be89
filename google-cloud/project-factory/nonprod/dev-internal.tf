#creates the project
module "dev_internal_project" {
  source  = "app.terraform.io/apella/project-factory/google"
  version = "1.1.15"

  app_name           = "internal"
  env_name           = "dev"
  project_id         = "dev-internal-b2aa9f"
  group_name         = "GCP-ADMIN"
  folder_id          = data.google_active_folder.dev.name
  org_id             = local.org_id
  billing_account_id = var.billing_account_id

  activate_apis = [
    "artifactregistry.googleapis.com",
    "compute.googleapis.com",
    "container.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "iap.googleapis.com",
    "drive.googleapis.com",
    "secretmanager.googleapis.com",
    "servicenetworking.googleapis.com",
    "sheets.googleapis.com",
    "sql-component.googleapis.com",
    "sqladmin.googleapis.com",
  ]

  project_labels = {
    data_classification = "non-confidential",
    environment         = "dev",
    support_team        = "engineering",
    created_by          = "terraform-project-factory",
  }
}

module "non_prod_internal_vpcaccess_dev" {
  source                = "app.terraform.io/apella/sharedvpc-access/google"
  version               = "1.0.1"
  shared_vpc_project_id = var.nonprod_network_project_id
  service_project_id    = module.dev_internal_project.project_id

  user_subnet_map = {
    terraform_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "serviceAccount:${module.dev_internal_project.tf_sa_email}"
    },
    maint_to_nonprod_central = {
      subnet = "nonprod-central1-compute"
      region = "us-central1"
      user   = "group:<EMAIL>"
    },
    # required only for the GKE bastion host
    terraform_to_internal_gke = {
      subnet = "nonprod-central1-dev-internal-gke"
      region = "us-central1"
      user   = "serviceAccount:${module.dev_internal_project.tf_sa_email}"
    }
  }
}

resource "google_project_iam_member" "dev_internal_oslogin" {
  project = module.dev_internal_project.project_id
  role    = "roles/compute.osAdminLogin"
  member  = "group:${var.dev_group}"
}

# this allows dev-internal to perform GKE network operations
# https://cloud.google.com/kubernetes-engine/docs/how-to/cluster-shared-vpc#grant_host_service_agent_role
resource "google_project_iam_member" "dev_internal_service_agent_api" {
  project = var.nonprod_network_project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:service-${module.dev_internal_project.project_number}@container-engine-robot.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "dev_internal_service_agent_api2" {
  project = var.nonprod_network_project_id
  role    = "roles/container.hostServiceAgentUser"
  member  = "serviceAccount:${module.dev_internal_project.project_number}@cloudservices.gserviceaccount.com"
}

# Grant access to use the subnetworks
resource "google_project_iam_member" "dev_internal_compute_network_user1" {
  project = var.nonprod_network_project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:service-${module.dev_internal_project.project_number}@container-engine-robot.iam.gserviceaccount.com"
}

resource "google_project_iam_member" "dev_internal_compute_network_user2" {
  project = var.nonprod_network_project_id
  role    = "roles/compute.networkUser"
  member  = "serviceAccount:${module.dev_internal_project.project_number}@cloudservices.gserviceaccount.com"
}
