
/******************************************************
 * Data Platform & Machine Learning
 ******************************************************/

module "gcp-data-platform" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-data-platform"
  description        = "Data Platform"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/data-platform"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

module "gcp-machine-learning" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-machine-learning"
  description        = "Machine Learning"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/machine-learning"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

/******************************************************
 * EHR
 ******************************************************/
module "ehr" {
  for_each           = toset(["dev", "staging", "prod"])
  source             = "./modules/apella-workspace"
  name               = "ehr-${each.key}"
  description        = "EHR Infrastructure"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/ehr"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}


/******************************************************
 * Shared Web
 ******************************************************/

module "gcp-web-api" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-web-api"
  description        = "Web API Infrastructure"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/web-api"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

module "gcp-web-apps" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-web-apps"
  description        = "Web Applications"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/web-apps"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

/******************************************************
 * Shared Projects
 ******************************************************/

module "gcp-billing" {
  source             = "./modules/apella-workspace"
  name               = "gcp-billing"
  description        = "Google cloud billing"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/billing"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

module "gcp-hierarchy" {
  source             = "./modules/apella-workspace"
  name               = "gcp-hierarchy"
  description        = "GCP Project folder hierarchy"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/gcp-hierarchy"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

module "gcp-iam" {
  source             = "./modules/apella-workspace"
  name               = "gcp-iam"
  description        = "IAM Roles"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/iam"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

module "gcp-internal" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-internal"
  description        = "Project for internal services, including our primary GKE"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/internal"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

module "gcp-logging" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-logging"
  description        = "Logging project"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/logging"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["nonprod", "prod"]
}


module "gcp-network" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-network"
  description        = "Network project (nonprod)"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/network"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["nonprod", "prod"]
}

module "gcp-platform" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-platform"
  description        = "Platform project"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/platform"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["nonprod", "prod"]
}

module "gcp-project-factory" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-project-factory"
  description        = "Project Factory project"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/project-factory"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["nonprod", "prod", "sandbox"]
}

module "gcp-sandbox-general" {
  source             = "./modules/apella-workspace"
  name               = "gcp-sandbox-general"
  description        = "A place to play"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/sbx-general"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

module "gcp-security" {
  source             = "./modules/apella-env-workspace"
  name               = "gcp-security"
  description        = "Security settings"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/security"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["nonprod", "prod", "global"]
}

module "edge" {
  for_each           = toset(["dev", "staging", "prod"])
  source             = "./modules/apella-workspace"
  name               = "tf-edge-${each.key}"
  description        = "Edge resources"
  organization       = tfe_organization.apella.name
  working_directory  = "google-cloud/edge"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}
