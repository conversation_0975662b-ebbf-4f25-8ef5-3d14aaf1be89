locals {
  optional_modules_trigger = var.modules_directory != "" ? ["${var.modules_directory}/**/*"] : []
}
resource "tfe_workspace" "workspace" {
  name              = var.name
  description       = var.description
  working_directory = var.working_directory
  organization      = var.organization
  queue_all_runs    = var.queue_all_runs

  trigger_patterns = concat(
    ["${var.working_directory}/**/*"],
    local.optional_modules_trigger
  )

  vcs_repo {
    identifier     = "Apella-Technology/${var.vcs_repo_name}"
    oauth_token_id = var.vcs_oauth_token_id
  }
}
