variable "name" {
  type        = string
  description = "Repository name"
  validation {
    condition     = can(regex("^[a-zA-Z0-9_-]+$", var.name))
    error_message = "The name must only contain letters, numbers, dashes, and underscores."
  }
}

variable "description" {
  type        = string
  description = "Repository description"
}

variable "auto_apply" {
  type        = bool
  description = "Auto apply changes"
  default     = "false"
}

variable "organization" {
  type        = string
  description = "Terraform Cloud organization"
}

variable "terraform_version" {
  type        = string
  description = "Terraform version"
  default     = "~>1.7.0"
}

variable "working_directory" {
  type        = string
  description = "Working directory"
}

variable "modules_directory" {
  type        = string
  description = "Modules directory"
  default     = ""
}

variable "queue_all_runs" {
  type        = bool
  description = "Queue all workspace runs"
  default     = false
}

variable "vcs_repo_name" {
  type        = string
  description = "VCS repository name"
  default     = "infrastructure"
}

variable "vcs_oauth_token_id" {
  type        = string
  description = "VCS OAuth token ID"
}