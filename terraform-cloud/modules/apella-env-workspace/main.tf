locals {
  optional_modules_trigger = var.modules_directory != "" ? ["${var.working_directory}/${var.modules_directory}/**/*"] : []
}
resource "tfe_workspace" "workspace" {
  for_each          = toset(var.environments)
  name              = "${var.name}-${each.key}"
  description       = "${var.description} (${each.key})"
  working_directory = "${var.working_directory}/${each.key}"
  organization      = var.organization
  queue_all_runs    = var.queue_all_runs

  trigger_patterns = concat(
    ["${var.working_directory}/${each.key}/**/*"],
    local.optional_modules_trigger
  )

  tag_names = toset(concat(
    var.tag_names,
    ["env:${each.key}"]
  ))

  vcs_repo {
    identifier     = "Apella-Technology/${var.vcs_repo_name}"
    oauth_token_id = var.vcs_oauth_token_id
  }
}
