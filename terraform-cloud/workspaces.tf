
resource "tfe_organization" "apella" {
  name                          = "apella"
  email                         = "<EMAIL>"
  allow_force_delete_workspaces = true
  collaborator_auth_policy      = "two_factor_mandatory"
}


/******************************************************
 * Datadog
 ******************************************************/

module "datadog-management" {
  source             = "./modules/apella-workspace"
  name               = "datadog"
  description        = "Datadog alerts for the development environment"
  organization       = tfe_organization.apella.name
  working_directory  = "datadog"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

/******************************************************
 * Auth0
 ******************************************************/

module "auth0" {
  source             = "./modules/apella-env-workspace"
  name               = "auth0"
  description        = "Terraform for Apella Auth0 tenant"
  organization       = tfe_organization.apella.name
  working_directory  = "auth0"
  modules_directory  = "modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
  environments       = ["dev", "staging", "prod"]
}

/******************************************************
 * Github
 ******************************************************/

module "github" {
  source             = "./modules/apella-workspace"
  name               = "github"
  description        = "Managing Apella Github resources in one place"
  organization       = tfe_organization.apella.name
  working_directory  = "github"
  modules_directory  = "github/modules"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

/******************************************************
 * Google Workspace
 ******************************************************/

module "google-workspace" {
  source             = "./modules/apella-workspace"
  name               = "google-workspace"
  description        = "Google Workspace configuration for security groups"
  organization       = tfe_organization.apella.name
  working_directory  = "google-workspace"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}


/******************************************************
 * Ops Genie
 ******************************************************/

module "ops-genie" {
  source             = "./modules/apella-workspace"
  name               = "ops-genie"
  description        = "Ops Genie configuration"
  organization       = tfe_organization.apella.name
  working_directory  = "ops-genie"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}

/******************************************************
 * Tailscale
 ******************************************************/

module "tailscale" {
  source             = "./modules/apella-workspace"
  name               = "tailscale"
  description        = "Apella Tailscale access control"
  organization       = tfe_organization.apella.name
  working_directory  = "tailscale"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}


/******************************************************
 * Incident.io
 ******************************************************/

module "incident-io" {
  source             = "./modules/apella-workspace"
  name               = "incident-io"
  description        = "Incident.io configuration"
  organization       = tfe_organization.apella.name
  working_directory  = "incident-io"
  vcs_oauth_token_id = var.vcs_oauth_token_id
}
