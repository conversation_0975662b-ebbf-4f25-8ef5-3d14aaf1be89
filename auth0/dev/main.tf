terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "auth0-dev"
    }
  }
  required_version = ">= 1.7.5"
}

data "terraform_remote_state" "nonprod_tf_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "prod_tf_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

data "terraform_remote_state" "nonprod_tf_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "dev_tf_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-dev"
    }
  }
}

module "apella_auth0" {
  environment = "dev"
  source      = "../modules/apella_auth0"
  gcp_project_ids = {
    web_api       = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-web-api-project.project_id,
    data_platform = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-data-platform-project.project_id,
    media_asset   = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-media-asset-project.project_id,
    ml            = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-ml-project.project_id,
    ehr           = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-ehr-project.project_id,
    edge          = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-edge-project.project_id,
    security      = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-security-project.project_id,
    internal      = data.terraform_remote_state.nonprod_tf_project_factory.outputs.dev-internal-project.project_id,
  }
  gcp_sa = {
    dataflow = data.terraform_remote_state.nonprod_tf_security.outputs.nonprod-dataflow-sa
  }
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
  auth0_domain        = var.auth0_domain
  cloudflare_configuration = {
    api_token  = var.cloudflare_api_token
    account_id = var.cloudflare_account_id
  }
  saml_certificates = {
    apella_internal   = var.apella_internal_cert
    houston_methodist = null
    health_first      = null
    northbay          = null
    tampa_general     = null
    nyu               = null
    lifebridge        = null
    hmhn              = null
    baptist_memorial  = null
    musc              = null
  }
  google_workspace_connection = {
    client_id     = var.google_workspace_connection_client_id
    client_secret = var.google_workspace_connection_client_secret
  }
}
