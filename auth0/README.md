# tf-auth0

Terraform for Auth0 Configuration

## How to import a resource

Oftentimes it is useful to import an existing Auth0 resource into terraform to avoid introducing downtime.

Terraform currently has [an issue](https://github.com/hashicorp/terraform/issues/26494) where the
sensitive variables are not able to be picked up when running `terraform import` locally.
Therefore, we set the sensitive variables as environment variables in Terraform Cloud and must
set them as environment variables locally

1. Obtain the auth0 client secret and auth0 client id from Auth0
   1. [Dev](https://manage.auth0.com/dashboard/us/apella-dev/applications/ECPRssFvAMNjaNItwbEvbkFGs71YwB7w/settings)
   2. [Staging](https://manage.auth0.com/dashboard/us/apella-staging/applications/D5JBWVtSPbmfNvebY7WQPPak3N0FmWzn/settings)
   3. [Prod](https://manage.auth0.com/dashboard/us/apella/applications/esgk61gZQanZ6QLTzHX0rYeVyJlCZ4Vg/settings)
2. Add the variables as environment variables when running terraform import locally

```
TF_VAR_auth0_client_id=<client-id> \
TF_VAR_auth0_client_secret=<secret> \
terraform import <tf-resource-address> <id-of-resource>
```

3. Verify the import succeeded `terraform state list`
