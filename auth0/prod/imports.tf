import {
  to = module.apella_auth0.auth0_role_permissions.apella_csm
  id = "rol_TTqk5wNFfEPO5bUq"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_dashboard_user
  id = "rol_I19sS7Tk1wtMfxr4"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_developer
  id = "rol_SvFe0F9T8tZRwI8k"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_developer_readonly
  id = "rol_wB3u4ogDaCeVwd8I"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_internal_site_scoped_role_permissions
  id = "rol_2VoGViIDm68vmSd2"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_labeler
  id = "rol_dG3ykC0zNukrx0Fb"
}
import {
  to = module.apella_auth0.auth0_role_permissions.apella_label_reviewer
  id = "rol_6tJKIvXiOoN1DQ6z"
}
import {
  to = module.apella_auth0.auth0_role_permissions.camera_technician
  id = "rol_6XsBm3bRaJE1YMw7"
}
import {
  to = module.apella_auth0.auth0_role_permissions.customer_dashboard_user
  id = "rol_EPhVCuW6CVPpOMb7"
}
import {
  to = module.apella_auth0.auth0_role_permissions.data_integrator
  id = "rol_BDRgZXxUw9f8r41D"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_analyst"]
  id = "rol_MRAh1Ob7MwG7y5xl"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_big_board"]
  id = "rol_7Z3pDkXNgSgTLDdi"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_case_duration"]
  id = "rol_PTObygU7F3ywd169"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_evs"]
  id = "rol_aqRxNpAnDdp6BcZF"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_management"]
  id = "rol_GNd5wJvAHdAxUFCy"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hf_role_permissions["health_first_staff"]
  id = "rol_HoO20QNzCQgImm81"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["all_houston_methodist_hospital"]
  id = "rol_mWdT180vWK8T170g"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_dunn_03"]
  id = "rol_ByCh1pclwA0fbn1s"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_dunn_06"]
  id = "rol_XGIELCMmDwRxboVP"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmba"]
  id = "rol_9qZhL5e9CGn16WfI"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmbt"]
  id = "rol_hu6aD6JGTlirRUYn"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmbt_asc"]
  id = "rol_O7VVQRoRvdAUzXaz"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmbt_ld"]
  id = "rol_2kqfGHI4MV7wvhE6"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmcl"]
  id = "rol_kytubmDZ9Iddmq2l"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmcl_asc"]
  id = "rol_1zyUegxm86ChsoqB"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmcl_ld"]
  id = "rol_dEFmixHw55Ef8DKy"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmcy_ld"]
  id = "rol_EFqaZIhCezzDRq5N"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmcy_main"]
  id = "rol_6gDkMinHMtqzFtHQ"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmsl_ld"]
  id = "rol_OUXv7pCpJmsDX7TS"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmsl_main"]
  id = "rol_1RKAwm1etzYDXq48"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmtw_ld"]
  id = "rol_b9WHT07o4hHVwvcd"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmtw_main"]
  id = "rol_lVUJ2C4JKIomnO8m"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmw_ld"]
  id = "rol_KNGUMuSzpvmJFqdJ"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmw_main"]
  id = "rol_JKl5jcv7EWmQ4bmk"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_hmwb_cf"]
  id = "rol_4mmJqKkHw1WqDSgC"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_ld"]
  id = "rol_2Oe5QxdPxgsmhtGI"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_main_03"]
  id = "rol_Rq0DAdp1k7wV7sRw"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_opc_18"]
  id = "rol_2JdP9H6sLejEfBS2"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_opc_19"]
  id = "rol_idyBkAig7mL4XIJD"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["hmh_walter_tower_03"]
  id = "rol_TBNdV4n8Z7FGZem4"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_anesthesiology"]
  id = "rol_OfnnN2LCK9llnSix"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_board_user"]
  id = "rol_gOaU7hnPhFedud4U"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_charge_nurse"]
  id = "rol_X2jTK1y8SFOd06Qc"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_highlights_only"]
  id = "rol_5hT7t1qXW90i2pym"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_infection_prevention"]
  id = "rol_V5eShjBXtTv9aMAm"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_insights_only"]
  id = "rol_b61myTueYwYs550Y"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_leadership"]
  id = "rol_CEoRRE3LcNqxmf1J"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_management"]
  id = "rol_u1YApKzQIVlBLc25"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_perfusion"]
  id = "rol_4j2HuD7X0nMCw2ux"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_schedule_only"]
  id = "rol_itcHUi9My0PgcGo5"
}
import {
  to = module.apella_auth0.auth0_role_permissions.hm_role_permissions["houston_methodist_surgeon"]
  id = "rol_RVn4Y0msCFmQMCwn"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["G-OR"]
  id = "rol_WymAZf8L3XG9mZbr"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["lifebridge_anesthesiology"]
  id = "rol_5UxX2vv7yS6S7Got"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["lifebridge_charge_nurse"]
  id = "rol_Xilx1KzmY3h6iqMI"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["lifebridge_leadership"]
  id = "rol_LOLLQcgmzfCmtTFT"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["lifebridge_schedule_only"]
  id = "rol_MrDU9DGnQ8QccQ7K"
}
import {
  to = module.apella_auth0.auth0_role_permissions.lifebridge_role_permissions["lifebridge_surgeon"]
  id = "rol_EVex8YCsGW42pAdA"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_admin_role"]
  id = "rol_4PsC98webFLhxigh"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_anesthesiologist_role"]
  id = "rol_O3cuzCUscjp7JKrM"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_business_analyst_role"]
  id = "rol_5bIJ0iNmHedF5s9r"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_charge_nurse_role"]
  id = "rol_JfRVZdD0mhXB1hSH"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_clinicschedulers_role"]
  id = "rol_g98z3W3xJHFh7GVE"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_evs_role"]
  id = "rol_hqezB2StBKiLCUul"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_management_role"]
  id = "rol_V33U4d4jjUxgKvRy"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_orschedulers_role"]
  id = "rol_TbaGi7T85JRwuIrq"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_physician_role"]
  id = "rol_ZKnJbyQobW4oKK2k"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nb_role_permissions["northbay_staff_role"]
  id = "rol_lrWXdOtDuiRBJlH7"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_anesthesiology"]
  id = "rol_LRiQqMaGJXFtVEm7"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_charge_nurse"]
  id = "rol_hWEYtUNtXJN2V0q6"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_infection_prevention"]
  id = "rol_nqv08xqyaO7pGA8z"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_insights_only"]
  id = "rol_TTgVHex3vUx2dMMr"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_kp"]
  id = "rol_AghvWtHNvrBIFoj3"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_leadership"]
  id = "rol_eblxgcn17NpoYCDO"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_li"]
  id = "rol_T08qTEYoZlspQPCi"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_live_only"]
  id = "rol_s3WU9Ssfi991IEBD"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_management"]
  id = "rol_mHXJnlPB6dd0gCne"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_schedule_only"]
  id = "rol_SIYIKaYKr1O4XNRF"
}
import {
  to = module.apella_auth0.auth0_role_permissions.nyu_role_permissions["nyu_surgeon"]
  id = "rol_GzJVeSR2ynPZjWA4"
}
import {
  to = module.apella_auth0.auth0_role_permissions.service_account_writer
  id = "rol_TCTSKBPkIVyII7zU"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_clinician"]
  id = "rol_YGouGCggxN2JJbwc"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_leadership"]
  id = "rol_qKfjybpEGgpChJ6C"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_live_only"]
  id = "rol_QEdppfkWiiYeTAu3"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_or_manager"]
  id = "rol_8m5g48qbXv1vwohb"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_schedule_only"]
  id = "rol_5LE0xJq6xP2aRH5X"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tampa_general_surgeon"]
  id = "rol_gB2xH9wZd31nJ9Lg"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tgh_cvtor_03"]
  id = "rol_qkF6vKxR04cBGDOp"
}
import {
  to = module.apella_auth0.auth0_role_permissions.tg_role_permissions["tgh_main_02"]
  id = "rol_K8Z1UdZRECRzv3Sp"
}
