terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "auth0-prod"
    }
  }
  required_version = ">= 1.7.5"
}

data "terraform_remote_state" "prod_tf_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

data "terraform_remote_state" "prod_tf_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-prod"
    }
  }
}

data "terraform_remote_state" "prod_tf_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-prod"
    }
  }
}


module "apella_auth0" {
  environment = "prod"
  source      = "../modules/apella_auth0"
  gcp_project_ids = {
    web_api       = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-web-api-project.project_id,
    data_platform = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-data-platform-project.project_id,
    media_asset   = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-media-asset-project.project_id,
    ml            = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-ml-project.project_id,
    ehr           = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-ehr-project.project_id,
    edge          = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-edge-project.project_id,
    security      = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-security-project.project_id,
    internal      = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-internal-project.project_id,
  }
  gcp_sa = {
    dataflow = data.terraform_remote_state.prod_tf_security.outputs.prod-dataflow-sa
  }
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
  auth0_domain        = var.auth0_domain
  cloudflare_configuration = {
    api_token  = var.cloudflare_api_token
    account_id = var.cloudflare_account_id
  }
  custom_domain = "auth.apella.io"
  saml_certificates = {
    apella_internal   = null
    houston_methodist = var.houston_methodist_cert
    health_first      = var.health_first_signing_cert
    northbay          = var.northbay_signing_cert
    tampa_general     = var.tampa_general_signing_cert
    nyu               = var.nyu_signing_cert
    lifebridge        = var.lifebridge_signing_cert
    hmhn              = var.hmhn_signing_cert
    baptist_memorial  = var.baptist_memorial_signing_cert
    musc              = var.musc_signing_cert
  }
  google_workspace_connection = {
    client_id     = var.google_workspace_connection_client_id
    client_secret = var.google_workspace_connection_client_secret
  }
}
