variable "auth0_domain" {
  description = "Auth0 Domain"
  type        = string
}

variable "auth0_client_id" {
  description = "Auth0 Client ID"
  type        = string
  sensitive   = true
}

variable "auth0_client_secret" {
  description = "Auth0 Client Secret"
  type        = string
  sensitive   = true
}

variable "cloudflare_api_token" {
  type        = string
  description = "Cloudflare Provider Configuration API Token"
  sensitive   = true
}

variable "cloudflare_account_id" {
  type        = string
  description = "Cloudflare Provider Configuration Account ID"
  sensitive   = true
}

variable "houston_methodist_cert" {
  type        = string
  description = "Houston Methodist SAML cert"
  sensitive   = true
}

variable "health_first_signing_cert" {
  type        = string
  description = "Health First SAML signing cert"
  sensitive   = true
}

variable "northbay_signing_cert" {
  type        = string
  description = "Northbay SAML signing cert"
  sensitive   = true
}

variable "tampa_general_signing_cert" {
  type        = string
  description = "Tampa General SAML signing cert"
  sensitive   = true
}

variable "nyu_signing_cert" {
  type        = string
  description = "NYU SAML signing cert"
  sensitive   = true
}

variable "lifebridge_signing_cert" {
  type        = string
  description = "Lifebridge SAML signing cert"
  sensitive   = true
}

variable "hmhn_signing_cert" {
  type        = string
  description = "HMHN SAML signing cert"
  sensitive   = true
}

variable "baptist_memorial_signing_cert" {
  type        = string
  description = "Baptist Memorial SAML signing cert"
  sensitive   = true
}

variable "musc_signing_cert" {
  type        = string
  description = "Medical University of South Carolina SAML signing cert"
  sensitive   = true
}

variable "google_workspace_connection_client_secret" {
  type        = string
  description = "GCP Credential OAuth Client Secret. Stored in Apella Auth0 Connector project."
  sensitive   = true
}

variable "google_workspace_connection_client_id" {
  type        = string
  description = "GCP Credential OAuth Client ID. Stored in Apella Auth0 Connector project."
  sensitive   = true
}
