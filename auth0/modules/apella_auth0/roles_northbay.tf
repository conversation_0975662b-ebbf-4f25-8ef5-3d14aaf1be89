locals {
  nb_feature_roles = {
    northbay_admin_role : {
      name : "NorthBay Admin",
      ad_group : "ApellaAdmin",
      permissions : toset(concat(
        local.highlights_permissions,
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.terminal_cleans_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_business_analyst_role : {
      name : "NorthBay Business Analyst",
      ad_group : "ApellaAnalyst",
      permissions : toset(concat(
        local.insights_permissions,
        local.schedule_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_anesthesiologist_role : {
      name : "NorthBay Anesthesiologist",
      ad_group : "ApellaAnesthesiologist",
      permissions : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.board_permissions,
        local.live_streaming_permissions,
        local.available_times_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_charge_nurse_role : {
      name : "NorthBay Charge Nurse",
      ad_group : "ApellaChargeNurse",
      permissions : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.terminal_cleans_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.board_management_permissions,
        local.case_planning_management_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_clinicschedulers_role : {
      name : "NorthBay Clinic Schedulers",
      ad_group : "ApellaClinicSchedulers",
      permissions : toset(concat(
        local.case_duration_permissions,
        local.available_times_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_evs_role : {
      name : "NorthBay EVS",
      ad_group : "ApellaEVS",
      permissions : toset(concat(
        local.highlights_permissions,
        local.schedule_permissions,
        local.terminal_cleans_permissions,
        local.turnovers_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_management_role : {
      name : "NorthBay Management",
      ad_group : "ApellaManagement",
      permissions : toset(concat(
        local.highlights_permissions,
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.terminal_cleans_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.board_management_permissions,
        local.case_planning_management_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.available_times_permissions,
        [local.permissions.dashboard_edit_schedule],
        local.staff_management_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_orschedulers_role : {
      name : "NorthBay OR Schedulers",
      ad_group : "ApellaORSchedulers",
      permissions : toset(concat(
        local.schedule_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_orscheduler_planner_role : {
      name : "NorthBay OR Scheduler Planner",
      ad_group : "ApellaORSchedulerPlanner",
      permissions : toset(concat(
        local.schedule_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.board_management_permissions,
        local.case_planning_management_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_physician_role : {
      name : "NorthBay Physician",
      ad_group : "ApellaPhysicians",
      permissions : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.case_duration_permissions,
        local.live_streaming_permissions,
        local.turnovers_permissions,
        [local.permissions.site_read_any],
      ))
    },
    northbay_staff_role : {
      name : "NorthBay Staff",
      ad_group : "ApellaStaff",
      permissions : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.turnovers_permissions,
        local.board_permissions,
        [local.permissions.site_read_any],
      ))
    },
  }
  nb_roles            = local.nb_feature_roles
  nb_ad_group_to_role = { for k, v in auth0_role.nb_roles : local.nb_roles[k]["ad_group"] => v.id }
}

resource "auth0_role" "nb_roles" {
  for_each    = { for k, v in local.nb_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "nb_role_permissions" {
  for_each = { for k, v in local.nb_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.nb_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
