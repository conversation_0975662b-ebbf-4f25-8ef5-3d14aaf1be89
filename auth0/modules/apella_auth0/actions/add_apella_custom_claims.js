/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
    const customClaimBase = 'https://apella.io/'

    if (event.organization) {
        const orgIdClaimName = `${customClaimBase}org_id`;
        const orgIdClaimValue = event.organization.metadata['apella_org_id']

        // Backend services rely on the apella_org_id to exist for authorization.
        if (!orgIdClaimValue) {
            api.access.deny('Missing apella_org_id claim in organization: ' + event.organization.display_name)
        }

        // Add apella org id claim to both id token for front end logging (ie amplitude).
        // Add it also to the access token for tenant-based authorization ease of use.
        api.idToken.setCustomClaim(orgIdClaimName, orgIdClaimValue);
        api.accessToken.setCustomClaim(orgIdClaimName, orgIdClaimValue);
    }

    // Add the user's roles to the id token for front end logging (ie amplitude).
    // Note: the roles are not added to the access token because only permissions should be used for authorization, not roles.
    // Note 2: roles added through another action (ie assign_org_roles_to_users) will not be available here.
    //   Once a user refreshes the page, the roles will be accurate and available.
    if (event.authorization?.roles) {
        api.idToken.setCustomClaim(`${customClaimBase}roles`, event.authorization.roles)
    }
};
