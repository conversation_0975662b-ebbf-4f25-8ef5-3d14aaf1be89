/**
* Prevent username/password sign ups from unauthorized email domains.
* Note: this will only run for database connections (username/password), not social or enterprise
* connections.
*
* Currently, as of July 2023, all hospital and Apella users use a separate IdP to authenticate.
* As such, we currently don't have a need to allow any sign ups through username/password. We do
* however have service accounts which act as users, and we want to allow those to sign up.
*
* This action only allows certain email domains to sign up. All other email domains fail.
* In tandem, there is a separate action which enforces email addresses must be verified to log in.
*
* @param {Event} event - Details about the context and user that is attempting to register.
* @param {PreUserRegistrationAPI} api - Interface whose methods can be used to change the behavior of the sign up.
*/
exports.onExecutePreUserRegistration = async (event, api) => {
  const allowlist = [
    %{ for domain in client_domain_allowlist ~}
      '${domain}',
    %{ endfor ~}
  ];

  const hasAllowedEmailDomain = allowlist.some((domain) => event.user.email?.endsWith(domain));

  if (!hasAllowedEmailDomain) {
    api.access.deny("The user's email domain was not part of the allowlist", "Access Denied")
  }
};
