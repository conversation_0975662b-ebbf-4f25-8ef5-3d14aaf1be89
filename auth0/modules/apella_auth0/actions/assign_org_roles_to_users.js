const auth0 = require("auth0")

/**
 * Handler that will be called during the execution of a PostLogin flow.
 * Based on the user's identity service role (ie `groups`), the connection ID, and the organization,
 * automatically add roles to the user for that organization.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 */
exports.onExecutePostLogin = async (event) => {
  // To be interpolated by templatefile(file, vars)
  const connectionMap = {
%{ for connection_id, properties in connection_map }
    '${connection_id}': {
      orgId: "${properties.org_id}",
      userPropertyToRole: {
          %{ for user_prop, role_id in properties.user_property_to_role }
          "${user_prop}": "${role_id}",
          %{ endfor ~}
      }
    },
%{ endfor ~}
  }

  const ManagementClient = auth0.ManagementClient;
  const management = new ManagementClient({
    domain: event.secrets.domain,
    clientId: event.secrets.clientId,
    clientSecret: event.secrets.clientSecret,
  });

  const connectionInfo = connectionMap[event.connection.id]
  if (connectionInfo === undefined) {
    console.log('User is not logging in through known connection: ' + event.connection.id)
    return
  }

  if (event.organization?.id !== connectionInfo.orgId) {
    console.log("User's org id " + event.organization?.id + " does not match the connection's org id: " +
    connectionInfo.orgId)
    return
  }

  try {
    const user = await management.users.get({id: event.user.user_id})
    const userGroups = getUserGroups(user, event.connection.name)

    const userPropertyToRole = connectionInfo.userPropertyToRole
    const apellaRoleIds = getApellaRoles(userGroups, userPropertyToRole)


    const params = { id: event.organization.id, user_id: event.user.user_id };
    console.log('User should have the following roles: ' + apellaRoleIds.join(', '))

    const currentRoles = await management.organizations.getMemberRoles(params)
    const currentRoleIds = currentRoles.map(role => role.id)
    console.log('User currently has the following roles: ' + currentRoleIds.join(', '));
    if (arrayEquals(currentRoleIds, apellaRoleIds)) {
      console.log('All set. User already has correct roles.')
      return
    }

    if (currentRoleIds.length > 0) {
      await management.organizations.removeMemberRoles(params, {roles: currentRoleIds})
      console.log('Users roles were removed ' + currentRoleIds.join(', '));
    }

    if(apellaRoleIds.length > 0) {
      await management.organizations.addMemberRoles(params, {roles: apellaRoleIds});
      console.log('User was granted Apella role ' + apellaRoleIds.join(', '));
    }
  } catch (err) {
    console.error(err);
  }
}

/**
 * Get the user's groups.
 * The user groups can be a string or an array depending on whether the user belongs to one
 * or multiple groups. If the user doesn't belong to any group, the field will be undefined.
 *
 * If a user has multiple identities (SAML, google-workspace, etc.) then the user?.groups
 * property will be undefined. The user's groups will be inside the specific user identity
 * for the given user connection. This currently only applies to legacy Apella users,
 * who have multiple identities.
 *
 * @param user {{groups: string|string[]|undefined, identities: {connection: string, profileData: {groups: string|string[]|undefined}}[]}}
 * @param connectionName {string}
 * @return {string[]}
 */
const getUserGroups = (user, connectionName) => {
  let userGroups = user?.groups

  if (userGroups === undefined) {
    const userIdentity = user.identities.find(identity => identity.connection === connectionName)
    userGroups = userIdentity?.profileData?.groups
  }

  if (userGroups === undefined) {
    return []
  }

  if (typeof userGroups == 'string') {
    return [userGroups]
  }

  if (!Array.isArray(userGroups)) {
    throw "Unknown type for user's groups: " + userGroups
  }

  const distinctUserGroups = [...new Set(userGroups)]

  return distinctUserGroups
}

/**
 * Get the Apella roles corresponding to the known, non-empty AD groups.
 * @param userGroups {string[]}
 * @param userGroupToRole {{[key: string]: string}}
 * @return {string[]}
 */
const getApellaRoles = (userGroups, userGroupToRole) => {
  // Remove the unknown or undefined user groups
  const knownUserGroups = userGroups.filter((userGroup) => {
    if (!(userGroup in userGroupToRole)) {
      return false
    }

    const apellaRoleId = userGroupToRole[userGroup]
    return apellaRoleId !== "";
  })

  // Return just the known Apella roles
  return knownUserGroups.map((userGroup) =>  userGroupToRole[userGroup])
}


/**
 * Simple array equality check. I didn't feel like importing a heavy package like lodash for this.
 */
const arrayEquals = (a, b) => {
  a.sort()
  b.sort()
  return Array.isArray(a) &&
    Array.isArray(b) &&
    a.length === b.length &&
    a.every((val, index) => val === b[index]);
}
