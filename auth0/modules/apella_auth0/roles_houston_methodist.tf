locals {
  hmh_feature_roles = {
    all_houston_methodist_hospital : {
      "name" : "All Houston Methodist Hospitals Systemwide",
      # apl_Apella_HM_SYSTEMWIDE
      "ad_group" : "fc9178e2-e2d0-47af-8d3a-f0e01068399a",
      "permissions" : toset([
        local.permissions.site_read_any,
      ])
    },
    "houston_methodist_leadership" : {
      "name" : "HM Leadership",
      # apl_Apella_Leadership
      "ad_group" : "3ace1545-4220-43cb-852e-78aae8f7509e",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.highlights_permissions,
        local.terminal_cleans_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.available_times_permissions,
        local.manage_room_configuration,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "houston_methodist_management" : {
      "name" : "<PERSON> Management",
      # apl_Apella_Management
      "ad_group" : "e205de24-31a8-4c10-ad49-a1cffd7df149",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.terminal_cleans_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.available_times_permissions,
        local.manage_room_configuration,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "houston_methodist_charge_nurse" : {
      "name" : "HM Charge Nurse",
      # apl_Apella_ChargeRN
      "ad_group" : "3b86ab33-9301-4e41-9228-506e40ca1364",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.available_times_permissions,
        local.manage_room_configuration,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "houston_methodist_anesthesiology" : {
      "name" : "HM Anesthesiology",
      # apl_Apella_Anesthesiology
      "ad_group" : "6810a5c5-3d2d-44f9-847f-eda30f48af4e",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
      ))
    },
    "houston_methodist_surgeon" : {
      "name" : "HM Surgeon",
      # apl_Apella_Surgeon
      "ad_group" : "1cebec40-06a0-41b0-a609-dc82752ee3fb",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.turnovers_permissions,
      ))
    },
    "houston_methodist_perfusion" : {
      "name" : "HM Perfusion",
      # apl_Apella_Perfusion
      "ad_group" : "06b7958f-c98b-44b5-8efd-b34b69111c28",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
      ))
    },
    "houston_methodist_infection_prevention" : {
      "name" : "HM Infection Prevention",
      # apl_Apella_InfectionPrevention
      "ad_group" : "ab81eda1-cde1-4e00-8f53-82c6cdfdacd5",
      "permissions" : toset(concat(
        local.highlights_permissions,
        local.terminal_cleans_permissions,
        local.historical_video_permissions,
      ))
    },
    "houston_methodist_highlights_only" : {
      "name" : "HM Highlights Only",
      # apl_Apella_HighlightsOnly
      "ad_group" : "9423cf49-0973-4e19-821d-d72681b2018c",
      "permissions" : toset(concat(
        local.highlights_permissions,
      ))
    },
    "houston_methodist_schedule_only" : {
      "name" : "HM Schedule Only",
      # apl_Apella_ScheduleOnly
      "ad_group" : "46658d1a-169c-4abd-aa20-9cd25e7058d9",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "houston_methodist_insights_only" : {
      "name" : "HM Insights Only",
      # apl_Apella_InsightsOnly
      "ad_group" : "35c6d28c-3543-4a0e-970e-fd36881771a0",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.case_duration_permissions,
      ))
    },
    "houston_methodist_board_user" : {
      "name" : "HM Board User",
      # apl_Apella_Board
      "ad_group" : "338c8187-c632-4eb2-8cc6-b85aaad1baa0",
      "permissions" : toset(concat(
        local.board_permissions,
        local.turnovers_permissions,
      ))
    },
  }
  hm_site_specific_roles = { for k, v in local.hm_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.hm_site_specific_permission_map[k]
    ])
  } }
  hmh_roles           = merge(local.hmh_feature_roles, local.hm_site_specific_roles)
  hm_ad_group_to_role = { for k, v in auth0_role.hm_roles : local.hmh_roles[k]["ad_group"] => v.id }
}

// ******************
// HMH-wide Roles
// ******************
resource "auth0_role" "hm_roles" {
  for_each    = { for k, v in local.hmh_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "hm_role_permissions" {
  for_each = { for k, v in local.hmh_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.hm_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
