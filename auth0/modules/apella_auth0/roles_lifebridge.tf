locals {
  lifebridge_feature_roles = {
    "lifebridge_leadership" : {
      "name" : "Lifebridge Leadership",
      "ad_group" : "Apella_Leadership",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.highlights_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "lifebridge_charge_nurse" : {
      "name" : "Lifebridge Charge Nurse",
      "ad_group" : "Apella_ChargeRN",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.board_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "lifebridge_schedule_only" : {
      "name" : "Lifebridge Schedule Only",
      "ad_group" : "Apella_ScheduleOnly",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.board_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "lifebridge_surgeon" : {
      "name" : "Lifebridge Surgeon",
      "ad_group" : "Apella_Surgeon",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
        local.schedule_permissions,
        local.board_permissions,
        local.turnovers_permissions,
      ))
    },
    "lifebridge_anesthesiology" : {
      "name" : "Lifebridge Anesthesiology",
      "ad_group" : "Apella_Anesthesiology",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
        local.schedule_permissions,
        local.board_permissions,
        local.turnovers_permissions,
      ))
    },
  }
  lifebridge_site_specific_roles = { for k, v in local.lifebridge_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.lifebridge_site_specific_permission_map[k]
    ])
  } }
  lifebridge_roles            = merge(local.lifebridge_feature_roles, local.lifebridge_site_specific_roles)
  lifebridge_ad_group_to_role = { for k, v in auth0_role.lifebridge_roles : local.lifebridge_roles[k]["ad_group"] => v.id }
}

// ******************
// Lifebridge-wide Roles
// ******************
resource "auth0_role" "lifebridge_roles" {
  for_each    = { for k, v in local.lifebridge_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "lifebridge_role_permissions" {
  for_each = { for k, v in local.lifebridge_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.lifebridge_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
