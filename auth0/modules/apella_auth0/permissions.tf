locals {
  permissions = {
    annotation_task_write_any : {
      description = "Write any annotation task"
      value       = "annotation_task:write:any"
    },
    annotation_task_read_any : {
      description = "Read any annotation task"
      value       = "annotation_task:read:any"
    },
    annotation_task_type_write_any : {
      description = "Write any annotation task type"
      value       = "annotation_task_type:write:any"
    },
    available_times_email_any : {
      description = "Email available times"
      value       = "available_times:email:any"
    },
    big_board_read_any : {
      description = "Read any big board content"
      value       = "big_board:read:any"
    },
    big_board_write_any : {
      description = "Write any big board content"
      value       = "big_board:write:any"
    },
    camera_read_any : {
      description = "Read any camera"
      value       = "camera:read:any"
    },
    camera_read_default : {
      description = "Read default camera"
      value       = "camera:read:default"
    },
    camera_write_any : {
      description = "Write any camera"
      value       = "camera:write:any"
    },
    case_duration_read_any : {
      description = "Read any case duration content"
      value       = "case_duration:read:any"
    },
    case_note_plan_read_any : {
      description = "Read any case notes plan"
      value       = "case_note_plan:read:any"
    },
    case_note_plan_write_any : {
      description = "Write any case notes plan"
      value       = "case_note_plan:write:any"
    },
    case_write_any : {
      description = "Write any case"
      value       = "case:write:any"
    },
    case_read_any : {
      description = "Read any case"
      value       = "case:read:any"
    },
    case_staff_plan_read_any : {
      description = "Read any case staff plan"
      value       = "case_staff_plan:read:any"
    },
    case_staff_plan_write_any : {
      description = "Write any case staff plan"
      value       = "case_staff_plan:write:any"
    },
    cluster_read_any : {
      description = "Read any cluster"
      value       = "cluster:read:any"
    },
    cluster_write_any : {
      description = "Write any cluster"
      value       = "cluster:write:any"
    },
    event_read_any : {
      description = "Read any event"
      value       = "event:read:any"
    },
    event_write_any : {
      description = "Write any event"
      value       = "event:write:any"
    },
    event_type_write_any : {
      description = "Write any event type"
      value       = "event_type:write:any"
    },
    feedback_read_any : {
      description = "Read any feedback"
      value       = "feedback:read:any"
    },
    feedback_write_if_assigned : {
      description = "Write feedback if assigned to the highlight"
      value       = "feedback:write_if_assigned"
    },
    highlight_read_any : {
      description = "Read any highlight. Access dashboard's Highlight product."
      value       = "highlight:read:any"
    },
    highlight_read_if_assigned : {
      description = "Read a highlight if you're assigned. Access dashboard's Highlight product."
      value       = "highlight:read_if_assigned"
    },
    highlight_write_any : {
      description = "Write any highlight"
      value       = "highlight:write:any"
    },
    live_stream_read_any : {
      description = "View any live stream."
      value       = "live_stream:read:any"
    },
    measurement_period_read_any : {
      description = "Read any measurement period within Terminal Cleans product."
      value       = "measurement_period:read:any"
    },
    measurement_period_write_any : {
      description = "Write any measurement period within Terminal Cleans product."
      value       = "measurement_period:write:any"
    },
    media_asset_read_any : {
      description = "Read any media asset. Access historical video."
      value       = "media_asset:read:any"
    },
    media_asset_read_if_assigned : {
      description = "If a user is assigned to a media asset then they can view it. Access historical video."
      value       = "media_asset:read_if_assigned"
    },
    media_asset_write_any : {
      description = "Write any media asset"
      value       = "media_asset:write:any"
    },
    mapping_read_any : {
      description = "Read any mapping"
      value       = "mapping:read:any"
    },
    mapping_write_any : {
      description = "Write any mapping"
      value       = "mapping:write:any"
    },
    object_read_any : {
      description = "Read any object data stream"
      value       = "object:read:any"
    },
    org_read_any : {
      description = "Read any organization"
      value       = "org:read:any"
    },
    org_write_any : {
      description = "Write any organization"
      value       = "org:write:any"
    },
    patient_read_all : {
      description = "Read all patient"
      value       = "patient:read:all"
    },
    room_read_any : {
      description = "Read any room"
      value       = "room:read:any"
    },
    room_write_any : {
      description = "Write any room"
      value       = "room:write:any"
    },
    room_write_configuration : {
      description = "Write configuration settings for any room"
      value       = "room:write:configuration"
    },
    site_read_any : {
      description = "Read any site"
      value       = "site:read:any"
    },
    site_write_any : {
      description = "Write any site"
      value       = "site:write:any"
    },
    site_write_launch : {
      description = "Write any site launch"
      value       = "site:write:launch"
    },
    user_read_any : {
      description = "Read any user"
      value       = "user:read:any"
    },
    dashboard_read_schedule : {
      description = "Access dashboard Schedule product.",
      value       = "dashboard:schedule:read:any",
    }
    dashboard_edit_schedule : {
      description = "Edit dashboard Schedule product.",
      value       = "dashboard:schedule:edit:any",
    }
    dashboard_read_optimized : {
      description = "Read Optimization in Dashboard",
      value       = "dashboard:optimized:read:any",
    }
    dashboard_read_insights : {
      description = "Access dashboard Insights product.",
      value       = "dashboard:insights:read:any",
    }
    dashboard_read_live : {
      description = "Access dashboard Live product.",
      value       = "dashboard:live:read:any"
    }
    dashboard_read_live_from_schedule : {
      description = "Access Live from the dashboard Schedule product.",
      value       = "dashboard:live_from_schedule:read:any"
    }
    dashboard_read_terminal_cleans : {
      description = "Access dashboard Terminal Cleans product.",
      value       = "dashboard:terminal_cleans:read:any"
    }
    dashboard_read_turnovers : {
      description = "Access dashboard Turnover product.",
      value       = "dashboard:turnovers_dashboard:read:any",
    },
    staffing_needs_write_any : {
      description = "Write any staffing needs"
      value       = "staffing_needs:write:any"
    },
    staffing_needs_read_any : {
      description = "Read any staffing needs"
      value       = "staffing_needs:read:any"
    },
    block_read_any : {
      description = "Read any block"
      value       = "block:read:any"
    },
    block_write_any : {
      description = "Write any block"
      value       = "block:write:any"
    }

    //Contact Information and Notifications Permissions

    contact_information_read_any : {
      description = "Read any contact information"
      value       = "contact_information:read:any"
    }

    contact_information_write_any : {
      description = "Write any contact information"
      value       = "contact_information:write:any"
    }

    notification_write_any : {
      description = "Write any notification"
      value       = "notification:write:any"
    }

    notification_read_any : {
      description = "Read any notification"
      value       = "notification:read:any"
    }

    // Apella Internal permissions
    site_read_palo_alto_1 : {
      description = "Read Apella Internal Palo Alto 1",
      value       = "site:read:palo_alto_1"
    }
  }
  board_permissions = [
    local.permissions.big_board_read_any,
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_read_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.event_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.object_read_any,
    local.permissions.patient_read_all,
    local.permissions.room_read_any,
    local.permissions.notification_read_any,
    local.permissions.contact_information_read_any,
    # Needed to read the "last edited by" user for each board in list.
    local.permissions.user_read_any,
  ]
  # Allows the creation and editing of Board configurations.
  # Note this grouping of permissions builds upon the `board_permission` set.
  board_management_permissions = [
    local.permissions.big_board_write_any,
  ]
  # Allows the creation and editing of case staff plans, case notes, and case flags.
  # Note: these build upon existing permission groups to read case planning, such as `board_permissions`.
  case_planning_management_permissions = [
    local.permissions.case_note_plan_write_any,
    local.permissions.case_staff_plan_write_any,
  ]
  insights_permissions = [
    local.permissions.block_read_any,
    local.permissions.dashboard_read_insights,
    local.permissions.event_read_any,
    local.permissions.room_read_any,
    local.permissions.case_read_any,
    local.permissions.patient_read_all,
    local.permissions.notification_read_any,
    local.permissions.contact_information_read_any,
  ]
  live_permissions = [
    local.permissions.block_read_any,
    local.permissions.room_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.event_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.patient_read_all,
    local.permissions.notification_read_any,
    local.permissions.contact_information_read_any,
    local.permissions.object_read_any
  ]
  schedule_permissions = [
    local.permissions.block_read_any,
    local.permissions.case_read_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.dashboard_read_schedule,
    local.permissions.event_read_any,
    local.permissions.object_read_any,
    local.permissions.patient_read_all,
    local.permissions.room_read_any,
    local.permissions.notification_read_any,
    local.permissions.contact_information_read_any,
  ]
  highlights_permissions = [
    local.permissions.org_read_any,
    local.permissions.event_read_any,
    local.permissions.block_read_any,
    local.permissions.case_read_any,
    local.permissions.object_read_any,
    local.permissions.camera_read_any,
    local.permissions.feedback_write_if_assigned,
    local.permissions.highlight_read_if_assigned,
    local.permissions.room_read_any,
    local.permissions.notification_read_any,
    local.permissions.contact_information_read_any,
  ]
  terminal_cleans_permissions = [
    local.permissions.annotation_task_read_any,
    local.permissions.camera_read_any,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.event_read_any,
    local.permissions.measurement_period_read_any,
    local.permissions.room_read_any,
  ]
  live_streaming_permissions = [
    local.permissions.room_read_any,
    local.permissions.camera_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.patient_read_all
  ]
  historical_video_permissions = [
    local.permissions.camera_read_any,
    local.permissions.room_read_any,
    local.permissions.media_asset_read_any,
  ]
  staff_management_permissions = [
    local.permissions.block_read_any,
    local.permissions.case_read_any,
    local.permissions.staffing_needs_write_any,
    local.permissions.event_read_any,
    local.permissions.room_read_any,
    local.permissions.staffing_needs_read_any,
  ]
  case_duration_permissions = [
    local.permissions.block_read_any,
    // Needed for the Organization selector in the UI to work.
    local.permissions.room_read_any,
    // Needed to access the Case Duration page.
    local.permissions.case_duration_read_any,
    // Needed to load the list of surgeons and procedures on the page.
    local.permissions.case_read_any,
  ]
  available_times_permissions = [
    local.permissions.block_read_any,
    // Needed for the Organization selector in the UI to work.
    local.permissions.room_read_any,
    // Needed to access the Case Duration page.
    local.permissions.case_duration_read_any,
    // Needed to access the Available Time Email page.
    local.permissions.available_times_email_any,
    // Needed to load the list of surgeons and procedures on the page.
    local.permissions.case_read_any,
  ]
  turnovers_permissions = [
    local.permissions.dashboard_read_turnovers,
    local.permissions.case_read_any,
    local.permissions.event_read_any,
    local.permissions.room_read_any,
  ]
  manage_room_configuration = [
    local.permissions.room_write_configuration,
  ]

  environment_scoped_permissions = {
    dev     = {}
    staging = merge(local.hf_site_specific_permissions, local.hm_site_specific_permissions, local.tg_site_specific_permissions, local.nyu_site_specific_permissions, local.lifebridge_site_specific_permissions, local.hmhn_site_specific_permissions, local.baptist_memorial_site_specific_permissions, local.musc_site_specific_permissions)
    prod    = merge(local.hf_site_specific_permissions, local.hm_site_specific_permissions, local.tg_site_specific_permissions, local.nyu_site_specific_permissions, local.lifebridge_site_specific_permissions, local.hmhn_site_specific_permissions, local.baptist_memorial_site_specific_permissions, local.musc_site_specific_permissions)
  }
}
