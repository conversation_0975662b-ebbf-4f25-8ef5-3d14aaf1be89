locals {
  dashboard = {
    url = {
      local   = "http://localhost:3020"
      dev     = "https://dashboard.dev.apella.io"
      staging = "https://dashboard.staging.apella.io"
      prod    = "https://dashboard.apella.io"
    }
  }
  dashboardPrUrl = var.environment == "dev" ? ["https://pr-*.dashboard.dev.internal.apella.io"] : []
  dashboardUrls  = concat([local.dashboard.url[var.environment], local.dashboard.url.local], local.dashboardPrUrl)
}
resource "auth0_client" "apella_dashboard" {
  # Basic Information
  name = "Dashboard Web App"

  # Application Properties
  app_type = "spa"

  # Application URIs
  initiate_login_uri  = local.dashboard.url[var.environment]
  callbacks           = local.dashboardUrls
  allowed_logout_urls = local.dashboardUrls
  web_origins         = local.dashboardUrls

  # Organizations
  organization_require_behavior = "post_login_prompt"
  organization_usage            = "require"

  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }

  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 86400
    infinite_idle_token_lifetime = false
    infinite_token_lifetime      = false
    leeway                       = 60
    rotation_type                = "rotating"
    token_lifetime               = 86401
  }

  # Advanced Settings
  cross_origin_auth = false
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
}

resource "auth0_client_credentials" "apella_dashboard_credentials" {
  client_id = auth0_client.apella_dashboard.id

  # Ported from changes in the UI dashboard to prevent infinite login loop
  authentication_method = "none"
}

resource "auth0_client" "apella_cli" {
  # Basic Information
  name = "Apella CLI"

  # Application Properties
  app_type = "native"
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
    "urn:ietf:params:oauth:grant-type:device_code",
  ]

  # Application URIs
  web_origins = []

  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }

  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 1296000
    infinite_idle_token_lifetime = true
    infinite_token_lifetime      = false
    leeway                       = 0
    rotation_type                = "rotating"
    token_lifetime               = 2592000
  }

  # Advanced Settings
  cross_origin_auth = true
}

locals {
  internal_tools = {
    url = {
      local   = "http://localhost:3000"
      dev     = "https://internal.dev.apella.io"
      staging = "https://internal.staging.apella.io"
      prod    = "https://internal.apella.io"
    }
  }
  internalPrUrl = var.environment == "dev" ? ["https://pr-*.internal.dev.internal.apella.io"] : []
  internalUrls  = concat([local.internal_tools.url[var.environment], local.internal_tools.url.local], local.internalPrUrl)
}
resource "auth0_client" "apella_internal_tools" {
  # Basic Information
  name = "Internal Tools Web App"

  # Application Properties
  app_type = "spa"

  # Application URIs
  callbacks           = local.internalUrls
  allowed_logout_urls = local.internalUrls
  web_origins         = local.internalUrls

  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }

  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 1296000
    infinite_idle_token_lifetime = false
    infinite_token_lifetime      = false
    leeway                       = 0
    rotation_type                = "rotating"
    token_lifetime               = 2592000
  }

  # Advanced Settings
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
  cross_origin_auth = true
}

locals {
  boards = {
    url = {
      dev     = "https://boards.dev.apella.io"
      staging = "https://boards.staging.apella.io"
      prod    = "https://boards.apella.io"
    }
  }
}
resource "auth0_client" "apella_boards" {
  name = "Boards Web App"

  app_type        = "spa"
  oidc_conformant = true

  # Application URIs
  initiate_login_uri  = local.boards.url[var.environment]
  callbacks           = [local.boards.url[var.environment]]
  allowed_logout_urls = [local.boards.url[var.environment]]
  web_origins         = [local.boards.url[var.environment]]

  # Organizations
  organization_require_behavior = "post_login_prompt"
  organization_usage            = "require"

  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }

  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 2592000
    infinite_idle_token_lifetime = false
    infinite_token_lifetime      = false
    leeway                       = 0
    rotation_type                = "rotating"
    token_lifetime               = 2592001
  }

  # Advanced Settings
  cross_origin_auth = false
  grant_types = [
    "authorization_code",
    "implicit",
    "refresh_token",
  ]
}

resource "auth0_client" "api_explorer_application" {
  name     = "API Explorer Application"
  app_type = "non_interactive"
  grant_types = [
    "client_credentials",
  ]

  # Advanced Settings
  cross_origin_auth = true
}

resource "auth0_client" "api_server" {
  name     = "API Server"
  app_type = "non_interactive"
  grant_types = [
    "client_credentials",
  ]

  # Advanced Settings
  cross_origin_auth = true
}

resource "auth0_client" "terraform_auth0_provider" {
  name     = "Terraform Auth0 Provider"
  app_type = "non_interactive"
  grant_types = [
    "client_credentials",
  ]

  # Advanced Settings
  cross_origin_auth = true
}

// https://github.com/auth0/terraform-provider-auth0/blob/main/MIGRATION_GUIDE.md#reading-client-secret
// For clients needing to expose the client_secret, there needs to be a `data` source.
data "auth0_client" "auth0_actions_management_api_client" {
  client_id = auth0_client.auth0_actions_management_api_client.id
}

resource "auth0_client" "auth0_actions_management_api_client" {
  name     = "Auth0 Actions Management API Client"
  app_type = "non_interactive"
  grant_types = [
    "client_credentials",
  ]

  # Advanced Settings
  cross_origin_auth = true
}

resource "auth0_client_grant" "auth0_actions_auth0_management_api" {
  client_id = auth0_client.auth0_actions_management_api_client.id
  # The terraform provider doesn't allow importing the resource_server for the Auth0 Management
  # API through a data source. Therefore, we infer the audience from the tenant id.
  audience = "https://${auth0_tenant.apella_tenant.id}.us.auth0.com/api/v2/"
  scopes = [
    "read:users",
    "update:users",
    "read:organizations",
    "create:organization_members",
    "create:organization_member_roles",
    "read:organization_member_roles",
    "delete:organization_member_roles"
  ]
}

data "auth0_client" "data_warehouse_management_api_client" {
  client_id = auth0_client.data_warehouse_management_api_client.id
}

resource "auth0_client" "data_warehouse_management_api_client" {
  name     = "Data Warehouse Management API Client"
  app_type = "non_interactive"
  grant_types = [
    "client_credentials",
  ]

  # Advanced Settings
  cross_origin_auth = true
}

resource "auth0_client_grant" "data_warehouse_management_api_client" {
  client_id = auth0_client.data_warehouse_management_api_client.id
  audience  = "https://${auth0_tenant.apella_tenant.id}.us.auth0.com/api/v2/"
  scopes = [
    "read:users",
    "read:organizations",
    "read:organization_members",
    "read:organization_member_roles",
  ]
}

resource "auth0_client" "apollo_explorer" {
  allowed_clients                     = []
  allowed_logout_urls                 = []
  allowed_origins                     = ["https://preflight-request.apollographql.com"]
  app_type                            = "regular_web"
  callbacks                           = ["https://studio.apollographql.com/explorer-oauth2"]
  client_aliases                      = []
  client_metadata                     = {}
  cross_origin_auth                   = false
  cross_origin_loc                    = null
  custom_login_page                   = null
  custom_login_page_on                = true
  description                         = null
  form_template                       = null
  grant_types                         = ["authorization_code", "refresh_token"]
  initiate_login_uri                  = null
  is_first_party                      = true
  is_token_endpoint_ip_header_trusted = false
  logo_uri                            = null
  name                                = "Apollo Explorer"
  oidc_conformant                     = true
  organization_require_behavior       = "no_prompt"
  organization_usage                  = "allow"
  sso                                 = false
  sso_disabled                        = false
  web_origins                         = []
  jwt_configuration {
    alg                 = "RS256"
    lifetime_in_seconds = 36000
    scopes              = {}
    secret_encoded      = false
  }
  native_social_login {
    apple {
      enabled = false
    }
    facebook {
      enabled = false
    }
  }
  refresh_token {
    expiration_type              = "expiring"
    idle_token_lifetime          = 3600
    infinite_idle_token_lifetime = false
    infinite_token_lifetime      = false
    leeway                       = 60
    rotation_type                = "rotating"
    token_lifetime               = 86400
  }
}
