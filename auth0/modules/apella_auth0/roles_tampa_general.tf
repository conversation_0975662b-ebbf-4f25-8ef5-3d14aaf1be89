locals {
  tgh_feature_roles = {
    "tampa_general_leadership" : {
      "name" : "TGH Leadership",
      "ad_group" : "Apella_Leadership",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.highlights_permissions,
        local.terminal_cleans_permissions,
        local.live_streaming_permissions,
        local.historical_video_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.available_times_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "tampa_general_or_manager" : {
      "name" : "TGH OR Manager",
      "ad_group" : "Apella_ORManagers",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.case_duration_permissions,
        local.available_times_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "tampa_general_live_only" : {
      "name" : "TGH Live Only",
      "ad_group" : "Apella_LiveOnly",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
      ))
    },
    "tampa_general_surgeon" : {
      "name" : "TGH Surgeon",
      "ad_group" : "Apella_Surgeon",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
        local.schedule_permissions,
        local.turnovers_permissions,
      ))
    },
    "tampa_general_schedule_only" : {
      "name" : "TGH Schedule Only",
      "ad_group" : "Apella_ScheduleOnly",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
      ))
    },
    "tampa_general_clinician" : {
      "name" : "TGH Clinician",
      "ad_group" : "Apella_Clinician",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.schedule_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
  }
  tg_site_specific_roles = { for k, v in local.tg_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.tg_site_specific_permission_map[k]
    ])
  } }
  tgh_roles           = merge(local.tgh_feature_roles, local.tg_site_specific_roles)
  tg_ad_group_to_role = { for k, v in auth0_role.tg_roles : local.tgh_roles[k]["ad_group"] => v.id }
}

// ******************
// TGH-wide Roles
// ******************
resource "auth0_role" "tg_roles" {
  for_each    = { for k, v in local.tgh_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "tg_role_permissions" {
  for_each = { for k, v in local.tgh_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.tg_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
