locals {
  musc_feature_roles = {
    "musc_schedule_only" : {
      "name" : "Medical Univeristy of South Carolina Schedule Only",
      "ad_group" : "SCHEDULE_REPLACE_ME",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
      ))
    },
  }
  musc_site_specific_roles = { for k, v in local.musc_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.musc_site_specific_permission_map[k]
    ])
  } }
  musc_roles            = merge(local.musc_feature_roles, local.musc_site_specific_roles)
  musc_ad_group_to_role = { for k, v in auth0_role.musc_roles : local.musc_roles[k]["ad_group"] => v.id }
}

// ******************
// Medical University of South Carolina-wide Roles
// ******************
resource "auth0_role" "musc_roles" {
  for_each    = { for k, v in local.musc_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "musc_role_permissions" {
  for_each = { for k, v in local.musc_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.musc_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
