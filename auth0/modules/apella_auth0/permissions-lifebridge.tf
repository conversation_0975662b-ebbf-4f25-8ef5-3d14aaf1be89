locals {
  // Lifebridge permissions
  lifebridge_sites = {
    "G-OR" : {
      "name" : "SHB-GOR",
      "slug" : "shb-gor",
      "ad_group" : "SHB-GOR",
      "site_id" : "LBHS-GOR"
    },
  }
  lifebridge_site_specific_permission_map = { for k, v in local.lifebridge_sites : k => {
    description : "Read ${v["name"]} site",
    value : "site:read:${v["site_id"]}"
  } }
  lifebridge_site_specific_permissions = { for k, v in local.lifebridge_site_specific_permission_map : "site_read_${try(v["slug"], k)}" => v }
}
