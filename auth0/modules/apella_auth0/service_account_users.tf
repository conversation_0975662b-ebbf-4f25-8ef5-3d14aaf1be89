locals {
  github_runner_sa = var.environment == "prod" ? "prod-github-runner-sa" : "nonprod-github-runner-sa"

  service_account_writer_emails = [
    "annotation-task-scheduler@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "autoedge@${var.gcp_project_ids.edge}.iam.gserviceaccount.com",
    "block-release-service-account@${var.gcp_project_ids.ehr}.iam.gserviceaccount.com",
    "event-processor@${var.gcp_project_ids.data_platform}.iam.gserviceaccount.com",
    "${local.github_runner_sa}@${var.gcp_project_ids.security}.iam.gserviceaccount.com",
    "redox-processor@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "api-server-internal-writer@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "loadgenerator@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "realtime-processing-services@${var.gcp_project_ids.data_platform}.iam.gserviceaccount.com",
    "dagster-realtime-dags@${var.gcp_project_ids.data_platform}.iam.gserviceaccount.com",
    "dagster-analytics-pipelines@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "mlops-dags@${var.gcp_project_ids.ml}.iam.gserviceaccount.com",
    "realtime-event-model@${var.gcp_project_ids.data_platform}.iam.gserviceaccount.com",
    "realtime-event-model-shadow@${var.gcp_project_ids.data_platform}.iam.gserviceaccount.com",
    "notifier-scheduler@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
    "ml-services@${var.gcp_project_ids.ml}.iam.gserviceaccount.com",
    "dagster-sa@${var.gcp_project_ids.internal}.iam.gserviceaccount.com",
    "notion-daemon@${var.gcp_project_ids.web_api}.iam.gserviceaccount.com",
  ]

  # extract the unique domain names from the list above
  service_account_domains = tolist(toset([for email in local.service_account_writer_emails : split("@", email)[1]]))
}

resource "random_password" "service_account_password" {
  for_each = toset(local.service_account_writer_emails)

  length  = 16
  special = true
}

resource "auth0_user" "service_account_user" {
  for_each = toset(local.service_account_writer_emails)

  connection_name = "Username-Password-Authentication"
  name            = each.value
  email           = each.value
  password        = random_password.service_account_password[each.value].result
  email_verified  = true

  depends_on = [auth0_action.signup_allowlisted_email_domains]
}

resource "auth0_user_roles" "service_account_user" {
  for_each = toset(local.service_account_writer_emails)

  user_id = auth0_user.service_account_user[each.key].id
  roles   = [auth0_role.service_account_writer.id]
}
