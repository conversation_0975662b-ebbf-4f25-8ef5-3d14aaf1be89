locals {
  hmhn_feature_roles = {
    "hmhn_leadership" : {
      "name" : "HMHN Leadership",
      "ad_group" : "App_Apella_Leadership",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.highlights_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_management" : {
      "name" : "HMHN Management",
      "ad_group" : "App_Apella_Management",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_charge_nurse" : {
      "name" : "HMHN Charge Nurse",
      "ad_group" : "App_Apella_ChargeRN",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_anesthesiology" : {
      "name" : "HMHN Anesthesiology",
      "ad_group" : "App_Apella_Anesthesiology",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_surgeon" : {
      "name" : "HMHN Surgeon",
      "ad_group" : "App_Apella_Surgeon",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_infection_prevention" : {
      "name" : "HMHN Infection Prevention",
      "ad_group" : "App_Apella_InfectionPrevention",
      "permissions" : toset(concat(
        local.highlights_permissions,
      ))
    },
    "hmhn_highlights_only" : {
      "name" : "HMHN Highlights Only",
      "ad_group" : "App_Apella_HighlightsOnly",
      "permissions" : toset(concat(
        local.highlights_permissions,
      ))
    },
    "hmhn_live_only" : {
      "name" : "HMHN Live Only",
      "ad_group" : "App_Apella_LiveOnly",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
      ))
    },
    "hmhn_schedule_only" : {
      "name" : "HMHN Schedule Only",
      "ad_group" : "App_Apella_ScheduleOnly",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
      ))
    },
    "hmhn_insights_only" : {
      "name" : "HMHN Insights Only",
      "ad_group" : "App_Apella_InsightsOnly",
      "permissions" : toset(concat(
        local.insights_permissions,
      ))
    },
  }

  hmhn_site_specific_roles = { for k, v in local.hmhn_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.hmhn_site_specific_permission_map[k]
    ])
  } }
  hmhn_roles            = merge(local.hmhn_feature_roles, local.hmhn_site_specific_roles)
  hmhn_ad_group_to_role = { for k, v in auth0_role.hmhn_roles : local.hmhn_roles[k]["ad_group"] => v.id }
}

// ******************
// HMHN-wide Roles
// ******************
resource "auth0_role" "hmhn_roles" {
  for_each    = { for k, v in local.hmhn_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "hmhn_role_permissions" {
  for_each = { for k, v in local.hmhn_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.hmhn_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
