locals {
  // Medical University Of South Carolina Health permissions
  musc_sites = {
    "main_4" : {
      "name" : "Medical University of South Carolina",
      "slug" : "MUSC MAIN4",
      "ad_group" : "PLACEHOLDER_MUSC_4",
      "site_id" : "MUSC-MAIN4"
    },
  }
  musc_site_specific_permission_map = { for k, v in local.musc_sites : k => {
    description : "Read ${v["name"]} site",
    value : "site:read:${v["site_id"]}"
  } }
  musc_site_specific_permissions = { for k, v in local.musc_site_specific_permission_map : "site_read_${try(v["slug"], k)}" => v }
}
