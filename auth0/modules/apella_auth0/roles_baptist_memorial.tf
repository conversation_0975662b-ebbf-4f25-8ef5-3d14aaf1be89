locals {
  baptist_memorial_feature_roles = {
    "baptist_memorial_schedule_only" : {
      "name" : "Baptist Memorial Schedule Only",
      "ad_group" : "SCHEDULE_REPLACE_ME",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
      ))
    },
  }
  baptist_memorial_site_specific_roles = { for k, v in local.baptist_memorial_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.baptist_memorial_site_specific_permission_map[k]
    ])
  } }
  baptist_memorial_roles            = merge(local.baptist_memorial_feature_roles, local.baptist_memorial_site_specific_roles)
  baptist_memorial_ad_group_to_role = { for k, v in auth0_role.baptist_memorial_roles : local.baptist_memorial_roles[k]["ad_group"] => v.id }
}

// ******************
// Baptist Memorial-wide Roles
// ******************
resource "auth0_role" "baptist_memorial_roles" {
  for_each    = { for k, v in local.baptist_memorial_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "baptist_memorial_role_permissions" {
  for_each = { for k, v in local.baptist_memorial_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.baptist_memorial_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
