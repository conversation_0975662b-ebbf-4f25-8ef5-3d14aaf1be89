locals {
  hf_feature_roles = {
    health_first_management : {
      name : "Health First Management",
      ad_group : "Apella Management",
      permissions : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
        local.hf_site_permissions,
      ))
    },
    health_first_staff : {
      name : "Health First Staff",
      ad_group : "Apella Staff",
      permissions : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
        local.hf_site_permissions,
      ))
    },
    health_first_analyst : {
      name : "Health First Analyst",
      ad_group : "Apella Analyst",
      permissions : toset(concat(
        local.hf_site_permissions,
      ))
    },
    health_first_evs : {
      name : "Health First EVS",
      ad_group : "Apella EVS",
      permissions : toset(concat(
        local.hf_site_permissions,
      ))
    },
    health_first_case_duration : {
      name : "Health First Case Duration",
      ad_group : "Apella Scheduler",
      permissions : toset(concat(
        local.hf_site_permissions,
      ))
    },
    health_first_big_board : {
      name : "Health First Big Board",
      ad_group : "Apella Big Board",
      permissions : toset(concat(
        local.hf_site_permissions,
      ))
    },
  }
  hf_roles            = merge(local.hf_feature_roles)
  hf_ad_group_to_role = { for k, v in auth0_role.hf_roles : local.hf_roles[k]["ad_group"] => v.id }
}

// ******************
// HealthFirst-wide Roles
// ******************
resource "auth0_role" "hf_roles" {
  for_each    = { for k, v in local.hf_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "hf_role_permissions" {
  for_each = { for k, v in local.hf_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.hf_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
