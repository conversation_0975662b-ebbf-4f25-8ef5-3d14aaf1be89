resource "auth0_organization" "apella_internal" {
  display_name = "Apella Internal"
  name         = "apella"

  branding {
    colors   = {}
    logo_url = "https://assets.apella.io/logos/signature/dark-on-transparent.png"
  }

  metadata = {
    apella_org_id = "apella_internal_0"
  }
}

resource "auth0_organization_connection" "apella_internal_connections" {
  for_each = toset([
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ])
  organization_id            = auth0_organization.apella_internal.id
  connection_id              = each.value
  assign_membership_on_login = each.value == auth0_connection.google_workspace.id
  show_as_button             = true
}

resource "auth0_organization" "greys_anatomy" {
  display_name = "Grey's Anatomy"
  name         = "greys"

  branding {
    colors = {
      "page_background" = "#000000"
      "primary"         = "#ee0000"
    }
    logo_url = "https://sites.google.com/site/greysanatomypmp/_/rsrc/1487328684701/home/<USER>"
  }

  metadata = {
    apella_org_id = "greys_anatomy"
  }
}

resource "auth0_organization_connection" "greys_anatomy_connections" {
  for_each = toset([
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ])
  organization_id            = auth0_organization.greys_anatomy.id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "scrubs" {
  display_name = "Scrubs"
  name         = "scrubs"

  branding {
    colors = {
      "page_background" = "#000000"
      "primary"         = "#00c8d6"
    }
    logo_url = "https://upload.wikimedia.org/wikipedia/commons/thumb/a/ab/Scrubs_Vectorized_Logo.svg/1200px-Scrubs_Vectorized_Logo.svg.png"
  }

  metadata = {
    apella_org_id = "scrubs"
  }
}

resource "auth0_organization_connection" "scrubs_connections" {
  for_each = toset([
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ])
  organization_id            = auth0_organization.scrubs.id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "northbay" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "NorthBay"
  name         = "northbay"

  metadata = {
    apella_org_id = "north_bay"
  }
}

resource "auth0_organization_connection" "north_bay_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.northbay_saml[0].id,
  ] : [])
  organization_id            = auth0_organization.northbay[0].id
  connection_id              = each.value
  assign_membership_on_login = each.value == auth0_connection.northbay_saml[0].id
  show_as_button             = each.value == auth0_connection.northbay_saml[0].id || each.value == auth0_connection.database_connection.id
}

resource "auth0_organization_connection" "north_bay_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
  ] : [])
  organization_id            = auth0_organization.northbay[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "houston_methodist" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Houston Methodist"
  name         = "houston_methodist"


  metadata = {
    apella_org_id = "houston_methodist"
  }
}
resource "auth0_organization_connection" "houston_methodist_connections" {
  # Houston Methodist Staging should only allow Google Workspace connection, not HMH SAML.
  for_each = toset(var.environment == "prod" ? [auth0_connection.google_workspace.id,
    auth0_connection.database_connection.id,
  auth0_connection.houston_methodist_saml[0].id] : [])
  connection_id              = each.value
  organization_id            = auth0_organization.houston_methodist[0].id
  assign_membership_on_login = each.value == auth0_connection.houston_methodist_saml[0].id
  show_as_button             = each.value == auth0_connection.houston_methodist_saml[0].id || each.value == auth0_connection.database_connection.id
}


resource "auth0_organization" "health_first" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Health First"
  name         = "health_first"

  metadata = {
    apella_org_id = "health_first"
  }
}

resource "auth0_organization_connection" "health_first_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.google_workspace.id,
    auth0_connection.health_first_saml[0].id
  ] : [])
  connection_id              = each.value
  organization_id            = auth0_organization.health_first[0].id
  assign_membership_on_login = each.value == auth0_connection.health_first_saml[0].id
  show_as_button             = each.value == auth0_connection.health_first_saml[0].id
}


resource "auth0_organization" "tampa_general" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Tampa General Hospital"
  name         = "tampa_general"

  metadata = {
    apella_org_id = "tampa_general"
  }
}

resource "auth0_organization_connection" "tampa_general_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.tampa_general_saml[0].id
  ] : [])
  connection_id              = each.value
  organization_id            = auth0_organization.tampa_general[0].id
  assign_membership_on_login = each.value == auth0_connection.tampa_general_saml[0].id
  show_as_button             = each.value == auth0_connection.tampa_general_saml[0].id || each.value == auth0_connection.database_connection.id
}

resource "auth0_organization_connection" "tampa_general_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
  ] : [])
  organization_id            = auth0_organization.tampa_general[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "lifebridge" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Lifebridge Health"
  name         = "lifebridge"

  metadata = {
    apella_org_id = "lifebridge"
  }
}

resource "auth0_organization_connection" "lifebridge_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.lifebridge_saml[0].id
  ] : [])
  connection_id              = each.value
  organization_id            = auth0_organization.lifebridge[0].id
  assign_membership_on_login = each.value == auth0_connection.lifebridge_saml[0].id
  show_as_button             = each.value == auth0_connection.lifebridge_saml[0].id || each.value == auth0_connection.database_connection.id
}

resource "auth0_organization_connection" "lifebridge_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
  ] : [])
  organization_id            = auth0_organization.lifebridge[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "nyu" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "NYU Langone Health"
  name         = "nyu"

  metadata = {
    apella_org_id = "nyu"
  }
}

resource "auth0_organization_connection" "nyu_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.nyu_saml[0].id
  ] : [])
  connection_id              = each.value
  organization_id            = auth0_organization.nyu[0].id
  assign_membership_on_login = each.value == auth0_connection.nyu_saml[0].id
  show_as_button             = each.value == auth0_connection.nyu_saml[0].id || each.value == auth0_connection.database_connection.id
}

resource "auth0_organization_connection" "nyu_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
  ] : [])
  organization_id            = auth0_organization.nyu[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "hmhn" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Hackensack Meridian Health"
  name         = "hackensack_meridian"

  metadata = {
    apella_org_id = "hackensack_meridian"
  }
}

resource "auth0_organization_connection" "hmhn_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.hmhn_saml[0].id
  ] : [])
  organization_id            = auth0_organization.hmhn[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization_connection" "hmhn_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ] : [])
  organization_id            = auth0_organization.hmhn[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "baptist_memorial" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Baptist Memorial"
  name         = "baptist_memorial"

  metadata = {
    apella_org_id = "baptist_memorial"
  }
}

resource "auth0_organization_connection" "baptist_memorial_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.baptist_memorial_saml[0].id
  ] : [])
  organization_id            = auth0_organization.baptist_memorial[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization_connection" "baptist_memorial_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ] : [])
  organization_id            = auth0_organization.baptist_memorial[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization" "musc" {
  count        = var.environment == "prod" || var.environment == "staging" ? 1 : 0
  display_name = "Medical University of South Carolina"
  name         = "musc"

  metadata = {
    apella_org_id = "musc"
  }
}

resource "auth0_organization_connection" "musc_connections" {
  for_each = toset(var.environment == "prod" ? [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id,
    auth0_connection.musc_saml[0].id
  ] : [])
  organization_id            = auth0_organization.musc[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}

resource "auth0_organization_connection" "musc_staging_connections" {
  for_each = toset(var.environment == "staging" ? [
    auth0_connection.database_connection.id
  ] : [])
  organization_id            = auth0_organization.musc[0].id
  connection_id              = each.value
  assign_membership_on_login = false
}


module "apella_demo" {
  source = "../auth0_organization"

  display_name  = "Apella Demo"
  apella_org_id = "apella_demo"

  connection_ids = [
    auth0_connection.database_connection.id,
    auth0_connection.google_workspace.id
  ]
}
