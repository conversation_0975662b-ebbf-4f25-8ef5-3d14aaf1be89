locals {
  nyu_feature_roles = {
    "nyu_leadership" : {
      "name" : "NYU Leadership",
      "ad_group" : "0ba2a620-9cb8-440a-99e4-35de5fe51b8c",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.highlights_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.historical_video_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "nyu_management" : {
      "name" : "NYU Management",
      "ad_group" : "09601314-3ee1-460c-a02a-58cf627b8501",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.case_duration_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "nyu_charge_nurse" : {
      "name" : "NYU Charge Nurse",
      "ad_group" : "c4c6922a-17e7-431e-a5b5-6cb4fef8f2d9",
      "permissions" : toset(concat(
        local.insights_permissions,
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
      ))
    },
    "nyu_anesthesiology" : {
      "name" : "NYU Anesthesiology",
      "ad_group" : "b75867db-01e4-4322-9c3b-b8385b248e3e",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.staff_management_permissions,
        local.turnovers_permissions,
      ))
    },
    "nyu_surgeon" : {
      "name" : "NYU Surgeon",
      "ad_group" : "ee1b3b02-2e48-4384-89a3-76878bc3aec5",
      "permissions" : toset(concat(
        local.live_permissions,
        local.schedule_permissions,
        local.live_streaming_permissions,
        local.turnovers_permissions,
      ))
    },
    "nyu_infection_prevention" : {
      "name" : "NYU Infection Prevention",
      "ad_group" : "e54f773a-6ca6-4230-a191-6b459f9cea42",
      "permissions" : toset(concat(
        local.highlights_permissions,
      ))
    },
    "nyu_live_only" : {
      "name" : "NYU Live Only",
      "ad_group" : "c2d72d34-9868-4d3f-93f2-adcdbb384584",
      "permissions" : toset(concat(
        local.live_permissions,
        local.live_streaming_permissions,
      ))
    },
    "nyu_schedule_only" : {
      "name" : "NYU Schedule Only",
      "ad_group" : "97e1be1a-fd08-4988-8ab8-78641ec1e8fd",
      "permissions" : toset(concat(
        local.schedule_permissions,
        local.turnovers_permissions,
        local.case_planning_management_permissions,
      ))
    },
    "nyu_insights_only" : {
      "name" : "NYU Insights Only",
      "ad_group" : "0a9b6082-b4b0-4d34-8154-2dc28dd3402c",
      "permissions" : toset(concat(
        local.insights_permissions,
      ))
    },
  }
  nyu_site_specific_roles = { for k, v in local.nyu_sites : k => {
    name : "${v["name"]} Site",
    ad_group : v.ad_group,
    permissions : toset([
      local.nyu_site_specific_permission_map[k]
    ])
  } }
  nyu_roles            = merge(local.nyu_feature_roles, local.nyu_site_specific_roles)
  nyu_ad_group_to_role = { for k, v in auth0_role.nyu_roles : local.nyu_roles[k]["ad_group"] => v.id }
}

// ******************
// NYU-wide Roles
// ******************
resource "auth0_role" "nyu_roles" {
  for_each    = { for k, v in local.nyu_roles : k => v if var.environment == "prod" }
  name        = each.value.name
  description = each.value.name
}

resource "auth0_role_permissions" "nyu_role_permissions" {
  for_each = { for k, v in local.nyu_roles : k => v if var.environment == "prod" }
  role_id  = auth0_role.nyu_roles[each.key].id

  dynamic "permissions" {
    for_each = each.value.permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
  depends_on = [
    auth0_resource_server.api_server
  ]
}
