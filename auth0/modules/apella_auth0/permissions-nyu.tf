locals {
  // NYU permissions
  nyu_sites = {
    "nyu_kp" : {
      "name" : "NYU Kimmel Pavilion 5",
      "slug" : "kp5",
      "ad_group" : "ad3d1a10-dbd3-4335-aa90-ca1f8700ac64",
      "site_id" : "NYU-KP5"
    },
    "nyu_li" : {
      "name" : "NYU Long Island 4",
      "slug" : "li4",
      "ad_group" : "fa59063a-c1ac-4207-9ebc-aed642db3954",
      "site_id" : "NYU-LI4"
    },
    "nyu_kp5_cath" : {
      "name" : "NYU Kimmel Pavilion 5 - Cath",
      "slug" : "kp5-cath",
      "ad_group" : "6f00ddb6-3b13-496a-a273-b2e98819e80d",
      "site_id" : "NYU-KP5-CATH"
    },
    "nyu_kp4" : {
      "name" : "NYU Kimmel Pavilion 4",
      "slug" : "kp4",
      "ad_group" : "a9c60a94-6a6a-45f9-8b92-5490c419d429",
      "site_id" : "NYU-KP4"
    },
  }
  nyu_site_specific_permission_map = { for k, v in local.nyu_sites : k => {
    description : "Read ${v["name"]} site",
    value : "site:read:${v["site_id"]}"
  } }
  nyu_site_specific_permissions = { for k, v in local.nyu_site_specific_permission_map : "site_read_${try(v["slug"], k)}" => v }
}
