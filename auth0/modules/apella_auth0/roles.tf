locals {
  api_server = {
    url = {
      dev     = "https://api.dev.apella.io"
      staging = "https://api.staging.apella.io"
      prod    = "https://api.apella.io"
    }
  }
}

locals {
  apella_developer_permissions = [
    local.permissions.annotation_task_read_any, local.permissions.annotation_task_write_any,
    local.permissions.annotation_task_type_write_any,
    local.permissions.big_board_read_any, local.permissions.big_board_write_any,
    local.permissions.camera_read_any, local.permissions.camera_write_any,
    local.permissions.case_duration_read_any,
    local.permissions.available_times_email_any,
    local.permissions.case_read_any, local.permissions.case_write_any,
    local.permissions.cluster_read_any, local.permissions.cluster_write_any,
    local.permissions.event_read_any, local.permissions.event_write_any,
    local.permissions.event_type_write_any,
    local.permissions.feedback_read_any, local.permissions.feedback_write_if_assigned,
    local.permissions.highlight_read_any, local.permissions.highlight_write_any,
    local.permissions.mapping_read_any, local.permissions.mapping_write_any,
    local.permissions.measurement_period_read_any, local.permissions.measurement_period_write_any,
    local.permissions.media_asset_read_any, local.permissions.media_asset_write_any,
    local.permissions.org_read_any, local.permissions.org_write_any,
    local.permissions.room_read_any, local.permissions.room_write_any, local.permissions.room_write_configuration,
    local.permissions.site_read_any, local.permissions.site_write_any,
    local.permissions.user_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.object_read_any,
    local.permissions.dashboard_read_insights, local.permissions.dashboard_read_schedule,
    local.permissions.staffing_needs_read_any, local.permissions.staffing_needs_write_any,
    local.permissions.dashboard_edit_schedule,
    local.permissions.dashboard_read_optimized,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.block_read_any,
    local.permissions.block_write_any,
    local.permissions.contact_information_read_any,
    local.permissions.contact_information_write_any,
    local.permissions.notification_write_any,
    local.permissions.notification_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_note_plan_write_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_staff_plan_write_any,
    local.permissions.patient_read_all,
    local.permissions.dashboard_read_turnovers
  ]
}
resource "auth0_role" "apella_developer" {
  description = "Apella Developer"
  name        = "Apella Developer"
}

resource "auth0_role_permissions" "apella_developer" {
  role_id = auth0_role.apella_developer.id

  dynamic "permissions" {
    for_each = local.apella_developer_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  apella_developer_readonly_permissions = [
    local.permissions.annotation_task_read_any,
    local.permissions.big_board_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_duration_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any,
    local.permissions.feedback_read_any,
    local.permissions.highlight_read_any,
    local.permissions.mapping_read_any,
    local.permissions.measurement_period_read_any,
    local.permissions.media_asset_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any,
    local.permissions.site_read_any,
    local.permissions.user_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.object_read_any,
    local.permissions.dashboard_read_insights, local.permissions.dashboard_read_schedule,
    local.permissions.staffing_needs_read_any,
    local.permissions.dashboard_read_optimized,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.block_read_any,
    local.permissions.contact_information_read_any,
    local.permissions.notification_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.patient_read_all,
    local.permissions.dashboard_read_turnovers
  ]
}
resource "auth0_role" "apella_developer_readonly" {
  description = "Apella Developer without write permissions"
  name        = "Apella Developer Readonly"
}

resource "auth0_role_permissions" "apella_developer_readonly" {
  role_id = auth0_role.apella_developer_readonly.id

  dynamic "permissions" {
    for_each = local.apella_developer_readonly_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  apella_dashboard_user_permissions = [
    local.permissions.annotation_task_read_any,
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any,
    local.permissions.feedback_read_any,
    local.permissions.highlight_read_any,
    local.permissions.mapping_read_any,
    local.permissions.measurement_period_read_any,
    local.permissions.media_asset_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any,
    local.permissions.site_read_any,
    local.permissions.user_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.object_read_any,
    local.permissions.dashboard_read_insights, local.permissions.dashboard_read_schedule,
    local.permissions.staffing_needs_read_any, local.permissions.staffing_needs_write_any,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.contact_information_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_duration_read_any,
    local.permissions.dashboard_read_turnovers
  ]
}
resource "auth0_role" "apella_dashboard_user" {
  description = "Apella Dashboard User"
  name        = "Apella Dashboard User"
}

resource "auth0_role_permissions" "apella_dashboard_user" {
  role_id = auth0_role.apella_dashboard_user.id

  dynamic "permissions" {
    for_each = local.apella_dashboard_user_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  customer_dashboard_user_permissions = [
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any,
    local.permissions.feedback_write_if_assigned,
    local.permissions.highlight_read_if_assigned,
    local.permissions.measurement_period_read_any,
    local.permissions.media_asset_read_if_assigned,
    local.permissions.org_read_any,
    local.permissions.room_read_any,
    local.permissions.site_read_any,
    local.permissions.media_asset_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.object_read_any,
    local.permissions.dashboard_read_insights, local.permissions.dashboard_read_schedule,
    local.permissions.staffing_needs_read_any, local.permissions.staffing_needs_write_any,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.dashboard_read_turnovers
  ]
}
resource "auth0_role" "customer_dashboard_user" {
  description = "Role to view Dashboard webapp (in an organization)"
  name        = "Customer Dashboard User"
}

resource "auth0_role_permissions" "customer_dashboard_user" {
  role_id = auth0_role.customer_dashboard_user.id

  dynamic "permissions" {
    for_each = local.customer_dashboard_user_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  service_account_writer_permissions = [
    local.permissions.annotation_task_read_any, local.permissions.annotation_task_write_any,
    local.permissions.annotation_task_type_write_any,
    local.permissions.block_read_any, local.permissions.block_write_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any, local.permissions.case_write_any,
    local.permissions.cluster_read_any, local.permissions.cluster_write_any,
    local.permissions.event_read_any, local.permissions.event_write_any,
    local.permissions.event_type_write_any,
    local.permissions.highlight_read_any, local.permissions.highlight_write_any,
    local.permissions.mapping_read_any, local.permissions.mapping_write_any,
    local.permissions.media_asset_read_any, local.permissions.media_asset_write_any,
    local.permissions.object_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any,
    local.permissions.site_read_any,
    local.permissions.site_write_launch,
    local.permissions.user_read_any,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.measurement_period_read_any, local.permissions.measurement_period_write_any,
    local.permissions.contact_information_read_any,
    local.permissions.contact_information_write_any,
    local.permissions.notification_write_any,
    local.permissions.notification_read_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_note_plan_write_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_staff_plan_write_any,
  ]
}
resource "auth0_role" "service_account_writer" {
  description = "Role for writer service accounts"
  name        = "Service Account: Writer"
}

resource "auth0_role_permissions" "service_account_writer" {
  role_id = auth0_role.service_account_writer.id

  dynamic "permissions" {
    for_each = local.service_account_writer_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  apella_labeler_permissions = [
    local.permissions.annotation_task_read_any, local.permissions.annotation_task_write_any,
    local.permissions.big_board_read_any,
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any, local.permissions.event_write_any,
    local.permissions.feedback_read_any,
    local.permissions.highlight_read_any, local.permissions.highlight_write_any,
    local.permissions.measurement_period_read_any,
    local.permissions.media_asset_read_any,
    local.permissions.object_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any, local.permissions.room_write_any, local.permissions.room_write_configuration,
    local.permissions.site_read_any,
    local.permissions.user_read_any,
    local.permissions.dashboard_read_optimized,
    local.permissions.dashboard_read_terminal_cleans,
  ]
}
resource "auth0_role" "apella_labeler" {
  description = "Apella Labeler"
  name        = "Apella Labeler"
}

resource "auth0_role_permissions" "apella_labeler" {
  role_id = auth0_role.apella_labeler.id

  dynamic "permissions" {
    for_each = local.apella_labeler_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  apella_label_reviewer_permissions = [
    local.permissions.annotation_task_read_any, local.permissions.annotation_task_write_any,
    local.permissions.annotation_task_type_write_any,
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any, local.permissions.event_write_any,
    local.permissions.event_type_write_any,
    local.permissions.feedback_read_any,
    local.permissions.highlight_read_any, local.permissions.highlight_write_any,
    local.permissions.measurement_period_read_any,
    local.permissions.media_asset_read_any,
    local.permissions.object_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any, local.permissions.room_write_any, local.permissions.room_write_configuration,
    local.permissions.site_read_any,
    local.permissions.user_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.dashboard_read_terminal_cleans,
  ]
}
resource "auth0_role" "apella_label_reviewer" {
  description = "Apella Label Reviewer"
  name        = "Apella Label Reviewer"
}

resource "auth0_role_permissions" "apella_label_reviewer" {
  role_id = auth0_role.apella_label_reviewer.id

  dynamic "permissions" {
    for_each = local.apella_label_reviewer_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}


locals {
  camera_technician_permissions = [
    local.permissions.site_read_any,
    local.permissions.block_read_any,
    local.permissions.room_read_any,
    local.permissions.camera_read_any,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.event_read_any,
    local.permissions.object_read_any
  ]
}

resource "auth0_role" "camera_technician" {
  description = "This is a role for people who are setting up and adjusting the cameras to be able to view live while they're adjusting"
  name        = "Camera Technician"
}

resource "auth0_role_permissions" "camera_technician" {
  role_id = auth0_role.camera_technician.id

  dynamic "permissions" {
    for_each = local.camera_technician_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  apella_csm_permissions = [
    local.permissions.annotation_task_read_any, local.permissions.annotation_task_write_any,
    local.permissions.big_board_read_any, local.permissions.big_board_write_any,
    local.permissions.block_read_any,
    local.permissions.camera_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any, local.permissions.event_write_any,
    local.permissions.event_type_write_any,
    local.permissions.feedback_read_any,
    local.permissions.highlight_read_any, local.permissions.highlight_write_any,
    local.permissions.media_asset_read_any,
    local.permissions.object_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any, local.permissions.room_write_any, local.permissions.room_write_configuration,
    local.permissions.site_read_any,
    local.permissions.user_read_any,
    local.permissions.dashboard_read_optimized,
    local.permissions.dashboard_read_terminal_cleans,
    local.permissions.dashboard_edit_schedule,
    local.permissions.measurement_period_read_any, local.permissions.measurement_period_write_any,
    local.permissions.contact_information_read_any,
    local.permissions.contact_information_write_any,
    local.permissions.case_note_plan_read_any,
    local.permissions.case_note_plan_write_any,
    local.permissions.case_staff_plan_read_any,
    local.permissions.case_staff_plan_write_any,
    local.permissions.dashboard_read_turnovers
  ]
}

resource "auth0_role" "apella_csm" {
  description = "Apella Customer Success Manager"
  name        = "Apella CSM"
}

resource "auth0_role_permissions" "apella_csm" {
  role_id = auth0_role.apella_csm.id

  dynamic "permissions" {
    for_each = local.apella_csm_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {
  data_integrator_permissions = [
    local.permissions.dashboard_read_schedule,
    local.permissions.site_read_any,
    local.permissions.case_read_any,
    local.permissions.room_read_any,
    local.permissions.block_read_any,
  ]
}

resource "auth0_role" "data_integrator" {
  description = "Role for those validating the EHR data is flowing to the schedule view properly"
  name        = "Data Integrator"
}

resource "auth0_role_permissions" "data_integrator" {
  role_id = auth0_role.data_integrator.id

  dynamic "permissions" {
    for_each = local.data_integrator_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}

locals {

  notion_sync_permissions = [
    local.permissions.site_read_any,
    local.permissions.site_write_launch,
  ]
}

resource "auth0_role" "notion_sync" {
  description = "Role for the Notion Sync service account"
  name        = "Notion Sync"
}

resource "auth0_role_permissions" "notion_sync" {
  role_id = auth0_role.notion_sync.id

  dynamic "permissions" {
    for_each = local.notion_sync_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}
