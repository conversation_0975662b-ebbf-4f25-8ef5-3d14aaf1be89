locals {
  // Tampa General permissions
  tg_sites = {
    "tgh_main_02" : {
      "name" : "TGH MAIN 02",
      "slug" : "main_02",
      "ad_group" : "Apella_MainOR",
      "site_id" : "TGH-MAIN02"
    },
    "tgh_cvtor_03" : {
      "name" : "TGH CVTOR 03",
      "slug" : "cvtor_03",
      "ad_group" : "Apella_CVTOR",
      "site_id" : "TGH-CVTOR03"
    },
  }
  tg_site_specific_permission_map = { for k, v in local.tg_sites : k => {
    description : "Read ${v["name"]} site",
    value : "site:read:${v["site_id"]}"
  } }
  tg_site_specific_permissions = { for k, v in local.tg_site_specific_permission_map : "site_read_${try(v["slug"], k)}" => v }
}
