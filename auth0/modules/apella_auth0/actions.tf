locals {
  # Adding apella.technology for testing with the big board logins
  allowlisted_email_domains = concat(local.service_account_domains, ["apella.technology"])
}

resource "auth0_action" "signup_allowlisted_email_domains" {
  code = templatefile("${path.module}/actions/signup_only_allowlisted_email_domains.js", {
    client_domain_allowlist = local.allowlisted_email_domains
  })
  name    = "Signup: Only Allowlisted Email Domains"
  runtime = "node18"
  deploy  = true

  supported_triggers {
    id      = "pre-user-registration"
    version = "v2"
  }
}

resource "auth0_action" "add_apella_custom_claims" {
  code    = file("${path.module}/actions/add_apella_custom_claims.js")
  name    = "Add apella custom claims"
  runtime = "node18"
  deploy  = true

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
}

locals {
  env_scoped_connection_org_roles = {
    dev     = {}
    staging = {}
    prod = var.environment == "prod" ? tomap({
      "${auth0_connection.northbay_saml[0].id}" : {
        org_id                = auth0_organization.northbay[0].id
        user_property_to_role = local.nb_ad_group_to_role
      },
      "${auth0_connection.health_first_saml[0].id}" : {
        org_id                = auth0_organization.health_first[0].id
        user_property_to_role = local.hf_ad_group_to_role
      },
      "${auth0_connection.houston_methodist_saml[0].id}" : {
        org_id                = auth0_organization.houston_methodist[0].id
        user_property_to_role = local.hm_ad_group_to_role
      },
      "${auth0_connection.tampa_general_saml[0].id}" : {
        org_id                = auth0_organization.tampa_general[0].id
        user_property_to_role = local.tg_ad_group_to_role
      },
      "${auth0_connection.nyu_saml[0].id}" : {
        org_id                = auth0_organization.nyu[0].id
        user_property_to_role = local.nyu_ad_group_to_role
      },
      "${auth0_connection.lifebridge_saml[0].id}" : {
        org_id                = auth0_organization.lifebridge[0].id
        user_property_to_role = local.lifebridge_ad_group_to_role
      },
      "${auth0_connection.hmhn_saml[0].id}" : {
        org_id                = auth0_organization.hmhn[0].id
        user_property_to_role = local.hmhn_ad_group_to_role
      },
      "${auth0_connection.baptist_memorial_saml[0].id}" : {
        org_id                = auth0_organization.baptist_memorial[0].id
        user_property_to_role = local.baptist_memorial_ad_group_to_role
      },
      "${auth0_connection.musc_saml[0].id}" : {
        org_id                = auth0_organization.musc[0].id
        user_property_to_role = local.musc_ad_group_to_role
      },
    }) : {}
  }

  org_connection_roles = {
    connection_map : merge(
      local.env_scoped_connection_org_roles[var.environment],
      {
        "${auth0_connection.google_workspace.id}" : {
          org_id = auth0_organization.apella_internal.id,
          user_property_to_role = {
            "eng" : auth0_role.apella_developer.id,
            "staff" : auth0_role.apella_dashboard_user.id
          }
        },
    })
  }
}
resource "auth0_action" "assign_org_roles_to_users" {
  code    = templatefile("${path.module}/actions/assign_org_roles_to_users.js", local.org_connection_roles)
  name    = "Assign organization roles to users"
  runtime = "node18"
  deploy  = true

  supported_triggers {
    id      = "post-login"
    version = "v3"
  }
  dependencies {
    name    = "auth0"
    version = "2.40.0"
  }
  secrets {
    name  = "clientId"
    value = auth0_client.auth0_actions_management_api_client.client_id
  }
  secrets {
    name  = "clientSecret"
    value = data.auth0_client.auth0_actions_management_api_client.client_secret
  }
  secrets {
    name  = "domain"
    value = "${auth0_tenant.apella_tenant.id}.us.auth0.com"
  }
}

locals {
  post_login_actions = [
    auth0_action.add_apella_custom_claims,
    auth0_action.assign_org_roles_to_users
  ]
}
resource "auth0_trigger_actions" "login_flow" {
  trigger = "post-login"
  dynamic "actions" {
    for_each = local.post_login_actions
    content {
      id           = actions.value.id
      display_name = actions.value.name
    }
  }
}

resource "auth0_trigger_actions" "signup_flow" {
  trigger = "pre-user-registration"

  actions {
    id           = auth0_action.signup_allowlisted_email_domains.id
    display_name = auth0_action.signup_allowlisted_email_domains.name
  }
}
