data "cloudflare_zones" "default" {
  filter {
    name = "apella.io"
  }
}

resource "auth0_custom_domain" "custom_domain" {
  count  = var.custom_domain == null ? 0 : 1
  domain = var.custom_domain
  type   = "auth0_managed_certs"
}

resource "cloudflare_record" "auth_dns_record" {
  count   = var.custom_domain == null ? 0 : 1
  zone_id = lookup(data.cloudflare_zones.default.zones[0], "id")
  name    = replace(var.custom_domain, ".apella.io", "")
  value   = auth0_custom_domain.custom_domain[0].verification[0].methods[0].record
  type    = "CNAME"
  ttl     = 1
  proxied = false
}

resource "auth0_custom_domain_verification" "custom_domain_verification" {
  count            = var.custom_domain == null ? 0 : 1
  custom_domain_id = auth0_custom_domain.custom_domain[0].id
  depends_on       = [cloudflare_record.auth_dns_record]
}
