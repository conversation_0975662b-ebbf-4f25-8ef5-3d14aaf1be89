variable "auth0_domain" {
  description = "Auth0 Domain"
  type        = string
}

variable "auth0_client_id" {
  description = "Auth0 Client ID"
  type        = string
  sensitive   = true
}

variable "auth0_client_secret" {
  description = "Auth0 Client Secret"
  type        = string
  sensitive   = true
}

variable "cloudflare_configuration" {
  type = object({
    api_token  = string
    account_id = string
  })
  description = "Cloudflare Provider Configuration"
  sensitive   = true
}

variable "environment" {
  type = string
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of 'dev', 'staging', or 'prod'."
  }
}

variable "gcp_project_ids" {
  type = object({
    web_api       = string
    data_platform = string
    media_asset   = string
    ml            = string
    ehr           = string
    edge          = string
    security      = string
    internal      = string
  })
  description = "GCP Project IDs for different projects"
}

variable "google_workspace_connection" {
  type = object({
    client_id     = string
    client_secret = string
  })
  description = "Google Workspace Auth0 credentials"
  sensitive   = true
}

variable "custom_domain" {
  type        = string
  description = "CNAME domain for auth0 custom domain"
  default     = null
}

variable "gcp_sa" {
  type = object({
    dataflow = string
  })
  description = "GCP service account emails"
}

variable "saml_certificates" {
  type = object({
    apella_internal   = string
    houston_methodist = string
    health_first      = string
    northbay          = string
    tampa_general     = string
    nyu               = string
    lifebridge        = string
    hmhn              = string
    baptist_memorial  = string
    musc              = string
  })
  description = "SAML public key certificates"
  sensitive   = true
  default = {
    apella_internal   = ""
    houston_methodist = ""
    health_first      = ""
    northbay          = ""
    tampa_general     = ""
    nyu               = ""
    lifebridge        = ""
    hmhn              = ""
    baptist_memorial  = ""
    musc              = ""
  }
}
