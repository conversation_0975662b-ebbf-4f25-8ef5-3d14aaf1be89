resource "auth0_resource_server" "api_server" {
  allow_offline_access                            = true
  enforce_policies                                = true
  identifier                                      = local.api_server.url[var.environment]
  name                                            = "API Server"
  signing_alg                                     = "RS256"
  skip_consent_for_verifiable_first_party_clients = true
  token_dialect                                   = "access_token_authz"
  # These 5 min tokens are intentional.  It is used to minimize the exposure of a token in our
  # front-end web applications. It does make our CLI a bit annoying to use because the auth
  # expires after 5 mins, but we still don't want to change this setting. The right solution is
  # to implement a full OAuth flow with refresh tokens in the Apella CLI.
  # see: https://apella-workspace.slack.com/archives/C013XMYRGH1/p1727395638474979
  token_lifetime         = 300
  token_lifetime_for_web = 300
}

resource "auth0_resource_server_scopes" "api_server" {
  resource_server_identifier = auth0_resource_server.api_server.identifier

  dynamic "scopes" {
    for_each = merge(local.permissions, local.environment_scoped_permissions[var.environment])
    content {
      name        = scopes.value.value
      description = scopes.value.description
    }
  }
}
