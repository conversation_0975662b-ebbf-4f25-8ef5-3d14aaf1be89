locals {
  apella_internal_site_scoped_permissions = [
    local.permissions.block_read_any,
    local.permissions.case_read_any,
    local.permissions.event_read_any,
    local.permissions.org_read_any,
    local.permissions.room_read_any,
    local.permissions.camera_read_any,
    local.permissions.site_read_palo_alto_1,
    local.permissions.live_stream_read_any,
    local.permissions.dashboard_read_live,
    local.permissions.dashboard_read_live_from_schedule,
    local.permissions.dashboard_read_schedule,
    local.permissions.object_read_any,
    local.permissions.staffing_needs_write_any,
    local.permissions.staffing_needs_read_any,
    local.permissions.dashboard_read_turnovers
  ]
}
resource "auth0_role" "apella_internal_site_scoped_role" {
  description = "Apella Internal / Palo Alto 1 site-scoped role"
  name        = "Apella Internal / Palo Alto 1 only"
}

resource "auth0_role_permissions" "apella_internal_site_scoped_role_permissions" {
  role_id = auth0_role.apella_internal_site_scoped_role.id

  dynamic "permissions" {
    for_each = local.apella_internal_site_scoped_permissions
    content {
      name                       = permissions.value.value
      resource_server_identifier = local.api_server.url[var.environment]
    }
  }
}
