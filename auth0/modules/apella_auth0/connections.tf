resource "auth0_connection" "database_connection" {
  name     = "Username-Password-Authentication"
  strategy = "auth0"
  options {
    disable_signup         = true
    password_policy        = "good"
    brute_force_protection = true

    password_history {
      enable = false
      # For some reason, the API requires the size to be a non-zero integer, even if it's not enabled.
      size = 1
    }
  }
}

# This connection is not used directly in any application.
# However, it is useful for testing SAML connections for new clients.
# If we find it's not helpful in the future, we can delete.
resource "auth0_connection" "google_saml" {
  count                = var.environment == "dev" ? 1 : 0
  strategy             = "samlp"
  display_name         = "Apella Google SAML"
  is_domain_connection = false
  name                 = "apella-google-workspace-saml"
  options {
    signing_cert      = var.saml_certificates.apella_internal
    sign_in_endpoint  = "https://accounts.google.com/o/saml2/idp?idpid=C01vbpfw2"
    sign_out_endpoint = "https://accounts.google.com/o/saml2/idp?idpid=C01vbpfw2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "email" : "email",
      "group" : "organization",
      "groups" : "organizations",
      "user_id" : "email",
      "given_name" : "first_name",
      "family_name" : "last_name",
      # We map the email_verified boolean to a truthy claim.
      "email_verified" : "email"
    })
    domain_aliases = [
      "apella.io",
    ]
    sign_saml_request        = true
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    user_id_attribute        = "email"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection" "houston_methodist_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-houston-methodist"
  display_name         = "Houston Methodist"
  strategy             = "samlp"
  is_domain_connection = false
  realms = [
    "apella-saml-houston-methodist",
  ]

  options {
    signing_cert      = var.saml_certificates.houston_methodist
    sign_in_endpoint  = "https://login.microsoftonline.com/7218dbf5-3286-42dc-9b7e-c61ab35f8f3f/saml2"
    sign_out_endpoint = "https://login.microsoftonline.com/7218dbf5-3286-42dc-9b7e-c61ab35f8f3f/saml2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/identity/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups",
      "user_id" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
      "given_name" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname",
      "family_name" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname",
      # We map the email_verified boolean to a truthy claim.
      "email_verified" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    })
    sign_saml_request        = true
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    user_id_attribute        = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
    domain_aliases           = ["houstonmethodist.org"]
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "houston_methodist_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.houston_methodist_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "google_workspace" {
  name                 = "apella-google-workspace"
  display_name         = "Apella Google Workspace"
  is_domain_connection = false
  strategy             = "google-apps"

  options {
    client_id     = var.google_workspace_connection.client_id
    client_secret = var.google_workspace_connection.client_secret
    domain        = "apella.io"
    domain_aliases = [
      "apella.io",
    ]
    tenant_domain            = "apella.io"
    api_enable_users         = true
    scopes                   = ["ext_groups"]
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "google_workspace_clients" {
  for_each = toset([
    auth0_client.apella_cli.id,
    auth0_client.apella_dashboard.id,
    auth0_client.apella_internal_tools.id,
    auth0_client.apella_boards.id,
  ])

  connection_id = auth0_connection.google_workspace.id
  client_id     = each.value

}

resource "auth0_connection" "health_first_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-health-first"
  display_name         = "Health First"
  strategy             = "samlp"
  is_domain_connection = false
  realms = [
    "apella-saml-health-first",
  ]

  options {
    metadata_url             = "https://login.microsoftonline.com/6a78dbd7-47d1-4d22-9380-e63123eca039/federationmetadata/2007-06/federationmetadata.xml?appid=1bef58ea-4ecc-47e5-a43d-e14b5db8c708"
    signing_cert             = base64encode(var.saml_certificates.health_first)
    sign_in_endpoint         = "https://login.microsoftonline.com/6a78dbd7-47d1-4d22-9380-e63123eca039/saml2"
    sign_out_endpoint        = "https://login.microsoftonline.com/6a78dbd7-47d1-4d22-9380-e63123eca039/saml2"
    protocol_binding         = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["hf.org"]
    tenant_domain            = "hf.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "health_first_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.health_first_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "hmhn_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-hmhn"
  display_name         = "Hackensack Meridian Health"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = "https://login.microsoftonline.com/0cb01843-6c50-47b8-a1df-42b5cd8d8f81/federationmetadata/2007-06/federationmetadata.xml?appid=0f6673b5-8f1d-439a-af9b-086e818f4a9a"
    signing_cert      = base64encode(var.saml_certificates.hmhn)
    sign_in_endpoint  = "https://login.microsoftonline.com/0cb01843-6c50-47b8-a1df-42b5cd8d8f81/saml2"
    sign_out_endpoint = "https://login.microsoftonline.com/0cb01843-6c50-47b8-a1df-42b5cd8d8f81/saml2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/2012/01/devicecontext/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/claims/EmailAddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["hmhn.org"]
    tenant_domain            = "hmhn.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "hackensack_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.hmhn_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "baptist_memorial_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-baptist-memorial"
  display_name         = "Baptist Memorial Health"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = ""
    signing_cert      = base64encode(var.saml_certificates.baptist_memorial)
    sign_in_endpoint  = "https://login.microsoftonline.com/2059208f-ff28-4b47-971e-f40dac55a264/saml2"
    sign_out_endpoint = "https://login.microsoftonline.com/2059208f-ff28-4b47-971e-f40dac55a264/saml2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/identity/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["bmhcc.org"]
    tenant_domain            = "bmhcc.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "baptist_memorial_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.baptist_memorial_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "lifebridge_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-lifebridge"
  display_name         = "Lifebridge Health"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = ""
    signing_cert      = base64encode(var.saml_certificates.lifebridge)
    sign_in_endpoint  = "https://adfs.lifebridgehealth.org/adfs/ls/"
    sign_out_endpoint = "https://adfs.lifebridgehealth.org/adfs/ls/"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/2012/01/devicecontext/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/claims/EmailAddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups",
      # We map the email_verified boolean to a truthy claim.
      "email_verified" : "http://schemas.xmlsoap.org/claims/EmailAddress"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["lifebridgehealth.org"]
    tenant_domain            = "lifebridgehealth.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "lifebridge_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.lifebridge_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "nyu_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-nyu"
  display_name         = "NYU"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = ""
    signing_cert      = base64encode(var.saml_certificates.nyu)
    sign_in_endpoint  = "https://login.microsoftonline.com/d172088c-333b-4013-9a39-262c9d371245/saml2"
    sign_out_endpoint = "https://login.microsoftonline.com/d172088c-333b-4013-9a39-262c9d371245/saml2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/identity/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups",
      # We map the email_verified boolean to a truthy claim.
      "email_verified" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["nyulangone.org", "nyumc.org"]
    tenant_domain            = "nyulangone.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "nyu_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.nyu_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "tampa_general_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-tampa-general"
  display_name         = "Tampa General"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = "https://auth.tgh.org/saml20/metadata/805c0674-47e0-410c-aad6-b0bd96e855dc"
    signing_cert      = base64encode(var.saml_certificates.tampa_general)
    sign_in_endpoint  = "https://auth.tgh.org/saml20/idp/sso"
    sign_out_endpoint = "https://auth.tgh.org/saml20/idp/slo"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/identity/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups",
      # We map the email_verified boolean to a truthy claim.
      "email_verified" : "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    user_id_attribute        = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
    domain_aliases           = ["tgh.org"]
    tenant_domain            = "tgh.org"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection" "musc_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-musc"
  display_name         = "Medical University of South Carolina"
  strategy             = "samlp"
  is_domain_connection = false

  options {
    metadata_url      = "https://login.microsoftonline.com/5c4a59c9-6d6b-4de0-88c0-53acd566bf83/federationmetadata/2007-06/federationmetadata.xml?appid=91d5227f-8ba2-47e5-9848-46b9b63b392d"
    signing_cert      = base64encode(var.saml_certificates.musc)
    sign_in_endpoint  = "https://login.microsoftonline.com/5c4a59c9-6d6b-4de0-88c0-53acd566bf83/saml2"
    sign_out_endpoint = "https://login.microsoftonline.com/5c4a59c9-6d6b-4de0-88c0-53acd566bf83/saml2"
    protocol_binding  = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    fields_map = jsonencode({
      "name" : "http://schemas.microsoft.com/2012/01/devicecontext/claims/displayname",
      "email" : "http://schemas.xmlsoap.org/claims/EmailAddress",
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
    })
    sign_saml_request        = false
    signature_algorithm      = "rsa-sha256"
    digest_algorithm         = "sha256"
    domain_aliases           = ["musc.edu"]
    tenant_domain            = "musc.edu"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "musc_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.musc_saml[0].id
  client_id     = each.value
}

resource "auth0_connection_client" "tampa_general_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.tampa_general_saml[0].id
  client_id     = each.value
}

resource "auth0_connection" "northbay_saml" {
  count                = var.environment == "prod" ? 1 : 0
  name                 = "apella-saml-northbay"
  display_name         = "NorthBay"
  strategy             = "samlp"
  is_domain_connection = false
  realms = [
    "apella-saml-northbay",
  ]

  options {
    metadata_url        = "https://adfs.northbay.org/FederationMetadata/2007-06/FederationMetadata.xml"
    signing_cert        = base64encode(var.saml_certificates.northbay)
    sign_in_endpoint    = "https://adfs.northbay.org/adfs/ls/"
    disable_sign_out    = false
    sign_out_endpoint   = "https://adfs.northbay.org/adfs/ls/"
    protocol_binding    = "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
    sign_saml_request   = true
    signature_algorithm = "rsa-sha256"
    digest_algorithm    = "sha256"
    domain_aliases = [
      "northbay.org"
    ]
    tenant_domain = "northbay.org"
    fields_map = jsonencode({
      "groups" : "http://schemas.microsoft.com/ws/2008/06/identity/claims/role",
      "email_verified" : "email"
    })
    user_id_attribute        = "email"
    set_user_root_attributes = "on_each_login"
  }
}

resource "auth0_connection_client" "northbay_clients" {
  for_each = toset(var.environment == "prod" ? [
    auth0_client.apella_dashboard.id,
    auth0_client.apella_boards.id,
  ] : [])

  connection_id = auth0_connection.northbay_saml[0].id
  client_id     = each.value
}

# We must keep this google social connection to avoid deleting the Apella employee user accounts
# which have "google-oauth2" as their primary identity.
# Do not expose this connection to any clients/applications or Organizations. No Apella users
# should be logging in with generic google social connection.
resource "auth0_connection" "google" {
  name                 = "google-oauth2"
  is_domain_connection = false
  strategy             = "google-oauth2"
  options {
    client_id     = var.google_workspace_connection.client_id
    client_secret = var.google_workspace_connection.client_secret
    scopes        = ["email", "profile"]
  }
}

