# Auth0 Organization Module

This module is intended to be used in the apella_auth0 module if the organization should
exist in all environments.  Or it should be used in the environment-specific module if that
organization should only exist in one environment.

This module currently needs a lot of development to move more organizations to it.  It doesn't
currently support customization for orgs like:
- Roles
- Permissions
- Actions
- Branding

