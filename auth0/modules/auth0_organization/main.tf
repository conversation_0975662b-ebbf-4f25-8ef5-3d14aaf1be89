
resource "auth0_organization" "organization" {
  display_name = var.display_name
  name         = var.apella_org_id

  metadata = {
    apella_org_id = var.apella_org_id
  }
}

resource "auth0_organization_connection" "connections" {
  for_each                   = toset(var.connection_ids)
  organization_id            = auth0_organization.organization.id
  connection_id              = each.value
  assign_membership_on_login = false
}