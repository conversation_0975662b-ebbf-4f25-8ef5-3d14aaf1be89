variable "auth0_domain" {
  description = "Auth0 Domain"
  type        = string
}

variable "auth0_client_id" {
  description = "Auth0 Client ID"
  type        = string
  sensitive   = true
}

variable "auth0_client_secret" {
  description = "Auth0 Client Secret"
  type        = string
  sensitive   = true
}


variable "cloudflare_api_token" {
  type        = string
  description = "Cloudflare Provider Configuration API Token"
  sensitive   = true
}

variable "cloudflare_account_id" {
  type        = string
  description = "Cloudflare Provider Configuration Account ID"
  sensitive   = true
}

variable "google_workspace_connection_client_secret" {
  type        = string
  description = "GCP Credential OAuth Client Secret. Stored in Apella Auth0 Connector project."
  sensitive   = true
}

variable "google_workspace_connection_client_id" {
  type        = string
  description = "GCP Credential OAuth Client ID. Stored in Apella Auth0 Connector project."
  sensitive   = true
}
