terraform {
  cloud {
    hostname     = "app.terraform.io"
    organization = "apella"
    workspaces {
      name = "auth0-staging"
    }
  }
  required_version = ">= 1.7.5"
}

data "terraform_remote_state" "nonprod_tf_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-nonprod"
    }
  }
}

data "terraform_remote_state" "prod_tf_project_factory" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-project-factory-prod"
    }
  }
}

data "terraform_remote_state" "nonprod_tf_security" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-security-nonprod"
    }
  }
}

data "terraform_remote_state" "staging_tf_data_platform" {
  backend = "remote"
  config = {
    organization = "apella"
    workspaces = {
      name = "gcp-data-platform-staging"
    }
  }
}

module "apella_auth0" {
  environment = "staging"
  source      = "../modules/apella_auth0"
  gcp_project_ids = {
    web_api       = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-web-api-project.project_id,
    data_platform = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-data-platform-project.project_id,
    media_asset   = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-media-asset-project.project_id,
    ml            = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-ml-project.project_id,
    ehr           = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-ehr-project.project_id,
    edge          = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-edge-project.project_id,
    security      = data.terraform_remote_state.prod_tf_project_factory.outputs.prod-security-project.project_id,
    internal      = data.terraform_remote_state.nonprod_tf_project_factory.outputs.staging-internal-project.project_id,
  }
  gcp_sa = {
    dataflow = data.terraform_remote_state.nonprod_tf_security.outputs.nonprod-dataflow-sa
  }
  auth0_client_id     = var.auth0_client_id
  auth0_client_secret = var.auth0_client_secret
  auth0_domain        = var.auth0_domain
  cloudflare_configuration = {
    api_token  = var.cloudflare_api_token
    account_id = var.cloudflare_account_id
  }
  custom_domain = "auth.staging.apella.io"
  google_workspace_connection = {
    client_id     = var.google_workspace_connection_client_id
    client_secret = var.google_workspace_connection_client_secret
  }
}
