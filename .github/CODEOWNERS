* @Apella-Technology/platform-services

## Github

github/team-ehr.tf                 @Apella-Technology/ehr
github/team-frontend.tf            @Apella-Technology/frontend
github/team-observations.tf        @Apella-Technology/observations
github/team-platform-services.tf   @Apella-Technology/platform-services
github/team-python.tf              @Apella-Technology/python
github/team-realtime.tf            @Apella-Technology/realtime
github/team-ds-ml.tf               @Apella-Technology/ds-ml
github/team-data-science.tf        @Apella-Technology/data-science
github/team-forecasting.tf         @Apella-Technology/forecasting
github/team-data-platform.tf       @Apella-Technology/data-platform
github/team-computer-vision.tf     @Apella-Technology/computer-vision


## Google Cloud

# EHR Interfaces
google-cloud/web-api/modules/interfaces/           @Apella-Technology/ehr
**/interfaces.tf                                   @Apella-Technology/ehr
google-cloud/web-api/redox-processor/              @Apella-Technology/ehr
**/redox-processor.tf                              @Apella-Technology/ehr
google-cloud/ehr/                                  @Apella-Technology/ehr

# Observations
google-cloud/web-api/modules/annotation-task-cloud-scheduler/ @Apella-Technology/observations
**/annotation-task-cloud-scheduler.tf                         @Apella-Technology/observations
google-cloud/web-api/modules/analytics-data-pipelines/        @Apella-Technology/observations
**/analytics-data-pipelines.tf                                @Apella-Technology/observations
google-cloud/web-api/modules/cubejs/                          @Apella-Technology/observations
**/cubejs.tf                                                  @Apella-Technology/observations

# Platform Services
**/action-runner.tf                                @Apella-Technology/platform-services
google-cloud/web-api/modules/api-server/           @Apella-Technology/platform-services
**/api-server.tf                                   @Apella-Technology/platform-services
**/lb-https-api.tf                                 @Apella-Technology/platform-services
google-cloud/web-api/modules/audit-log-sink/       @Apella-Technology/platform-services
**/audit.tf                                        @Apella-Technology/platform-services
**/cloudsql.tf                                     @Apella-Technology/platform-services
google-cloud/web-api/modules/exit-node/            @Apella-Technology/platform-services
**/exit-node.tf                                    @Apella-Technology/platform-services
**/iam.tf                                          @Apella-Technology/platform-services
google-cloud/web-api/modules/gcp-datadog/          @Apella-Technology/platform-services
**/datadog.tf                                      @Apella-Technology/platform-services
**/private-dns.tf                                  @Apella-Technology/platform-services
**/public-dns.tf                                   @Apella-Technology/platform-services
**/ssl-policy.tf                                   @Apella-Technology/platform-services

# Realtime
google-cloud/web-api/modules/notifications/        @Apella-Technology/realtime

# DSML
google-cloud/data-platform/modules/warehouse/      @Apella-Technology/ds-ml
google-cloud/data-platform/modules/decodable/      @Apella-Technology/ds-ml

## Incident.io
incident-io/ @juandiegocastrillon @rockapella

incident-io/team-clinical-data.tf           @Apella-Technology/ehr
incident-io/team-computer-vision.tf         @Apella-Technology/ds-ml
incident-io/team-forecasting.tf             @Apella-Technology/ds-ml
incident-io/team-data-platform.tf           @Apella-Technology/ds-ml
incident-io/team-field-eng.tf               @Apella-Technology/platform-services
incident-io/team-orca-orion.tf              @Apella-Technology/observations @Apella-Technology/orca
incident-io/team-platform-services.tf       @Apella-Technology/platform-services
incident-io/team-realtime.tf                @Apella-Technology/realtime
