## Summary

Replace this text with your PR summary.

> [!NOTE]
> In order for Linear to find this PR, it should either:
> * Use a branch name generated by Linear -or-
> * Have the associated Linear ticket number as a prefix in the PR title (e.g. `DATA-1234: Title of PR`)

## Type of change

- [ ] Chore (non-breaking change that doesn't impact functionality i.e. upgrading dependencies)
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)

## Important considerations

If any of these boxes are checked, a thorough explanation should be given in the Summary and code
reviewers should give extra scrutiny.

- [ ] This change affects **Authentication and/or Authorization**