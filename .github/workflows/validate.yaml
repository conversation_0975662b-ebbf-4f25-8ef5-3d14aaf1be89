name: "Validate Terraform"
on:
  push:
    branches: [main]
  pull_request:

env:
  # Tell Terraform to use a common directory for provider plugins instead of per-module directories.
  # This allows already downloaded plugins to be reused by other modules. Unfortunately, such a
  # thing apparently doesn't exist yet for the module cache
  # (https://github.com/hashicorp/terraform/issues/34634).
  TF_PLUGIN_CACHE_DIR: ${{ github.workspace }}/.terraform.d/plugin-cache

jobs:
  Changes:
    runs-on: ubuntu-latest
    outputs:
      changed_directories: ${{ steps.changed-files.outputs.all_changed_files }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: true

      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v46
        with:
          files_ignore: .github/**
          dir_names: true
          dir_names_max_depth: 1
          json: true
          escape_json: false

      - name: List all changed files
        run: echo '${{ steps.changed-files.outputs.all_changed_files }}'
  Validate-Terraform:
    runs-on: ubuntu-latest
    if: ${{ needs.Changes.outputs.changed_directories != '["."]' && needs.Changes.outputs.changed_directories != '[]' && needs.Changes.outputs.changed_directories != '' }}  # Without it, the strategy parser will fail if the changed_directories is empty.
    strategy:
      matrix:
        dir: ${{ fromJson(needs.Changes.outputs.changed_directories) }}
    needs:
      - Changes
    steps:
      - uses: actions/checkout@v4

      - name: Set up Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          cli_config_credentials_token: ${{ secrets.TF_API_TOKEN }}

      - name: Create Terraform provider plugin cache directory
        run: mkdir --parents $TF_PLUGIN_CACHE_DIR

      - name: Check formatting
        run: cd "${{ matrix.dir }}"; terraform fmt -check -recursive
        shell: bash

      - name: Find Terraform modules
        id: find-modules
        # What's going on in this unholy mess? Let's step through it:
        # 1. We're looking for files that end in .tf. This abomination assumes that any .tf file is in the root of a Terraform module.
        # 2. Those files are piped through `dirname`, which strips the file name from the path and gives us the module root.
        # 3. Those module roots are piped through `sort -u` which deduplicates them.
        #
        # Because the output ends up being multiline, we have to wrap it in the special syntax
        # outlined here:
        # https://docs.github.com/en/actions/using-workflows/workflow-commands-for-github-actions#multiline-strings
        run: |
          echo "modules<<EOF" >> "$GITHUB_OUTPUT"
          echo "$(find ${{ matrix.dir }} -name '*.tf' | xargs dirname | sort -u)" >> "$GITHUB_OUTPUT"
          echo "EOF" >> "$GITHUB_OUTPUT"
        shell: bash

      # Store the Terraform providers cache in Github. This is a bit more efficient than having
      # Terraform download each of the providers one at a time.
      - name: Cache Terraform providers
        uses: actions/cache@v4
        with:
          path: ${{ env.TF_PLUGIN_CACHE_DIR }}
          key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}

      - name: Validate Modules
        run: |
          while IFS= read -r module; do
            echo "Validating module: $module"
            test -f "${module}/.terraform.lock.hcl" || (echo "Failed to find ${module}/.terraform.lock.hcl" && exit 1)
            terraform -chdir="$module" init -backend=false -lockfile=readonly
            terraform -chdir="$module" validate
          done <<< "${{ steps.find-modules.outputs.modules }}"
        shell: bash
